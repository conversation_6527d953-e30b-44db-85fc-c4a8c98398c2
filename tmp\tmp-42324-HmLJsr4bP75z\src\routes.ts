
import { Router, Request, Response } from 'express';
import axios from 'axios';

const router = Router();
const BASE_URL = process.env.BASE_URL || 'http://localhost:8000';


// Add a new pet to the store.
router.post('/addPet', async (req: Request, res: Response) => {
  try {
    const { body } = req.body;
    
    // Build URL with path parameters
    let url = `${BASE_URL}/pet`;
    
    // Prepare request configuration
    const config: any = {
      method: 'POST',
      url,
      timeout: 30000
    };
    
    
    
    // Add request body
    if (body) {
      config.data = body;
      config.headers = { 
        ...config.headers, 
        'Content-Type': 'application/json' 
      };
    }
    
    // Make API request
    const response = await axios(config);
    
    res.json({
      success: true,
      data: response.data,
      status: response.status,
      headers: response.headers
    });
    
  } catch (error: any) {
    console.error('addPet error:', error.message);
    
    if (error.response) {
      // API error
      res.status(error.response.status || 500).json({
        success: false,
        error: 'API Error',
        message: error.response.data?.message || error.message,
        status: error.response.status,
        data: error.response.data
      });
    } else {
      // Network or other error
      res.status(500).json({
        success: false,
        error: 'Request Failed',
        message: error.message
      });
    }
  }
});


// Update an existing pet.
router.post('/updatePet', async (req: Request, res: Response) => {
  try {
    const { body } = req.body;
    
    // Build URL with path parameters
    let url = `${BASE_URL}/pet`;
    
    // Prepare request configuration
    const config: any = {
      method: 'PUT',
      url,
      timeout: 30000
    };
    
    
    
    // Add request body
    if (body) {
      config.data = body;
      config.headers = { 
        ...config.headers, 
        'Content-Type': 'application/json' 
      };
    }
    
    // Make API request
    const response = await axios(config);
    
    res.json({
      success: true,
      data: response.data,
      status: response.status,
      headers: response.headers
    });
    
  } catch (error: any) {
    console.error('updatePet error:', error.message);
    
    if (error.response) {
      // API error
      res.status(error.response.status || 500).json({
        success: false,
        error: 'API Error',
        message: error.response.data?.message || error.message,
        status: error.response.status,
        data: error.response.data
      });
    } else {
      // Network or other error
      res.status(500).json({
        success: false,
        error: 'Request Failed',
        message: error.message
      });
    }
  }
});


// Finds Pets by status.
router.post('/findPetsByStatus', async (req: Request, res: Response) => {
  try {
    const { query } = req.body;
    
    // Build URL with path parameters
    let url = `${BASE_URL}/pet/findByStatus`;
    
    // Prepare request configuration
    const config: any = {
      method: 'GET',
      url,
      timeout: 30000
    };
    
    // Add query parameters
    if (query) {
      config.params = query;
    }
    
    
    
    // Make API request
    const response = await axios(config);
    
    res.json({
      success: true,
      data: response.data,
      status: response.status,
      headers: response.headers
    });
    
  } catch (error: any) {
    console.error('findPetsByStatus error:', error.message);
    
    if (error.response) {
      // API error
      res.status(error.response.status || 500).json({
        success: false,
        error: 'API Error',
        message: error.response.data?.message || error.message,
        status: error.response.status,
        data: error.response.data
      });
    } else {
      // Network or other error
      res.status(500).json({
        success: false,
        error: 'Request Failed',
        message: error.message
      });
    }
  }
});


// Finds Pets by tags.
router.post('/findPetsByTags', async (req: Request, res: Response) => {
  try {
    const { query } = req.body;
    
    // Build URL with path parameters
    let url = `${BASE_URL}/pet/findByTags`;
    
    // Prepare request configuration
    const config: any = {
      method: 'GET',
      url,
      timeout: 30000
    };
    
    // Add query parameters
    if (query) {
      config.params = query;
    }
    
    
    
    // Make API request
    const response = await axios(config);
    
    res.json({
      success: true,
      data: response.data,
      status: response.status,
      headers: response.headers
    });
    
  } catch (error: any) {
    console.error('findPetsByTags error:', error.message);
    
    if (error.response) {
      // API error
      res.status(error.response.status || 500).json({
        success: false,
        error: 'API Error',
        message: error.response.data?.message || error.message,
        status: error.response.status,
        data: error.response.data
      });
    } else {
      // Network or other error
      res.status(500).json({
        success: false,
        error: 'Request Failed',
        message: error.message
      });
    }
  }
});


// Find pet by ID.
router.post('/getPetById', async (req: Request, res: Response) => {
  try {
    const { petId } = req.body;
    
    // Build URL with path parameters
    let url = `${BASE_URL}/pet/{petId}`;
    url = url.replace('petId', encodeURIComponent(petId));
    
    // Prepare request configuration
    const config: any = {
      method: 'GET',
      url,
      timeout: 30000
    };
    
    
    
    
    // Make API request
    const response = await axios(config);
    
    res.json({
      success: true,
      data: response.data,
      status: response.status,
      headers: response.headers
    });
    
  } catch (error: any) {
    console.error('getPetById error:', error.message);
    
    if (error.response) {
      // API error
      res.status(error.response.status || 500).json({
        success: false,
        error: 'API Error',
        message: error.response.data?.message || error.message,
        status: error.response.status,
        data: error.response.data
      });
    } else {
      // Network or other error
      res.status(500).json({
        success: false,
        error: 'Request Failed',
        message: error.message
      });
    }
  }
});


// Updates a pet in the store with form data.
router.post('/updatePetWithForm', async (req: Request, res: Response) => {
  try {
    const { petId, query } = req.body;
    
    // Build URL with path parameters
    let url = `${BASE_URL}/pet/{petId}`;
    url = url.replace('petId', encodeURIComponent(petId));
    
    // Prepare request configuration
    const config: any = {
      method: 'POST',
      url,
      timeout: 30000
    };
    
    // Add query parameters
    if (query) {
      config.params = query;
    }
    
    
    
    // Make API request
    const response = await axios(config);
    
    res.json({
      success: true,
      data: response.data,
      status: response.status,
      headers: response.headers
    });
    
  } catch (error: any) {
    console.error('updatePetWithForm error:', error.message);
    
    if (error.response) {
      // API error
      res.status(error.response.status || 500).json({
        success: false,
        error: 'API Error',
        message: error.response.data?.message || error.message,
        status: error.response.status,
        data: error.response.data
      });
    } else {
      // Network or other error
      res.status(500).json({
        success: false,
        error: 'Request Failed',
        message: error.message
      });
    }
  }
});


// Deletes a pet.
router.post('/deletePet', async (req: Request, res: Response) => {
  try {
    const { petId, headers } = req.body;
    
    // Build URL with path parameters
    let url = `${BASE_URL}/pet/{petId}`;
    url = url.replace('petId', encodeURIComponent(petId));
    
    // Prepare request configuration
    const config: any = {
      method: 'DELETE',
      url,
      timeout: 30000
    };
    
    
    // Add headers
    if (headers) {
      config.headers = { ...config.headers, ...headers };
    }
    
    
    // Make API request
    const response = await axios(config);
    
    res.json({
      success: true,
      data: response.data,
      status: response.status,
      headers: response.headers
    });
    
  } catch (error: any) {
    console.error('deletePet error:', error.message);
    
    if (error.response) {
      // API error
      res.status(error.response.status || 500).json({
        success: false,
        error: 'API Error',
        message: error.response.data?.message || error.message,
        status: error.response.status,
        data: error.response.data
      });
    } else {
      // Network or other error
      res.status(500).json({
        success: false,
        error: 'Request Failed',
        message: error.message
      });
    }
  }
});


// Uploads an image.
router.post('/uploadFile', async (req: Request, res: Response) => {
  try {
    const { petId, query, body } = req.body;
    
    // Build URL with path parameters
    let url = `${BASE_URL}/pet/{petId}/uploadImage`;
    url = url.replace('petId', encodeURIComponent(petId));
    
    // Prepare request configuration
    const config: any = {
      method: 'POST',
      url,
      timeout: 30000
    };
    
    // Add query parameters
    if (query) {
      config.params = query;
    }
    
    
    // Add request body
    if (body) {
      config.data = body;
      config.headers = { 
        ...config.headers, 
        'Content-Type': 'application/octet-stream' 
      };
    }
    
    // Make API request
    const response = await axios(config);
    
    res.json({
      success: true,
      data: response.data,
      status: response.status,
      headers: response.headers
    });
    
  } catch (error: any) {
    console.error('uploadFile error:', error.message);
    
    if (error.response) {
      // API error
      res.status(error.response.status || 500).json({
        success: false,
        error: 'API Error',
        message: error.response.data?.message || error.message,
        status: error.response.status,
        data: error.response.data
      });
    } else {
      // Network or other error
      res.status(500).json({
        success: false,
        error: 'Request Failed',
        message: error.message
      });
    }
  }
});


// Returns pet inventories by status.
router.post('/getInventory', async (req: Request, res: Response) => {
  try {
    const {  } = req.body;
    
    // Build URL with path parameters
    let url = `${BASE_URL}/store/inventory`;
    
    // Prepare request configuration
    const config: any = {
      method: 'GET',
      url,
      timeout: 30000
    };
    
    
    
    
    // Make API request
    const response = await axios(config);
    
    res.json({
      success: true,
      data: response.data,
      status: response.status,
      headers: response.headers
    });
    
  } catch (error: any) {
    console.error('getInventory error:', error.message);
    
    if (error.response) {
      // API error
      res.status(error.response.status || 500).json({
        success: false,
        error: 'API Error',
        message: error.response.data?.message || error.message,
        status: error.response.status,
        data: error.response.data
      });
    } else {
      // Network or other error
      res.status(500).json({
        success: false,
        error: 'Request Failed',
        message: error.message
      });
    }
  }
});


// Place an order for a pet.
router.post('/placeOrder', async (req: Request, res: Response) => {
  try {
    const { body } = req.body;
    
    // Build URL with path parameters
    let url = `${BASE_URL}/store/order`;
    
    // Prepare request configuration
    const config: any = {
      method: 'POST',
      url,
      timeout: 30000
    };
    
    
    
    // Add request body
    if (body) {
      config.data = body;
      config.headers = { 
        ...config.headers, 
        'Content-Type': 'application/json' 
      };
    }
    
    // Make API request
    const response = await axios(config);
    
    res.json({
      success: true,
      data: response.data,
      status: response.status,
      headers: response.headers
    });
    
  } catch (error: any) {
    console.error('placeOrder error:', error.message);
    
    if (error.response) {
      // API error
      res.status(error.response.status || 500).json({
        success: false,
        error: 'API Error',
        message: error.response.data?.message || error.message,
        status: error.response.status,
        data: error.response.data
      });
    } else {
      // Network or other error
      res.status(500).json({
        success: false,
        error: 'Request Failed',
        message: error.message
      });
    }
  }
});


// Find purchase order by ID.
router.post('/getOrderById', async (req: Request, res: Response) => {
  try {
    const { orderId } = req.body;
    
    // Build URL with path parameters
    let url = `${BASE_URL}/store/order/{orderId}`;
    url = url.replace('orderId', encodeURIComponent(orderId));
    
    // Prepare request configuration
    const config: any = {
      method: 'GET',
      url,
      timeout: 30000
    };
    
    
    
    
    // Make API request
    const response = await axios(config);
    
    res.json({
      success: true,
      data: response.data,
      status: response.status,
      headers: response.headers
    });
    
  } catch (error: any) {
    console.error('getOrderById error:', error.message);
    
    if (error.response) {
      // API error
      res.status(error.response.status || 500).json({
        success: false,
        error: 'API Error',
        message: error.response.data?.message || error.message,
        status: error.response.status,
        data: error.response.data
      });
    } else {
      // Network or other error
      res.status(500).json({
        success: false,
        error: 'Request Failed',
        message: error.message
      });
    }
  }
});


// Delete purchase order by identifier.
router.post('/deleteOrder', async (req: Request, res: Response) => {
  try {
    const { orderId } = req.body;
    
    // Build URL with path parameters
    let url = `${BASE_URL}/store/order/{orderId}`;
    url = url.replace('orderId', encodeURIComponent(orderId));
    
    // Prepare request configuration
    const config: any = {
      method: 'DELETE',
      url,
      timeout: 30000
    };
    
    
    
    
    // Make API request
    const response = await axios(config);
    
    res.json({
      success: true,
      data: response.data,
      status: response.status,
      headers: response.headers
    });
    
  } catch (error: any) {
    console.error('deleteOrder error:', error.message);
    
    if (error.response) {
      // API error
      res.status(error.response.status || 500).json({
        success: false,
        error: 'API Error',
        message: error.response.data?.message || error.message,
        status: error.response.status,
        data: error.response.data
      });
    } else {
      // Network or other error
      res.status(500).json({
        success: false,
        error: 'Request Failed',
        message: error.message
      });
    }
  }
});


// Create user.
router.post('/createUser', async (req: Request, res: Response) => {
  try {
    const { body } = req.body;
    
    // Build URL with path parameters
    let url = `${BASE_URL}/user`;
    
    // Prepare request configuration
    const config: any = {
      method: 'POST',
      url,
      timeout: 30000
    };
    
    
    
    // Add request body
    if (body) {
      config.data = body;
      config.headers = { 
        ...config.headers, 
        'Content-Type': 'application/json' 
      };
    }
    
    // Make API request
    const response = await axios(config);
    
    res.json({
      success: true,
      data: response.data,
      status: response.status,
      headers: response.headers
    });
    
  } catch (error: any) {
    console.error('createUser error:', error.message);
    
    if (error.response) {
      // API error
      res.status(error.response.status || 500).json({
        success: false,
        error: 'API Error',
        message: error.response.data?.message || error.message,
        status: error.response.status,
        data: error.response.data
      });
    } else {
      // Network or other error
      res.status(500).json({
        success: false,
        error: 'Request Failed',
        message: error.message
      });
    }
  }
});


// Creates list of users with given input array.
router.post('/createUsersWithListInput', async (req: Request, res: Response) => {
  try {
    const { body } = req.body;
    
    // Build URL with path parameters
    let url = `${BASE_URL}/user/createWithList`;
    
    // Prepare request configuration
    const config: any = {
      method: 'POST',
      url,
      timeout: 30000
    };
    
    
    
    // Add request body
    if (body) {
      config.data = body;
      config.headers = { 
        ...config.headers, 
        'Content-Type': 'application/json' 
      };
    }
    
    // Make API request
    const response = await axios(config);
    
    res.json({
      success: true,
      data: response.data,
      status: response.status,
      headers: response.headers
    });
    
  } catch (error: any) {
    console.error('createUsersWithListInput error:', error.message);
    
    if (error.response) {
      // API error
      res.status(error.response.status || 500).json({
        success: false,
        error: 'API Error',
        message: error.response.data?.message || error.message,
        status: error.response.status,
        data: error.response.data
      });
    } else {
      // Network or other error
      res.status(500).json({
        success: false,
        error: 'Request Failed',
        message: error.message
      });
    }
  }
});


// Logs user into the system.
router.post('/loginUser', async (req: Request, res: Response) => {
  try {
    const { query } = req.body;
    
    // Build URL with path parameters
    let url = `${BASE_URL}/user/login`;
    
    // Prepare request configuration
    const config: any = {
      method: 'GET',
      url,
      timeout: 30000
    };
    
    // Add query parameters
    if (query) {
      config.params = query;
    }
    
    
    
    // Make API request
    const response = await axios(config);
    
    res.json({
      success: true,
      data: response.data,
      status: response.status,
      headers: response.headers
    });
    
  } catch (error: any) {
    console.error('loginUser error:', error.message);
    
    if (error.response) {
      // API error
      res.status(error.response.status || 500).json({
        success: false,
        error: 'API Error',
        message: error.response.data?.message || error.message,
        status: error.response.status,
        data: error.response.data
      });
    } else {
      // Network or other error
      res.status(500).json({
        success: false,
        error: 'Request Failed',
        message: error.message
      });
    }
  }
});


// Logs out current logged in user session.
router.post('/logoutUser', async (req: Request, res: Response) => {
  try {
    const {  } = req.body;
    
    // Build URL with path parameters
    let url = `${BASE_URL}/user/logout`;
    
    // Prepare request configuration
    const config: any = {
      method: 'GET',
      url,
      timeout: 30000
    };
    
    
    
    
    // Make API request
    const response = await axios(config);
    
    res.json({
      success: true,
      data: response.data,
      status: response.status,
      headers: response.headers
    });
    
  } catch (error: any) {
    console.error('logoutUser error:', error.message);
    
    if (error.response) {
      // API error
      res.status(error.response.status || 500).json({
        success: false,
        error: 'API Error',
        message: error.response.data?.message || error.message,
        status: error.response.status,
        data: error.response.data
      });
    } else {
      // Network or other error
      res.status(500).json({
        success: false,
        error: 'Request Failed',
        message: error.message
      });
    }
  }
});


// Get user by user name.
router.post('/getUserByName', async (req: Request, res: Response) => {
  try {
    const { username } = req.body;
    
    // Build URL with path parameters
    let url = `${BASE_URL}/user/{username}`;
    url = url.replace('username', encodeURIComponent(username));
    
    // Prepare request configuration
    const config: any = {
      method: 'GET',
      url,
      timeout: 30000
    };
    
    
    
    
    // Make API request
    const response = await axios(config);
    
    res.json({
      success: true,
      data: response.data,
      status: response.status,
      headers: response.headers
    });
    
  } catch (error: any) {
    console.error('getUserByName error:', error.message);
    
    if (error.response) {
      // API error
      res.status(error.response.status || 500).json({
        success: false,
        error: 'API Error',
        message: error.response.data?.message || error.message,
        status: error.response.status,
        data: error.response.data
      });
    } else {
      // Network or other error
      res.status(500).json({
        success: false,
        error: 'Request Failed',
        message: error.message
      });
    }
  }
});


// Update user resource.
router.post('/updateUser', async (req: Request, res: Response) => {
  try {
    const { username, body } = req.body;
    
    // Build URL with path parameters
    let url = `${BASE_URL}/user/{username}`;
    url = url.replace('username', encodeURIComponent(username));
    
    // Prepare request configuration
    const config: any = {
      method: 'PUT',
      url,
      timeout: 30000
    };
    
    
    
    // Add request body
    if (body) {
      config.data = body;
      config.headers = { 
        ...config.headers, 
        'Content-Type': 'application/json' 
      };
    }
    
    // Make API request
    const response = await axios(config);
    
    res.json({
      success: true,
      data: response.data,
      status: response.status,
      headers: response.headers
    });
    
  } catch (error: any) {
    console.error('updateUser error:', error.message);
    
    if (error.response) {
      // API error
      res.status(error.response.status || 500).json({
        success: false,
        error: 'API Error',
        message: error.response.data?.message || error.message,
        status: error.response.status,
        data: error.response.data
      });
    } else {
      // Network or other error
      res.status(500).json({
        success: false,
        error: 'Request Failed',
        message: error.message
      });
    }
  }
});


// Delete user resource.
router.post('/deleteUser', async (req: Request, res: Response) => {
  try {
    const { username } = req.body;
    
    // Build URL with path parameters
    let url = `${BASE_URL}/user/{username}`;
    url = url.replace('username', encodeURIComponent(username));
    
    // Prepare request configuration
    const config: any = {
      method: 'DELETE',
      url,
      timeout: 30000
    };
    
    
    
    
    // Make API request
    const response = await axios(config);
    
    res.json({
      success: true,
      data: response.data,
      status: response.status,
      headers: response.headers
    });
    
  } catch (error: any) {
    console.error('deleteUser error:', error.message);
    
    if (error.response) {
      // API error
      res.status(error.response.status || 500).json({
        success: false,
        error: 'API Error',
        message: error.response.data?.message || error.message,
        status: error.response.status,
        data: error.response.data
      });
    } else {
      // Network or other error
      res.status(500).json({
        success: false,
        error: 'Request Failed',
        message: error.message
      });
    }
  }
});


export { router };
export default router;
