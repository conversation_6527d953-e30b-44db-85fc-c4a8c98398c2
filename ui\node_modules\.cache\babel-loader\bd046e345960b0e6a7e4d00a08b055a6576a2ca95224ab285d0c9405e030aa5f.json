{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst CornerUpRight = createLucideIcon(\"CornerUpRight\", [[\"polyline\", {\n  points: \"15 14 20 9 15 4\",\n  key: \"1tbx3s\"\n}], [\"path\", {\n  d: \"M4 20v-7a4 4 0 0 1 4-4h12\",\n  key: \"1lu4f8\"\n}]]);\nexport { CornerUpRight as default };", "map": {"version": 3, "names": ["CornerUpRight", "createLucideIcon", "points", "key", "d"], "sources": ["D:\\repos-personal\\repos\\openapi-to-mcp\\ui\\node_modules\\lucide-react\\src\\icons\\corner-up-right.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name CornerUpRight\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cG9seWxpbmUgcG9pbnRzPSIxNSAxNCAyMCA5IDE1IDQiIC8+CiAgPHBhdGggZD0iTTQgMjB2LTdhNCA0IDAgMCAxIDQtNGgxMiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/corner-up-right\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst CornerUpRight = createLucideIcon('CornerUpRight', [\n  ['polyline', { points: '15 14 20 9 15 4', key: '1tbx3s' }],\n  ['path', { d: 'M4 20v-7a4 4 0 0 1 4-4h12', key: '1lu4f8' }],\n]);\n\nexport default CornerUpRight;\n"], "mappings": ";;;;;AAaM,MAAAA,aAAA,GAAgBC,gBAAA,CAAiB,eAAiB,GACtD,CAAC,UAAY;EAAEC,MAAA,EAAQ,iBAAmB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzD,CAAC,MAAQ;EAAEC,CAAA,EAAG,2BAA6B;EAAAD,GAAA,EAAK;AAAA,CAAU,EAC3D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}