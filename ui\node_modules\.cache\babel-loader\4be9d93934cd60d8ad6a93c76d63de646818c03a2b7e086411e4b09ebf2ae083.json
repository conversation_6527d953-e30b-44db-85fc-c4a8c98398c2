{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst Dices = createLucideIcon(\"Dices\", [[\"rect\", {\n  width: \"12\",\n  height: \"12\",\n  x: \"2\",\n  y: \"10\",\n  rx: \"2\",\n  ry: \"2\",\n  key: \"6agr2n\"\n}], [\"path\", {\n  d: \"m17.92 14 3.5-3.5a2.24 2.24 0 0 0 0-3l-5-4.92a2.24 2.24 0 0 0-3 0L10 6\",\n  key: \"1o487t\"\n}], [\"path\", {\n  d: \"M6 18h.01\",\n  key: \"uhywen\"\n}], [\"path\", {\n  d: \"M10 14h.01\",\n  key: \"ssrbsk\"\n}], [\"path\", {\n  d: \"M15 6h.01\",\n  key: \"cblpky\"\n}], [\"path\", {\n  d: \"M18 9h.01\",\n  key: \"2061c0\"\n}]]);\nexport { Dices as default };", "map": {"version": 3, "names": ["Dices", "createLucideIcon", "width", "height", "x", "y", "rx", "ry", "key", "d"], "sources": ["D:\\repos-personal\\repos\\openapi-to-mcp\\ui\\node_modules\\lucide-react\\src\\icons\\dices.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Dices\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMTIiIGhlaWdodD0iMTIiIHg9IjIiIHk9IjEwIiByeD0iMiIgcnk9IjIiIC8+CiAgPHBhdGggZD0ibTE3LjkyIDE0IDMuNS0zLjVhMi4yNCAyLjI0IDAgMCAwIDAtM2wtNS00LjkyYTIuMjQgMi4yNCAwIDAgMC0zIDBMMTAgNiIgLz4KICA8cGF0aCBkPSJNNiAxOGguMDEiIC8+CiAgPHBhdGggZD0iTTEwIDE0aC4wMSIgLz4KICA8cGF0aCBkPSJNMTUgNmguMDEiIC8+CiAgPHBhdGggZD0iTTE4IDloLjAxIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/dices\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Dices = createLucideIcon('Dices', [\n  [\n    'rect',\n    {\n      width: '12',\n      height: '12',\n      x: '2',\n      y: '10',\n      rx: '2',\n      ry: '2',\n      key: '6agr2n',\n    },\n  ],\n  [\n    'path',\n    {\n      d: 'm17.92 14 3.5-3.5a2.24 2.24 0 0 0 0-3l-5-4.92a2.24 2.24 0 0 0-3 0L10 6',\n      key: '1o487t',\n    },\n  ],\n  ['path', { d: 'M6 18h.01', key: 'uhywen' }],\n  ['path', { d: 'M10 14h.01', key: 'ssrbsk' }],\n  ['path', { d: 'M15 6h.01', key: 'cblpky' }],\n  ['path', { d: 'M18 9h.01', key: '2061c0' }],\n]);\n\nexport default Dices;\n"], "mappings": ";;;;;AAaM,MAAAA,KAAA,GAAQC,gBAAA,CAAiB,OAAS,GACtC,CACE,QACA;EACEC,KAAO;EACPC,MAAQ;EACRC,CAAG;EACHC,CAAG;EACHC,EAAI;EACJC,EAAI;EACJC,GAAK;AACP,EACF,EACA,CACE,QACA;EACEC,CAAG;EACHD,GAAK;AACP,EACF,EACA,CAAC,MAAQ;EAAEC,CAAA,EAAG,WAAa;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAEC,CAAA,EAAG,YAAc;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,MAAQ;EAAEC,CAAA,EAAG,WAAa;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAEC,CAAA,EAAG,WAAa;EAAAD,GAAA,EAAK;AAAA,CAAU,EAC3C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}