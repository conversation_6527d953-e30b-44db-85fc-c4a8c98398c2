{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst Hop = createLucideIcon(\"Hop\", [[\"path\", {\n  d: \"M17.5 5.5C19 7 20.5 9 21 11c-2.5.5-5 .5-8.5-1\",\n  key: \"l0z2za\"\n}], [\"path\", {\n  d: \"M5.5 17.5C7 19 9 20.5 11 21c.5-2.5.5-5-1-8.5\",\n  key: \"1mqyjd\"\n}], [\"path\", {\n  d: \"M16.5 11.5c1 2 1 3.5 1 6-2.5 0-4 0-6-1\",\n  key: \"10xoad\"\n}], [\"path\", {\n  d: \"M20 11.5c1 1.5 2 3.5 2 4.5-1.5.5-3 0-4.5-.5\",\n  key: \"1a4gpx\"\n}], [\"path\", {\n  d: \"M11.5 20c1.5 1 3.5 2 4.5 2 .5-1.5 0-3-.5-4.5\",\n  key: \"1ufrz1\"\n}], [\"path\", {\n  d: \"M20.5 16.5c1 2 1.5 3.5 1.5 5.5-2 0-3.5-.5-5.5-1.5\",\n  key: \"1ok5d2\"\n}], [\"path\", {\n  d: \"M4.783 4.782C8.493 1.072 14.5 1 18 5c-1 1-4.5 2-6.5 1.5 1 1.5 1 4 .5 5.5-1.5.5-4 .5-5.5-.5C7 13.5 6 17 5 18c-4-3.5-3.927-9.508-.217-13.218Z\",\n  key: \"8hlroy\"\n}], [\"path\", {\n  d: \"M4.5 4.5 3 3c-.184-.185-.184-.816 0-1\",\n  key: \"q3aj97\"\n}]]);\nexport { Hop as default };", "map": {"version": 3, "names": ["Hop", "createLucideIcon", "d", "key"], "sources": ["D:\\repos-personal\\repos\\openapi-to-mcp\\ui\\node_modules\\lucide-react\\src\\icons\\hop.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Hop\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTcuNSA1LjVDMTkgNyAyMC41IDkgMjEgMTFjLTIuNS41LTUgLjUtOC41LTEiIC8+CiAgPHBhdGggZD0iTTUuNSAxNy41QzcgMTkgOSAyMC41IDExIDIxYy41LTIuNS41LTUtMS04LjUiIC8+CiAgPHBhdGggZD0iTTE2LjUgMTEuNWMxIDIgMSAzLjUgMSA2LTIuNSAwLTQgMC02LTEiIC8+CiAgPHBhdGggZD0iTTIwIDExLjVjMSAxLjUgMiAzLjUgMiA0LjUtMS41LjUtMyAwLTQuNS0uNSIgLz4KICA8cGF0aCBkPSJNMTEuNSAyMGMxLjUgMSAzLjUgMiA0LjUgMiAuNS0xLjUgMC0zLS41LTQuNSIgLz4KICA8cGF0aCBkPSJNMjAuNSAxNi41YzEgMiAxLjUgMy41IDEuNSA1LjUtMiAwLTMuNS0uNS01LjUtMS41IiAvPgogIDxwYXRoIGQ9Ik00Ljc4MyA0Ljc4MkM4LjQ5MyAxLjA3MiAxNC41IDEgMTggNWMtMSAxLTQuNSAyLTYuNSAxLjUgMSAxLjUgMSA0IC41IDUuNS0xLjUuNS00IC41LTUuNS0uNUM3IDEzLjUgNiAxNyA1IDE4Yy00LTMuNS0zLjkyNy05LjUwOC0uMjE3LTEzLjIxOFoiIC8+CiAgPHBhdGggZD0iTTQuNSA0LjUgMyAzYy0uMTg0LS4xODUtLjE4NC0uODE2IDAtMSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/hop\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Hop = createLucideIcon('Hop', [\n  [\n    'path',\n    { d: 'M17.5 5.5C19 7 20.5 9 21 11c-2.5.5-5 .5-8.5-1', key: 'l0z2za' },\n  ],\n  [\n    'path',\n    { d: 'M5.5 17.5C7 19 9 20.5 11 21c.5-2.5.5-5-1-8.5', key: '1mqyjd' },\n  ],\n  ['path', { d: 'M16.5 11.5c1 2 1 3.5 1 6-2.5 0-4 0-6-1', key: '10xoad' }],\n  ['path', { d: 'M20 11.5c1 1.5 2 3.5 2 4.5-1.5.5-3 0-4.5-.5', key: '1a4gpx' }],\n  [\n    'path',\n    { d: 'M11.5 20c1.5 1 3.5 2 4.5 2 .5-1.5 0-3-.5-4.5', key: '1ufrz1' },\n  ],\n  [\n    'path',\n    { d: 'M20.5 16.5c1 2 1.5 3.5 1.5 5.5-2 0-3.5-.5-5.5-1.5', key: '1ok5d2' },\n  ],\n  [\n    'path',\n    {\n      d: 'M4.783 4.782C8.493 1.072 14.5 1 18 5c-1 1-4.5 2-6.5 1.5 1 1.5 1 4 .5 5.5-1.5.5-4 .5-5.5-.5C7 13.5 6 17 5 18c-4-3.5-3.927-9.508-.217-13.218Z',\n      key: '8hlroy',\n    },\n  ],\n  ['path', { d: 'M4.5 4.5 3 3c-.184-.185-.184-.816 0-1', key: 'q3aj97' }],\n]);\n\nexport default Hop;\n"], "mappings": ";;;;;AAaM,MAAAA,GAAA,GAAMC,gBAAA,CAAiB,KAAO,GAClC,CACE,QACA;EAAEC,CAAA,EAAG,+CAAiD;EAAAC,GAAA,EAAK;AAAS,EACtE,EACA,CACE,QACA;EAAED,CAAA,EAAG,8CAAgD;EAAAC,GAAA,EAAK;AAAS,EACrE,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,wCAA0C;EAAAC,GAAA,EAAK;AAAA,CAAU,GACvE,CAAC,MAAQ;EAAED,CAAA,EAAG,6CAA+C;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC5E,CACE,QACA;EAAED,CAAA,EAAG,8CAAgD;EAAAC,GAAA,EAAK;AAAS,EACrE,EACA,CACE,QACA;EAAED,CAAA,EAAG,mDAAqD;EAAAC,GAAA,EAAK;AAAS,EAC1E,EACA,CACE,QACA;EACED,CAAG;EACHC,GAAK;AACP,EACF,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,uCAAyC;EAAAC,GAAA,EAAK;AAAA,CAAU,EACvE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}