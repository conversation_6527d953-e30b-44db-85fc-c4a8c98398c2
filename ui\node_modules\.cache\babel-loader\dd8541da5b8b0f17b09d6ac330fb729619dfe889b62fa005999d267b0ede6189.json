{"ast": null, "code": "/**\n * Coming Soon page component\n */import React from'react';import{<PERSON>}from'react-router-dom';import{<PERSON><PERSON><PERSON>,<PERSON>,ArrowLeft,<PERSON>,Clock,Star}from'lucide-react';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";export const ComingSoonPage=_ref=>{let{feature,description,expectedDate=\"Q1 2025\"}=_ref;const[email,setEmail]=React.useState('');const[isSubscribed,setIsSubscribed]=React.useState(false);const handleNotifyMe=e=>{e.preventDefault();// TODO: Integrate with email service\nsetIsSubscribed(true);setEmail('');};return/*#__PURE__*/_jsx(\"div\",{className:\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12\",children:[/*#__PURE__*/_jsxs(Link,{to:\"/convert\",className:\"inline-flex items-center space-x-2 text-gray-600 hover:text-gray-900 mb-8 transition-colors\",children:[/*#__PURE__*/_jsx(ArrowLeft,{className:\"w-4 h-4\"}),/*#__PURE__*/_jsx(\"span\",{children:\"Back to Converter\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex justify-center mb-6\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"relative\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-24 h-24 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full flex items-center justify-center\",children:/*#__PURE__*/_jsx(Sparkles,{className:\"w-12 h-12 text-white\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"absolute -top-2 -right-2 w-8 h-8 bg-yellow-400 rounded-full flex items-center justify-center\",children:/*#__PURE__*/_jsx(Star,{className:\"w-4 h-4 text-yellow-800\"})})]})}),/*#__PURE__*/_jsxs(\"h1\",{className:\"text-4xl font-bold text-gray-900 mb-4\",children:[feature,\" is Coming Soon!\"]}),/*#__PURE__*/_jsx(\"p\",{className:\"text-xl text-gray-600 mb-8 max-w-2xl mx-auto\",children:description}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-center space-x-2 mb-8\",children:[/*#__PURE__*/_jsx(Clock,{className:\"w-5 h-5 text-blue-600\"}),/*#__PURE__*/_jsxs(\"span\",{className:\"text-lg font-medium text-blue-600\",children:[\"Expected: \",expectedDate]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid md:grid-cols-3 gap-6 mb-12\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white rounded-lg p-6 shadow-sm border border-gray-200\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4 mx-auto\",children:/*#__PURE__*/_jsx(Rocket,{className:\"w-6 h-6 text-blue-600\"})}),/*#__PURE__*/_jsx(\"h3\",{className:\"font-semibold text-gray-900 mb-2\",children:\"Powerful Features\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600 text-sm\",children:\"Advanced capabilities designed for professional use\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white rounded-lg p-6 shadow-sm border border-gray-200\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-4 mx-auto\",children:/*#__PURE__*/_jsx(Sparkles,{className:\"w-6 h-6 text-purple-600\"})}),/*#__PURE__*/_jsx(\"h3\",{className:\"font-semibold text-gray-900 mb-2\",children:\"Intuitive Design\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600 text-sm\",children:\"User-friendly interface that makes complex tasks simple\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white rounded-lg p-6 shadow-sm border border-gray-200\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-4 mx-auto\",children:/*#__PURE__*/_jsx(Star,{className:\"w-6 h-6 text-green-600\"})}),/*#__PURE__*/_jsx(\"h3\",{className:\"font-semibold text-gray-900 mb-2\",children:\"Premium Quality\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600 text-sm\",children:\"Built with attention to detail and performance\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white rounded-xl p-8 shadow-lg border border-gray-200 max-w-md mx-auto\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-center mb-4\",children:[/*#__PURE__*/_jsx(Bell,{className:\"w-6 h-6 text-blue-600 mr-2\"}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-semibold text-gray-900\",children:\"Get Notified\"})]}),isSubscribed?/*#__PURE__*/_jsxs(\"div\",{className:\"text-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4\",children:/*#__PURE__*/_jsx(Star,{className:\"w-6 h-6 text-green-600\"})}),/*#__PURE__*/_jsx(\"p\",{className:\"text-green-600 font-medium\",children:\"Thanks! We'll notify you when it's ready.\"})]}):/*#__PURE__*/_jsxs(\"form\",{onSubmit:handleNotifyMe,className:\"space-y-4\",children:[/*#__PURE__*/_jsxs(\"p\",{className:\"text-gray-600 text-sm mb-4\",children:[\"Be the first to know when \",feature.toLowerCase(),\" launches\"]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex space-x-2\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"email\",value:email,onChange:e=>setEmail(e.target.value),placeholder:\"Enter your email\",className:\"flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",required:true}),/*#__PURE__*/_jsx(\"button\",{type:\"submit\",className:\"bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-2 rounded-lg font-medium hover:from-blue-700 hover:to-purple-700 transition-all\",children:\"Notify Me\"})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"mt-12\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600 mb-4\",children:\"In the meantime, try our API to MCP converter\"}),/*#__PURE__*/_jsxs(Link,{to:\"/convert\",className:\"inline-flex items-center space-x-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-3 rounded-lg font-medium hover:from-blue-700 hover:to-purple-700 transition-all\",children:[/*#__PURE__*/_jsx(Rocket,{className:\"w-4 h-4\"}),/*#__PURE__*/_jsx(\"span\",{children:\"Start Converting APIs\"})]})]})]})]})});};", "map": {"version": 3, "names": ["React", "Link", "<PERSON><PERSON><PERSON>", "Bell", "ArrowLeft", "Rocket", "Clock", "Star", "jsx", "_jsx", "jsxs", "_jsxs", "ComingSoonPage", "_ref", "feature", "description", "expectedDate", "email", "setEmail", "useState", "isSubscribed", "setIsSubscribed", "handleNotifyMe", "e", "preventDefault", "className", "children", "to", "onSubmit", "toLowerCase", "type", "value", "onChange", "target", "placeholder", "required"], "sources": ["D:/repos-personal/repos/openapi-to-mcp/ui/src/pages/ComingSoonPage.tsx"], "sourcesContent": ["/**\n * Coming Soon page component\n */\n\nimport React from 'react';\nimport { <PERSON> } from 'react-router-dom';\nimport { \n  <PERSON><PERSON><PERSON>, \n  <PERSON>, \n  ArrowLeft, \n  Rocket,\n  Clock,\n  Star\n} from 'lucide-react';\n\ninterface ComingSoonPageProps {\n  feature: string;\n  description: string;\n  expectedDate?: string;\n}\n\nexport const ComingSoonPage: React.FC<ComingSoonPageProps> = ({ \n  feature, \n  description, \n  expectedDate = \"Q1 2025\" \n}) => {\n  const [email, setEmail] = React.useState('');\n  const [isSubscribed, setIsSubscribed] = React.useState(false);\n\n  const handleNotifyMe = (e: React.FormEvent) => {\n    e.preventDefault();\n    // TODO: Integrate with email service\n    setIsSubscribed(true);\n    setEmail('');\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50\">\n      <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n        {/* Back Button */}\n        <Link\n          to=\"/convert\"\n          className=\"inline-flex items-center space-x-2 text-gray-600 hover:text-gray-900 mb-8 transition-colors\"\n        >\n          <ArrowLeft className=\"w-4 h-4\" />\n          <span>Back to Converter</span>\n        </Link>\n\n        <div className=\"text-center\">\n          {/* Icon */}\n          <div className=\"flex justify-center mb-6\">\n            <div className=\"relative\">\n              <div className=\"w-24 h-24 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full flex items-center justify-center\">\n                <Sparkles className=\"w-12 h-12 text-white\" />\n              </div>\n              <div className=\"absolute -top-2 -right-2 w-8 h-8 bg-yellow-400 rounded-full flex items-center justify-center\">\n                <Star className=\"w-4 h-4 text-yellow-800\" />\n              </div>\n            </div>\n          </div>\n\n          {/* Title */}\n          <h1 className=\"text-4xl font-bold text-gray-900 mb-4\">\n            {feature} is Coming Soon!\n          </h1>\n\n          {/* Description */}\n          <p className=\"text-xl text-gray-600 mb-8 max-w-2xl mx-auto\">\n            {description}\n          </p>\n\n          {/* Expected Date */}\n          <div className=\"flex items-center justify-center space-x-2 mb-8\">\n            <Clock className=\"w-5 h-5 text-blue-600\" />\n            <span className=\"text-lg font-medium text-blue-600\">Expected: {expectedDate}</span>\n          </div>\n\n          {/* Features Preview */}\n          <div className=\"grid md:grid-cols-3 gap-6 mb-12\">\n            <div className=\"bg-white rounded-lg p-6 shadow-sm border border-gray-200\">\n              <div className=\"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4 mx-auto\">\n                <Rocket className=\"w-6 h-6 text-blue-600\" />\n              </div>\n              <h3 className=\"font-semibold text-gray-900 mb-2\">Powerful Features</h3>\n              <p className=\"text-gray-600 text-sm\">Advanced capabilities designed for professional use</p>\n            </div>\n\n            <div className=\"bg-white rounded-lg p-6 shadow-sm border border-gray-200\">\n              <div className=\"w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-4 mx-auto\">\n                <Sparkles className=\"w-6 h-6 text-purple-600\" />\n              </div>\n              <h3 className=\"font-semibold text-gray-900 mb-2\">Intuitive Design</h3>\n              <p className=\"text-gray-600 text-sm\">User-friendly interface that makes complex tasks simple</p>\n            </div>\n\n            <div className=\"bg-white rounded-lg p-6 shadow-sm border border-gray-200\">\n              <div className=\"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-4 mx-auto\">\n                <Star className=\"w-6 h-6 text-green-600\" />\n              </div>\n              <h3 className=\"font-semibold text-gray-900 mb-2\">Premium Quality</h3>\n              <p className=\"text-gray-600 text-sm\">Built with attention to detail and performance</p>\n            </div>\n          </div>\n\n          {/* Notify Me Form */}\n          <div className=\"bg-white rounded-xl p-8 shadow-lg border border-gray-200 max-w-md mx-auto\">\n            <div className=\"flex items-center justify-center mb-4\">\n              <Bell className=\"w-6 h-6 text-blue-600 mr-2\" />\n              <h3 className=\"text-lg font-semibold text-gray-900\">Get Notified</h3>\n            </div>\n            \n            {isSubscribed ? (\n              <div className=\"text-center\">\n                <div className=\"w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n                  <Star className=\"w-6 h-6 text-green-600\" />\n                </div>\n                <p className=\"text-green-600 font-medium\">Thanks! We'll notify you when it's ready.</p>\n              </div>\n            ) : (\n              <form onSubmit={handleNotifyMe} className=\"space-y-4\">\n                <p className=\"text-gray-600 text-sm mb-4\">\n                  Be the first to know when {feature.toLowerCase()} launches\n                </p>\n                <div className=\"flex space-x-2\">\n                  <input\n                    type=\"email\"\n                    value={email}\n                    onChange={(e) => setEmail(e.target.value)}\n                    placeholder=\"Enter your email\"\n                    className=\"flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                    required\n                  />\n                  <button\n                    type=\"submit\"\n                    className=\"bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-2 rounded-lg font-medium hover:from-blue-700 hover:to-purple-700 transition-all\"\n                  >\n                    Notify Me\n                  </button>\n                </div>\n              </form>\n            )}\n          </div>\n\n          {/* CTA */}\n          <div className=\"mt-12\">\n            <p className=\"text-gray-600 mb-4\">\n              In the meantime, try our API to MCP converter\n            </p>\n            <Link\n              to=\"/convert\"\n              className=\"inline-flex items-center space-x-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-3 rounded-lg font-medium hover:from-blue-700 hover:to-purple-700 transition-all\"\n            >\n              <Rocket className=\"w-4 h-4\" />\n              <span>Start Converting APIs</span>\n            </Link>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n"], "mappings": "AAAA;AACA;AACA,GAEA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,IAAI,KAAQ,kBAAkB,CACvC,OACEC,QAAQ,CACRC,IAAI,CACJC,SAAS,CACTC,MAAM,CACNC,KAAK,CACLC,IAAI,KACC,cAAc,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAQtB,MAAO,MAAM,CAAAC,cAA6C,CAAGC,IAAA,EAIvD,IAJwD,CAC5DC,OAAO,CACPC,WAAW,CACXC,YAAY,CAAG,SACjB,CAAC,CAAAH,IAAA,CACC,KAAM,CAACI,KAAK,CAAEC,QAAQ,CAAC,CAAGlB,KAAK,CAACmB,QAAQ,CAAC,EAAE,CAAC,CAC5C,KAAM,CAACC,YAAY,CAAEC,eAAe,CAAC,CAAGrB,KAAK,CAACmB,QAAQ,CAAC,KAAK,CAAC,CAE7D,KAAM,CAAAG,cAAc,CAAIC,CAAkB,EAAK,CAC7CA,CAAC,CAACC,cAAc,CAAC,CAAC,CAClB;AACAH,eAAe,CAAC,IAAI,CAAC,CACrBH,QAAQ,CAAC,EAAE,CAAC,CACd,CAAC,CAED,mBACET,IAAA,QAAKgB,SAAS,CAAC,oEAAoE,CAAAC,QAAA,cACjFf,KAAA,QAAKc,SAAS,CAAC,8CAA8C,CAAAC,QAAA,eAE3Df,KAAA,CAACV,IAAI,EACH0B,EAAE,CAAC,UAAU,CACbF,SAAS,CAAC,6FAA6F,CAAAC,QAAA,eAEvGjB,IAAA,CAACL,SAAS,EAACqB,SAAS,CAAC,SAAS,CAAE,CAAC,cACjChB,IAAA,SAAAiB,QAAA,CAAM,mBAAiB,CAAM,CAAC,EAC1B,CAAC,cAEPf,KAAA,QAAKc,SAAS,CAAC,aAAa,CAAAC,QAAA,eAE1BjB,IAAA,QAAKgB,SAAS,CAAC,0BAA0B,CAAAC,QAAA,cACvCf,KAAA,QAAKc,SAAS,CAAC,UAAU,CAAAC,QAAA,eACvBjB,IAAA,QAAKgB,SAAS,CAAC,sGAAsG,CAAAC,QAAA,cACnHjB,IAAA,CAACP,QAAQ,EAACuB,SAAS,CAAC,sBAAsB,CAAE,CAAC,CAC1C,CAAC,cACNhB,IAAA,QAAKgB,SAAS,CAAC,8FAA8F,CAAAC,QAAA,cAC3GjB,IAAA,CAACF,IAAI,EAACkB,SAAS,CAAC,yBAAyB,CAAE,CAAC,CACzC,CAAC,EACH,CAAC,CACH,CAAC,cAGNd,KAAA,OAAIc,SAAS,CAAC,uCAAuC,CAAAC,QAAA,EAClDZ,OAAO,CAAC,kBACX,EAAI,CAAC,cAGLL,IAAA,MAAGgB,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CACxDX,WAAW,CACX,CAAC,cAGJJ,KAAA,QAAKc,SAAS,CAAC,iDAAiD,CAAAC,QAAA,eAC9DjB,IAAA,CAACH,KAAK,EAACmB,SAAS,CAAC,uBAAuB,CAAE,CAAC,cAC3Cd,KAAA,SAAMc,SAAS,CAAC,mCAAmC,CAAAC,QAAA,EAAC,YAAU,CAACV,YAAY,EAAO,CAAC,EAChF,CAAC,cAGNL,KAAA,QAAKc,SAAS,CAAC,iCAAiC,CAAAC,QAAA,eAC9Cf,KAAA,QAAKc,SAAS,CAAC,0DAA0D,CAAAC,QAAA,eACvEjB,IAAA,QAAKgB,SAAS,CAAC,gFAAgF,CAAAC,QAAA,cAC7FjB,IAAA,CAACJ,MAAM,EAACoB,SAAS,CAAC,uBAAuB,CAAE,CAAC,CACzC,CAAC,cACNhB,IAAA,OAAIgB,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAC,mBAAiB,CAAI,CAAC,cACvEjB,IAAA,MAAGgB,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,qDAAmD,CAAG,CAAC,EACzF,CAAC,cAENf,KAAA,QAAKc,SAAS,CAAC,0DAA0D,CAAAC,QAAA,eACvEjB,IAAA,QAAKgB,SAAS,CAAC,kFAAkF,CAAAC,QAAA,cAC/FjB,IAAA,CAACP,QAAQ,EAACuB,SAAS,CAAC,yBAAyB,CAAE,CAAC,CAC7C,CAAC,cACNhB,IAAA,OAAIgB,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAC,kBAAgB,CAAI,CAAC,cACtEjB,IAAA,MAAGgB,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,yDAAuD,CAAG,CAAC,EAC7F,CAAC,cAENf,KAAA,QAAKc,SAAS,CAAC,0DAA0D,CAAAC,QAAA,eACvEjB,IAAA,QAAKgB,SAAS,CAAC,iFAAiF,CAAAC,QAAA,cAC9FjB,IAAA,CAACF,IAAI,EAACkB,SAAS,CAAC,wBAAwB,CAAE,CAAC,CACxC,CAAC,cACNhB,IAAA,OAAIgB,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAC,iBAAe,CAAI,CAAC,cACrEjB,IAAA,MAAGgB,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,gDAA8C,CAAG,CAAC,EACpF,CAAC,EACH,CAAC,cAGNf,KAAA,QAAKc,SAAS,CAAC,2EAA2E,CAAAC,QAAA,eACxFf,KAAA,QAAKc,SAAS,CAAC,uCAAuC,CAAAC,QAAA,eACpDjB,IAAA,CAACN,IAAI,EAACsB,SAAS,CAAC,4BAA4B,CAAE,CAAC,cAC/ChB,IAAA,OAAIgB,SAAS,CAAC,qCAAqC,CAAAC,QAAA,CAAC,cAAY,CAAI,CAAC,EAClE,CAAC,CAELN,YAAY,cACXT,KAAA,QAAKc,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BjB,IAAA,QAAKgB,SAAS,CAAC,mFAAmF,CAAAC,QAAA,cAChGjB,IAAA,CAACF,IAAI,EAACkB,SAAS,CAAC,wBAAwB,CAAE,CAAC,CACxC,CAAC,cACNhB,IAAA,MAAGgB,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,2CAAyC,CAAG,CAAC,EACpF,CAAC,cAENf,KAAA,SAAMiB,QAAQ,CAAEN,cAAe,CAACG,SAAS,CAAC,WAAW,CAAAC,QAAA,eACnDf,KAAA,MAAGc,SAAS,CAAC,4BAA4B,CAAAC,QAAA,EAAC,4BACd,CAACZ,OAAO,CAACe,WAAW,CAAC,CAAC,CAAC,WACnD,EAAG,CAAC,cACJlB,KAAA,QAAKc,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7BjB,IAAA,UACEqB,IAAI,CAAC,OAAO,CACZC,KAAK,CAAEd,KAAM,CACbe,QAAQ,CAAGT,CAAC,EAAKL,QAAQ,CAACK,CAAC,CAACU,MAAM,CAACF,KAAK,CAAE,CAC1CG,WAAW,CAAC,kBAAkB,CAC9BT,SAAS,CAAC,8GAA8G,CACxHU,QAAQ,MACT,CAAC,cACF1B,IAAA,WACEqB,IAAI,CAAC,QAAQ,CACbL,SAAS,CAAC,iJAAiJ,CAAAC,QAAA,CAC5J,WAED,CAAQ,CAAC,EACN,CAAC,EACF,CACP,EACE,CAAC,cAGNf,KAAA,QAAKc,SAAS,CAAC,OAAO,CAAAC,QAAA,eACpBjB,IAAA,MAAGgB,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAC,+CAElC,CAAG,CAAC,cACJf,KAAA,CAACV,IAAI,EACH0B,EAAE,CAAC,UAAU,CACbF,SAAS,CAAC,oLAAoL,CAAAC,QAAA,eAE9LjB,IAAA,CAACJ,MAAM,EAACoB,SAAS,CAAC,SAAS,CAAE,CAAC,cAC9BhB,IAAA,SAAAiB,QAAA,CAAM,uBAAqB,CAAM,CAAC,EAC9B,CAAC,EACJ,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,CAEV,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}