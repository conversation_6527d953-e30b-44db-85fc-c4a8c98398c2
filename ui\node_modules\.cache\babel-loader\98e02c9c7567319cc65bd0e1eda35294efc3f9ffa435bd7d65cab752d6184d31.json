{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst Accessibility = createLucideIcon(\"Accessibility\", [[\"circle\", {\n  cx: \"16\",\n  cy: \"4\",\n  r: \"1\",\n  key: \"1grugj\"\n}], [\"path\", {\n  d: \"m18 19 1-7-6 1\",\n  key: \"r0i19z\"\n}], [\"path\", {\n  d: \"m5 8 3-3 5.5 3-2.36 3.5\",\n  key: \"9ptxx2\"\n}], [\"path\", {\n  d: \"M4.24 14.5a5 5 0 0 0 6.88 6\",\n  key: \"10kmtu\"\n}], [\"path\", {\n  d: \"M13.76 17.5a5 5 0 0 0-6.88-6\",\n  key: \"2qq6rc\"\n}]]);\nexport { Accessibility as default };", "map": {"version": 3, "names": ["Accessibility", "createLucideIcon", "cx", "cy", "r", "key", "d"], "sources": ["D:\\repos-personal\\repos\\openapi-to-mcp\\ui\\node_modules\\lucide-react\\src\\icons\\accessibility.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Accessibility\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxNiIgY3k9IjQiIHI9IjEiIC8+CiAgPHBhdGggZD0ibTE4IDE5IDEtNy02IDEiIC8+CiAgPHBhdGggZD0ibTUgOCAzLTMgNS41IDMtMi4zNiAzLjUiIC8+CiAgPHBhdGggZD0iTTQuMjQgMTQuNWE1IDUgMCAwIDAgNi44OCA2IiAvPgogIDxwYXRoIGQ9Ik0xMy43NiAxNy41YTUgNSAwIDAgMC02Ljg4LTYiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/accessibility\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Accessibility = createLucideIcon('Accessibility', [\n  ['circle', { cx: '16', cy: '4', r: '1', key: '1grugj' }],\n  ['path', { d: 'm18 19 1-7-6 1', key: 'r0i19z' }],\n  ['path', { d: 'm5 8 3-3 5.5 3-2.36 3.5', key: '9ptxx2' }],\n  ['path', { d: 'M4.24 14.5a5 5 0 0 0 6.88 6', key: '10kmtu' }],\n  ['path', { d: 'M13.76 17.5a5 5 0 0 0-6.88-6', key: '2qq6rc' }],\n]);\n\nexport default Accessibility;\n"], "mappings": ";;;;;AAaM,MAAAA,aAAA,GAAgBC,gBAAA,CAAiB,eAAiB,GACtD,CAAC,QAAU;EAAEC,EAAI;EAAMC,EAAI;EAAKC,CAAG;EAAKC,GAAK;AAAA,CAAU,GACvD,CAAC,MAAQ;EAAEC,CAAA,EAAG,gBAAkB;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC/C,CAAC,MAAQ;EAAEC,CAAA,EAAG,yBAA2B;EAAAD,GAAA,EAAK;AAAA,CAAU,GACxD,CAAC,MAAQ;EAAEC,CAAA,EAAG,6BAA+B;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC5D,CAAC,MAAQ;EAAEC,CAAA,EAAG,8BAAgC;EAAAD,GAAA,EAAK;AAAA,CAAU,EAC9D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}