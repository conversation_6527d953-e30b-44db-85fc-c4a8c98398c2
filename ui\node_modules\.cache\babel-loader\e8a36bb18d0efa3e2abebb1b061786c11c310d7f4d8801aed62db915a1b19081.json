{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst CandyOff = createLucideIcon(\"CandyOff\", [[\"path\", {\n  d: \"m8.5 8.5-1 1a4.95 4.95 0 0 0 7 7l1-1\",\n  key: \"1ff4ui\"\n}], [\"path\", {\n  d: \"M11.843 6.187A4.947 4.947 0 0 1 16.5 7.5a4.947 4.947 0 0 1 1.313 4.657\",\n  key: \"1sbrv4\"\n}], [\"path\", {\n  d: \"M14 16.5V14\",\n  key: \"1maf8j\"\n}], [\"path\", {\n  d: \"M14 6.5v1.843\",\n  key: \"1a6u6t\"\n}], [\"path\", {\n  d: \"M10 10v7.5\",\n  key: \"80pj65\"\n}], [\"path\", {\n  d: \"m16 7 1-5 1.367.683A3 3 0 0 0 19.708 3H21v1.292a3 3 0 0 0 .317 1.341L22 7l-5 1\",\n  key: \"11a9mt\"\n}], [\"path\", {\n  d: \"m8 17-1 5-1.367-.683A3 3 0 0 0 4.292 21H3v-1.292a3 3 0 0 0-.317-1.341L2 17l5-1\",\n  key: \"3mjmon\"\n}], [\"line\", {\n  x1: \"2\",\n  x2: \"22\",\n  y1: \"2\",\n  y2: \"22\",\n  key: \"a6p6uj\"\n}]]);\nexport { CandyOff as default };", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "createLucideIcon", "d", "key", "x1", "x2", "y1", "y2"], "sources": ["D:\\repos-personal\\repos\\openapi-to-mcp\\ui\\node_modules\\lucide-react\\src\\icons\\candy-off.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name CandyOff\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtOC41IDguNS0xIDFhNC45NSA0Ljk1IDAgMCAwIDcgN2wxLTEiIC8+CiAgPHBhdGggZD0iTTExLjg0MyA2LjE4N0E0Ljk0NyA0Ljk0NyAwIDAgMSAxNi41IDcuNWE0Ljk0NyA0Ljk0NyAwIDAgMSAxLjMxMyA0LjY1NyIgLz4KICA8cGF0aCBkPSJNMTQgMTYuNVYxNCIgLz4KICA8cGF0aCBkPSJNMTQgNi41djEuODQzIiAvPgogIDxwYXRoIGQ9Ik0xMCAxMHY3LjUiIC8+CiAgPHBhdGggZD0ibTE2IDcgMS01IDEuMzY3LjY4M0EzIDMgMCAwIDAgMTkuNzA4IDNIMjF2MS4yOTJhMyAzIDAgMCAwIC4zMTcgMS4zNDFMMjIgN2wtNSAxIiAvPgogIDxwYXRoIGQ9Im04IDE3LTEgNS0xLjM2Ny0uNjgzQTMgMyAwIDAgMCA0LjI5MiAyMUgzdi0xLjI5MmEzIDMgMCAwIDAtLjMxNy0xLjM0MUwyIDE3bDUtMSIgLz4KICA8bGluZSB4MT0iMiIgeDI9IjIyIiB5MT0iMiIgeTI9IjIyIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/candy-off\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst CandyOff = createLucideIcon('CandyOff', [\n  ['path', { d: 'm8.5 8.5-1 1a4.95 4.95 0 0 0 7 7l1-1', key: '1ff4ui' }],\n  [\n    'path',\n    {\n      d: 'M11.843 6.187A4.947 4.947 0 0 1 16.5 7.5a4.947 4.947 0 0 1 1.313 4.657',\n      key: '1sbrv4',\n    },\n  ],\n  ['path', { d: 'M14 16.5V14', key: '1maf8j' }],\n  ['path', { d: 'M14 6.5v1.843', key: '1a6u6t' }],\n  ['path', { d: 'M10 10v7.5', key: '80pj65' }],\n  [\n    'path',\n    {\n      d: 'm16 7 1-5 1.367.683A3 3 0 0 0 19.708 3H21v1.292a3 3 0 0 0 .317 1.341L22 7l-5 1',\n      key: '11a9mt',\n    },\n  ],\n  [\n    'path',\n    {\n      d: 'm8 17-1 5-1.367-.683A3 3 0 0 0 4.292 21H3v-1.292a3 3 0 0 0-.317-1.341L2 17l5-1',\n      key: '3mjmon',\n    },\n  ],\n  ['line', { x1: '2', x2: '22', y1: '2', y2: '22', key: 'a6p6uj' }],\n]);\n\nexport default CandyOff;\n"], "mappings": ";;;;;AAaM,MAAAA,QAAA,GAAWC,gBAAA,CAAiB,UAAY,GAC5C,CAAC,MAAQ;EAAEC,CAAA,EAAG,sCAAwC;EAAAC,GAAA,EAAK;AAAA,CAAU,GACrE,CACE,QACA;EACED,CAAG;EACHC,GAAK;AACP,EACF,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,aAAe;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC5C,CAAC,MAAQ;EAAED,CAAA,EAAG,eAAiB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC9C,CAAC,MAAQ;EAAED,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3C,CACE,QACA;EACED,CAAG;EACHC,GAAK;AACP,EACF,EACA,CACE,QACA;EACED,CAAG;EACHC,GAAK;AACP,EACF,EACA,CAAC,QAAQ;EAAEC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,IAAM;EAAAJ,GAAA,EAAK;AAAA,CAAU,EACjE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}