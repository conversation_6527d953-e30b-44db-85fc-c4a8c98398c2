{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst FileCog = createLucideIcon(\"FileCog\", [[\"path\", {\n  d: \"M4 6V4a2 2 0 0 1 2-2h8.5L20 7.5V20a2 2 0 0 1-2 2H4\",\n  key: \"dba9qu\"\n}], [\"polyline\", {\n  points: \"14 2 14 8 20 8\",\n  key: \"1ew0cm\"\n}], [\"circle\", {\n  cx: \"6\",\n  cy: \"14\",\n  r: \"3\",\n  key: \"a1xfv6\"\n}], [\"path\", {\n  d: \"M6 10v1\",\n  key: \"xs0f9j\"\n}], [\"path\", {\n  d: \"M6 17v1\",\n  key: \"idyhc0\"\n}], [\"path\", {\n  d: \"M10 14H9\",\n  key: \"m5fm2q\"\n}], [\"path\", {\n  d: \"M3 14H2\",\n  key: \"19ot09\"\n}], [\"path\", {\n  d: \"m9 11-.88.88\",\n  key: \"lhul2b\"\n}], [\"path\", {\n  d: \"M3.88 16.12 3 17\",\n  key: \"169z9n\"\n}], [\"path\", {\n  d: \"m9 17-.88-.88\",\n  key: \"5io96w\"\n}], [\"path\", {\n  d: \"M3.88 11.88 3 11\",\n  key: \"1ynhy1\"\n}]]);\nexport { FileCog as default };", "map": {"version": 3, "names": ["FileCog", "createLucideIcon", "d", "key", "points", "cx", "cy", "r"], "sources": ["D:\\repos-personal\\repos\\openapi-to-mcp\\ui\\node_modules\\lucide-react\\src\\icons\\file-cog.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name FileCog\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNCA2VjRhMiAyIDAgMCAxIDItMmg4LjVMMjAgNy41VjIwYTIgMiAwIDAgMS0yIDJINCIgLz4KICA8cG9seWxpbmUgcG9pbnRzPSIxNCAyIDE0IDggMjAgOCIgLz4KICA8Y2lyY2xlIGN4PSI2IiBjeT0iMTQiIHI9IjMiIC8+CiAgPHBhdGggZD0iTTYgMTB2MSIgLz4KICA8cGF0aCBkPSJNNiAxN3YxIiAvPgogIDxwYXRoIGQ9Ik0xMCAxNEg5IiAvPgogIDxwYXRoIGQ9Ik0zIDE0SDIiIC8+CiAgPHBhdGggZD0ibTkgMTEtLjg4Ljg4IiAvPgogIDxwYXRoIGQ9Ik0zLjg4IDE2LjEyIDMgMTciIC8+CiAgPHBhdGggZD0ibTkgMTctLjg4LS44OCIgLz4KICA8cGF0aCBkPSJNMy44OCAxMS44OCAzIDExIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/file-cog\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst FileCog = createLucideIcon('FileCog', [\n  [\n    'path',\n    { d: 'M4 6V4a2 2 0 0 1 2-2h8.5L20 7.5V20a2 2 0 0 1-2 2H4', key: 'dba9qu' },\n  ],\n  ['polyline', { points: '14 2 14 8 20 8', key: '1ew0cm' }],\n  ['circle', { cx: '6', cy: '14', r: '3', key: 'a1xfv6' }],\n  ['path', { d: 'M6 10v1', key: 'xs0f9j' }],\n  ['path', { d: 'M6 17v1', key: 'idyhc0' }],\n  ['path', { d: 'M10 14H9', key: 'm5fm2q' }],\n  ['path', { d: 'M3 14H2', key: '19ot09' }],\n  ['path', { d: 'm9 11-.88.88', key: 'lhul2b' }],\n  ['path', { d: 'M3.88 16.12 3 17', key: '169z9n' }],\n  ['path', { d: 'm9 17-.88-.88', key: '5io96w' }],\n  ['path', { d: 'M3.88 11.88 3 11', key: '1ynhy1' }],\n]);\n\nexport default FileCog;\n"], "mappings": ";;;;;AAaM,MAAAA,OAAA,GAAUC,gBAAA,CAAiB,SAAW,GAC1C,CACE,QACA;EAAEC,CAAA,EAAG,oDAAsD;EAAAC,GAAA,EAAK;AAAS,EAC3E,EACA,CAAC,UAAY;EAAEC,MAAA,EAAQ,gBAAkB;EAAAD,GAAA,EAAK;AAAA,CAAU,GACxD,CAAC,QAAU;EAAEE,EAAI;EAAKC,EAAI;EAAMC,CAAG;EAAKJ,GAAK;AAAA,CAAU,GACvD,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,cAAgB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC7C,CAAC,MAAQ;EAAED,CAAA,EAAG,kBAAoB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACjD,CAAC,MAAQ;EAAED,CAAA,EAAG,eAAiB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC9C,CAAC,MAAQ;EAAED,CAAA,EAAG,kBAAoB;EAAAC,GAAA,EAAK;AAAA,CAAU,EAClD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}