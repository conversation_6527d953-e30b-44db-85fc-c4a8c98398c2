{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst Coins = createLucideIcon(\"Coins\", [[\"circle\", {\n  cx: \"8\",\n  cy: \"8\",\n  r: \"6\",\n  key: \"3yglwk\"\n}], [\"path\", {\n  d: \"M18.09 10.37A6 6 0 1 1 10.34 18\",\n  key: \"t5s6rm\"\n}], [\"path\", {\n  d: \"M7 6h1v4\",\n  key: \"1obek4\"\n}], [\"path\", {\n  d: \"m16.71 **********-2.82 2.82\",\n  key: \"1rbuyh\"\n}]]);\nexport { Coins as default };", "map": {"version": 3, "names": ["Coins", "createLucideIcon", "cx", "cy", "r", "key", "d"], "sources": ["D:\\repos-personal\\repos\\openapi-to-mcp\\ui\\node_modules\\lucide-react\\src\\icons\\coins.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Coins\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSI4IiBjeT0iOCIgcj0iNiIgLz4KICA8cGF0aCBkPSJNMTguMDkgMTAuMzdBNiA2IDAgMSAxIDEwLjM0IDE4IiAvPgogIDxwYXRoIGQ9Ik03IDZoMXY0IiAvPgogIDxwYXRoIGQ9Im0xNi43MSAxMy44OC43LjcxLTIuODIgMi44MiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/coins\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Coins = createLucideIcon('Coins', [\n  ['circle', { cx: '8', cy: '8', r: '6', key: '3yglwk' }],\n  ['path', { d: 'M18.09 10.37A6 6 0 1 1 10.34 18', key: 't5s6rm' }],\n  ['path', { d: 'M7 6h1v4', key: '1obek4' }],\n  ['path', { d: 'm16.71 **********-2.82 2.82', key: '1rbuyh' }],\n]);\n\nexport default Coins;\n"], "mappings": ";;;;;AAaM,MAAAA,KAAA,GAAQC,gBAAA,CAAiB,OAAS,GACtC,CAAC,QAAU;EAAEC,EAAI;EAAKC,EAAI;EAAKC,CAAG;EAAKC,GAAK;AAAA,CAAU,GACtD,CAAC,MAAQ;EAAEC,CAAA,EAAG,iCAAmC;EAAAD,GAAA,EAAK;AAAA,CAAU,GAChE,CAAC,MAAQ;EAAEC,CAAA,EAAG,UAAY;EAAAD,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAEC,CAAA,EAAG,6BAA+B;EAAAD,GAAA,EAAK;AAAA,CAAU,EAC7D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}