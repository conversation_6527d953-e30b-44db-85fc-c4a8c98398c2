{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst FileCog2 = createLucideIcon(\"FileCog2\", [[\"path\", {\n  d: \"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z\",\n  key: \"1nnpy2\"\n}], [\"polyline\", {\n  points: \"14 2 14 8 20 8\",\n  key: \"1ew0cm\"\n}], [\"circle\", {\n  cx: \"12\",\n  cy: \"15\",\n  r: \"2\",\n  key: \"1vpstw\"\n}], [\"path\", {\n  d: \"M12 12v1\",\n  key: \"1vuyxr\"\n}], [\"path\", {\n  d: \"M12 17v1\",\n  key: \"y8y3f9\"\n}], [\"path\", {\n  d: \"m14.6 13.5-.87.5\",\n  key: \"nomeg4\"\n}], [\"path\", {\n  d: \"m10.27 16-.87.5\",\n  key: \"1o8v95\"\n}], [\"path\", {\n  d: \"m14.6 16.5-.87-.5\",\n  key: \"gzu2jw\"\n}], [\"path\", {\n  d: \"m10.27 14-.87-.5\",\n  key: \"1vlkk3\"\n}]]);\nexport { FileCog2 as default };", "map": {"version": 3, "names": ["FileCog2", "createLucideIcon", "d", "key", "points", "cx", "cy", "r"], "sources": ["D:\\repos-personal\\repos\\openapi-to-mcp\\ui\\node_modules\\lucide-react\\src\\icons\\file-cog-2.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name FileCog2\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTQuNSAySDZhMiAyIDAgMCAwLTIgMnYxNmEyIDIgMCAwIDAgMiAyaDEyYTIgMiAwIDAgMCAyLTJWNy41TDE0LjUgMnoiIC8+CiAgPHBvbHlsaW5lIHBvaW50cz0iMTQgMiAxNCA4IDIwIDgiIC8+CiAgPGNpcmNsZSBjeD0iMTIiIGN5PSIxNSIgcj0iMiIgLz4KICA8cGF0aCBkPSJNMTIgMTJ2MSIgLz4KICA8cGF0aCBkPSJNMTIgMTd2MSIgLz4KICA8cGF0aCBkPSJtMTQuNiAxMy41LS44Ny41IiAvPgogIDxwYXRoIGQ9Im0xMC4yNyAxNi0uODcuNSIgLz4KICA8cGF0aCBkPSJtMTQuNiAxNi41LS44Ny0uNSIgLz4KICA8cGF0aCBkPSJtMTAuMjcgMTQtLjg3LS41IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/file-cog-2\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst FileCog2 = createLucideIcon('FileCog2', [\n  [\n    'path',\n    {\n      d: 'M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z',\n      key: '1nnpy2',\n    },\n  ],\n  ['polyline', { points: '14 2 14 8 20 8', key: '1ew0cm' }],\n  ['circle', { cx: '12', cy: '15', r: '2', key: '1vpstw' }],\n  ['path', { d: 'M12 12v1', key: '1vuyxr' }],\n  ['path', { d: 'M12 17v1', key: 'y8y3f9' }],\n  ['path', { d: 'm14.6 13.5-.87.5', key: 'nomeg4' }],\n  ['path', { d: 'm10.27 16-.87.5', key: '1o8v95' }],\n  ['path', { d: 'm14.6 16.5-.87-.5', key: 'gzu2jw' }],\n  ['path', { d: 'm10.27 14-.87-.5', key: '1vlkk3' }],\n]);\n\nexport default FileCog2;\n"], "mappings": ";;;;;AAaM,MAAAA,QAAA,GAAWC,gBAAA,CAAiB,UAAY,GAC5C,CACE,QACA;EACEC,CAAG;EACHC,GAAK;AACP,EACF,EACA,CAAC,UAAY;EAAEC,MAAA,EAAQ,gBAAkB;EAAAD,GAAA,EAAK;AAAA,CAAU,GACxD,CAAC,QAAU;EAAEE,EAAI;EAAMC,EAAI;EAAMC,CAAG;EAAKJ,GAAK;AAAA,CAAU,GACxD,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,kBAAoB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACjD,CAAC,MAAQ;EAAED,CAAA,EAAG,iBAAmB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAChD,CAAC,MAAQ;EAAED,CAAA,EAAG,mBAAqB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAClD,CAAC,MAAQ;EAAED,CAAA,EAAG,kBAAoB;EAAAC,GAAA,EAAK;AAAA,CAAU,EAClD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}