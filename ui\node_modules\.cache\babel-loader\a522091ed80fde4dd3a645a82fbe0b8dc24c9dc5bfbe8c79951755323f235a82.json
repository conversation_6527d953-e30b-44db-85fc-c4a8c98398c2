{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst Package2 = createLucideIcon(\"Package2\", [[\"path\", {\n  d: \"M3 9h18v10a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V9Z\",\n  key: \"1ront0\"\n}], [\"path\", {\n  d: \"m3 9 2.45-4.9A2 2 0 0 1 7.24 3h9.52a2 2 0 0 1 1.8 1.1L21 9\",\n  key: \"19h2x1\"\n}], [\"path\", {\n  d: \"M12 3v6\",\n  key: \"1holv5\"\n}]]);\nexport { Package2 as default };", "map": {"version": 3, "names": ["Package2", "createLucideIcon", "d", "key"], "sources": ["D:\\repos-personal\\repos\\openapi-to-mcp\\ui\\node_modules\\lucide-react\\src\\icons\\package-2.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Package2\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMyA5aDE4djEwYTIgMiAwIDAgMS0yIDJINWEyIDIgMCAwIDEtMi0yVjlaIiAvPgogIDxwYXRoIGQ9Im0zIDkgMi40NS00LjlBMiAyIDAgMCAxIDcuMjQgM2g5LjUyYTIgMiAwIDAgMSAxLjggMS4xTDIxIDkiIC8+CiAgPHBhdGggZD0iTTEyIDN2NiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/package-2\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Package2 = createLucideIcon('Package2', [\n  ['path', { d: 'M3 9h18v10a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V9Z', key: '1ront0' }],\n  [\n    'path',\n    {\n      d: 'm3 9 2.45-4.9A2 2 0 0 1 7.24 3h9.52a2 2 0 0 1 1.8 1.1L21 9',\n      key: '19h2x1',\n    },\n  ],\n  ['path', { d: 'M12 3v6', key: '1holv5' }],\n]);\n\nexport default Package2;\n"], "mappings": ";;;;;AAaM,MAAAA,QAAA,GAAWC,gBAAA,CAAiB,UAAY,GAC5C,CAAC,MAAQ;EAAEC,CAAA,EAAG,6CAA+C;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC5E,CACE,QACA;EACED,CAAG;EACHC,GAAK;AACP,EACF,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,EACzC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}