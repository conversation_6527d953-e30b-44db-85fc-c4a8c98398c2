{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst Church = createLucideIcon(\"Church\", [[\"path\", {\n  d: \"m18 7 4 2v11a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V9l4-2\",\n  key: \"gy5gyo\"\n}], [\"path\", {\n  d: \"M14 22v-4a2 2 0 0 0-2-2v0a2 2 0 0 0-2 2v4\",\n  key: \"cpkuc4\"\n}], [\"path\", {\n  d: \"M18 22V5l-6-3-6 3v17\",\n  key: \"1hsnhq\"\n}], [\"path\", {\n  d: \"M12 7v5\",\n  key: \"ma6bk\"\n}], [\"path\", {\n  d: \"M10 9h4\",\n  key: \"u4k05v\"\n}]]);\nexport { Church as default };", "map": {"version": 3, "names": ["Church", "createLucideIcon", "d", "key"], "sources": ["D:\\repos-personal\\repos\\openapi-to-mcp\\ui\\node_modules\\lucide-react\\src\\icons\\church.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Church\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMTggNyA0IDJ2MTFhMiAyIDAgMCAxLTIgMkg0YTIgMiAwIDAgMS0yLTJWOWw0LTIiIC8+CiAgPHBhdGggZD0iTTE0IDIydi00YTIgMiAwIDAgMC0yLTJ2MGEyIDIgMCAwIDAtMiAydjQiIC8+CiAgPHBhdGggZD0iTTE4IDIyVjVsLTYtMy02IDN2MTciIC8+CiAgPHBhdGggZD0iTTEyIDd2NSIgLz4KICA8cGF0aCBkPSJNMTAgOWg0IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/church\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Church = createLucideIcon('Church', [\n  [\n    'path',\n    { d: 'm18 7 4 2v11a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V9l4-2', key: 'gy5gyo' },\n  ],\n  ['path', { d: 'M14 22v-4a2 2 0 0 0-2-2v0a2 2 0 0 0-2 2v4', key: 'cpkuc4' }],\n  ['path', { d: 'M18 22V5l-6-3-6 3v17', key: '1hsnhq' }],\n  ['path', { d: 'M12 7v5', key: 'ma6bk' }],\n  ['path', { d: 'M10 9h4', key: 'u4k05v' }],\n]);\n\nexport default Church;\n"], "mappings": ";;;;;AAaM,MAAAA,MAAA,GAASC,gBAAA,CAAiB,QAAU,GACxC,CACE,QACA;EAAEC,CAAA,EAAG,kDAAoD;EAAAC,GAAA,EAAK;AAAS,EACzE,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,2CAA6C;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1E,CAAC,MAAQ;EAAED,CAAA,EAAG,sBAAwB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACrD,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAS,GACvC,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,EACzC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}