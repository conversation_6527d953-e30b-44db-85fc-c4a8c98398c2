{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst Clapperboard = createLucideIcon(\"Clapperboard\", [[\"path\", {\n  d: \"M4 11v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8H4Z\",\n  key: \"1hxvyx\"\n}], [\"path\", {\n  d: \"m4 11-.88-2.87a2 2 0 0 1 1.33-2.5l11.48-3.5a2 2 0 0 1 2.5 1.32l.87 2.87L4 11.01Z\",\n  key: \"1vz1k2\"\n}], [\"path\", {\n  d: \"m6.6 4.99 3.38 4.2\",\n  key: \"192ida\"\n}], [\"path\", {\n  d: \"m11.86 3.38 3.38 4.2\",\n  key: \"hhucvz\"\n}]]);\nexport { Clapperboard as default };", "map": {"version": 3, "names": ["Clapperboard", "createLucideIcon", "d", "key"], "sources": ["D:\\repos-personal\\repos\\openapi-to-mcp\\ui\\node_modules\\lucide-react\\src\\icons\\clapperboard.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Clapperboard\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNCAxMXY4YTIgMiAwIDAgMCAyIDJoMTJhMiAyIDAgMCAwIDItMnYtOEg0WiIgLz4KICA8cGF0aCBkPSJtNCAxMS0uODgtMi44N2EyIDIgMCAwIDEgMS4zMy0yLjVsMTEuNDgtMy41YTIgMiAwIDAgMSAyLjUgMS4zMmwuODcgMi44N0w0IDExLjAxWiIgLz4KICA8cGF0aCBkPSJtNi42IDQuOTkgMy4zOCA0LjIiIC8+CiAgPHBhdGggZD0ibTExLjg2IDMuMzggMy4zOCA0LjIiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/clapperboard\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Clapperboard = createLucideIcon('Clapperboard', [\n  [\n    'path',\n    { d: 'M4 11v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8H4Z', key: '1hxvyx' },\n  ],\n  [\n    'path',\n    {\n      d: 'm4 11-.88-2.87a2 2 0 0 1 1.33-2.5l11.48-3.5a2 2 0 0 1 2.5 1.32l.87 2.87L4 11.01Z',\n      key: '1vz1k2',\n    },\n  ],\n  ['path', { d: 'm6.6 4.99 3.38 4.2', key: '192ida' }],\n  ['path', { d: 'm11.86 3.38 3.38 4.2', key: 'hhucvz' }],\n]);\n\nexport default Clapperboard;\n"], "mappings": ";;;;;AAaM,MAAAA,YAAA,GAAeC,gBAAA,CAAiB,cAAgB,GACpD,CACE,QACA;EAAEC,CAAA,EAAG,8CAAgD;EAAAC,GAAA,EAAK;AAAS,EACrE,EACA,CACE,QACA;EACED,CAAG;EACHC,GAAK;AACP,EACF,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,oBAAsB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACnD,CAAC,MAAQ;EAAED,CAAA,EAAG,sBAAwB;EAAAC,GAAA,EAAK;AAAA,CAAU,EACtD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}