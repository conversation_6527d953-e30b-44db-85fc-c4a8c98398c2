const axios = require('axios');
const fs = require('fs');

async function generateMCPForCline() {
  try {
    console.log('🔄 Generating MCP server for Cline testing...');
    
    const response = await axios.post('http://localhost:3000/api/convert', {
      openapi: 'https://petstore3.swagger.io/api/v3/openapi.json',
      name: 'cline-test-petstore',
      version: '1.0.0',
      description: 'Petstore MCP Server for Cline Testing'
    });
    
    console.log('✅ Generation successful!');
    console.log('📦 Download URL:', response.data.downloadUrl);
    console.log('📊 Stats:', response.data.stats);
    
    // Download the bundle
    const downloadUrl = `http://localhost:3000${response.data.downloadUrl}`;
    console.log('⬇️ Downloading bundle...');
    
    const downloadResponse = await axios.get(downloadUrl, {
      responseType: 'stream'
    });
    
    const writer = fs.createWriteStream('cline-test-petstore.zip');
    downloadResponse.data.pipe(writer);
    
    await new Promise((resolve, reject) => {
      writer.on('finish', resolve);
      writer.on('error', reject);
    });
    
    console.log('✅ Downloaded: cline-test-petstore.zip');
    console.log('\n🎯 Next steps for Cline testing:');
    console.log('1. Extract the zip file');
    console.log('2. Run: cd cline-test-petstore && npm install && npm run build');
    console.log('3. Configure Cline with the MCP server');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    if (error.response) {
      console.error('Response:', error.response.data);
    }
  }
}

generateMCPForCline();
