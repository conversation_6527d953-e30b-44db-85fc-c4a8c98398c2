export interface ToolRequest {
    [key: string]: any;
}
export interface ToolResponse {
    success: boolean;
    data?: any;
    error?: string;
    message?: string;
    status?: number;
}
export interface HealthResponse {
    status: string;
    name: string;
    version: string;
    timestamp: string;
    baseUrl: string;
}
export interface MCPManifest {
    tags?: any[];
}
export interface addPetRequest {
    body: any;
    tags?: any[];
}
export interface updatePetRequest {
    body: any;
    tags?: any[];
}
export interface findPetsByStatusRequest {
    query?: {
        status?: string;
    };
    tags?: any[];
}
export interface findPetsByTagsRequest {
    query?: {
        tags?: any[];
    };
    tags?: any[];
}
export interface getPetByIdRequest {
    petId: string;
    tags?: any[];
}
export interface updatePetWithFormRequest {
    petId: string;
    query?: {
        name?: string;
        status?: string;
    };
    tags?: any[];
}
export interface deletePetRequest {
    petId: string;
    headers?: {
        api_key?: string;
    };
    tags?: any[];
}
export interface uploadFileRequest {
    petId: string;
    query?: {
        additionalMetadata?: string;
    };
    body?: any;
    tags?: any[];
}
export interface getInventoryRequest {
    tags?: any[];
}
export interface placeOrderRequest {
    body?: any;
    tags?: any[];
}
export interface getOrderByIdRequest {
    orderId: string;
    tags?: any[];
}
export interface deleteOrderRequest {
    orderId: string;
    tags?: any[];
}
export interface createUserRequest {
    body?: any;
    tags?: any[];
}
export interface createUsersWithListInputRequest {
    body?: any;
    tags?: any[];
}
export interface loginUserRequest {
    query?: {
        username?: string;
        password?: string;
    };
    tags?: any[];
}
export interface logoutUserRequest {
    tags?: any[];
}
export interface getUserByNameRequest {
    username: string;
    tags?: any[];
}
export interface updateUserRequest {
    username: string;
    body?: any;
    tags?: any[];
}
export interface deleteUserRequest {
    username: string;
    tags?: any[];
}
