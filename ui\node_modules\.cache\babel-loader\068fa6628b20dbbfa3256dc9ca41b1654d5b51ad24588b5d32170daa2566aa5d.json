{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst FastForward = createLucideIcon(\"FastForward\", [[\"polygon\", {\n  points: \"13 19 22 12 13 5 13 19\",\n  key: \"587y9g\"\n}], [\"polygon\", {\n  points: \"2 19 11 12 2 5 2 19\",\n  key: \"3pweh0\"\n}]]);\nexport { FastForward as default };", "map": {"version": 3, "names": ["FastForward", "createLucideIcon", "points", "key"], "sources": ["D:\\repos-personal\\repos\\openapi-to-mcp\\ui\\node_modules\\lucide-react\\src\\icons\\fast-forward.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name FastForward\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cG9seWdvbiBwb2ludHM9IjEzIDE5IDIyIDEyIDEzIDUgMTMgMTkiIC8+CiAgPHBvbHlnb24gcG9pbnRzPSIyIDE5IDExIDEyIDIgNSAyIDE5IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/fast-forward\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst FastForward = createLucideIcon('FastForward', [\n  ['polygon', { points: '13 19 22 12 13 5 13 19', key: '587y9g' }],\n  ['polygon', { points: '2 19 11 12 2 5 2 19', key: '3pweh0' }],\n]);\n\nexport default FastForward;\n"], "mappings": ";;;;;AAaM,MAAAA,WAAA,GAAcC,gBAAA,CAAiB,aAAe,GAClD,CAAC,SAAW;EAAEC,MAAA,EAAQ,wBAA0B;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC/D,CAAC,SAAW;EAAED,MAAA,EAAQ,qBAAuB;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC7D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}