{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst Gift = createLucideIcon(\"Gift\", [[\"polyline\", {\n  points: \"20 12 20 22 4 22 4 12\",\n  key: \"nda8fc\"\n}], [\"rect\", {\n  width: \"20\",\n  height: \"5\",\n  x: \"2\",\n  y: \"7\",\n  key: \"wkgdzj\"\n}], [\"line\", {\n  x1: \"12\",\n  x2: \"12\",\n  y1: \"22\",\n  y2: \"7\",\n  key: \"1n8zgp\"\n}], [\"path\", {\n  d: \"M12 7H7.5a2.5 2.5 0 0 1 0-5C11 2 12 7 12 7z\",\n  key: \"zighg4\"\n}], [\"path\", {\n  d: \"M12 7h4.5a2.5 2.5 0 0 0 0-5C13 2 12 7 12 7z\",\n  key: \"1pa5tk\"\n}]]);\nexport { Gift as default };", "map": {"version": 3, "names": ["Gift", "createLucideIcon", "points", "key", "width", "height", "x", "y", "x1", "x2", "y1", "y2", "d"], "sources": ["D:\\repos-personal\\repos\\openapi-to-mcp\\ui\\node_modules\\lucide-react\\src\\icons\\gift.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Gift\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cG9seWxpbmUgcG9pbnRzPSIyMCAxMiAyMCAyMiA0IDIyIDQgMTIiIC8+CiAgPHJlY3Qgd2lkdGg9IjIwIiBoZWlnaHQ9IjUiIHg9IjIiIHk9IjciIC8+CiAgPGxpbmUgeDE9IjEyIiB4Mj0iMTIiIHkxPSIyMiIgeTI9IjciIC8+CiAgPHBhdGggZD0iTTEyIDdINy41YTIuNSAyLjUgMCAwIDEgMC01QzExIDIgMTIgNyAxMiA3eiIgLz4KICA8cGF0aCBkPSJNMTIgN2g0LjVhMi41IDIuNSAwIDAgMCAwLTVDMTMgMiAxMiA3IDEyIDd6IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/gift\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Gift = createLucideIcon('Gift', [\n  ['polyline', { points: '20 12 20 22 4 22 4 12', key: 'nda8fc' }],\n  ['rect', { width: '20', height: '5', x: '2', y: '7', key: 'wkgdzj' }],\n  ['line', { x1: '12', x2: '12', y1: '22', y2: '7', key: '1n8zgp' }],\n  ['path', { d: 'M12 7H7.5a2.5 2.5 0 0 1 0-5C11 2 12 7 12 7z', key: 'zighg4' }],\n  ['path', { d: 'M12 7h4.5a2.5 2.5 0 0 0 0-5C13 2 12 7 12 7z', key: '1pa5tk' }],\n]);\n\nexport default Gift;\n"], "mappings": ";;;;;AAaM,MAAAA,IAAA,GAAOC,gBAAA,CAAiB,MAAQ,GACpC,CAAC,UAAY;EAAEC,MAAA,EAAQ,uBAAyB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC/D,CAAC,QAAQ;EAAEC,KAAA,EAAO,IAAM;EAAAC,MAAA,EAAQ,GAAK;EAAAC,CAAA,EAAG,GAAK;EAAAC,CAAA,EAAG,GAAK;EAAAJ,GAAA,EAAK;AAAA,CAAU,GACpE,CAAC,QAAQ;EAAEK,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,GAAK;EAAAR,GAAA,EAAK;AAAA,CAAU,GACjE,CAAC,MAAQ;EAAES,CAAA,EAAG,6CAA+C;EAAAT,GAAA,EAAK;AAAA,CAAU,GAC5E,CAAC,MAAQ;EAAES,CAAA,EAAG,6CAA+C;EAAAT,GAAA,EAAK;AAAA,CAAU,EAC7E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}