{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst FileScan = createLucideIcon(\"FileScan\", [[\"path\", {\n  d: \"M20 10V7.5L14.5 2H6a2 2 0 0 0-2 2v16c0 1.1.9 2 2 2h4.5\",\n  key: \"uvikde\"\n}], [\"polyline\", {\n  points: \"14 2 14 8 20 8\",\n  key: \"1ew0cm\"\n}], [\"path\", {\n  d: \"M16 22a2 2 0 0 1-2-2\",\n  key: \"1wqh5n\"\n}], [\"path\", {\n  d: \"M20 22a2 2 0 0 0 2-2\",\n  key: \"1l9q4k\"\n}], [\"path\", {\n  d: \"M20 14a2 2 0 0 1 2 2\",\n  key: \"1ny6zw\"\n}], [\"path\", {\n  d: \"M16 14a2 2 0 0 0-2 2\",\n  key: \"ceaadl\"\n}]]);\nexport { FileScan as default };", "map": {"version": 3, "names": ["FileScan", "createLucideIcon", "d", "key", "points"], "sources": ["D:\\repos-personal\\repos\\openapi-to-mcp\\ui\\node_modules\\lucide-react\\src\\icons\\file-scan.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name FileScan\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjAgMTBWNy41TDE0LjUgMkg2YTIgMiAwIDAgMC0yIDJ2MTZjMCAxLjEuOSAyIDIgMmg0LjUiIC8+CiAgPHBvbHlsaW5lIHBvaW50cz0iMTQgMiAxNCA4IDIwIDgiIC8+CiAgPHBhdGggZD0iTTE2IDIyYTIgMiAwIDAgMS0yLTIiIC8+CiAgPHBhdGggZD0iTTIwIDIyYTIgMiAwIDAgMCAyLTIiIC8+CiAgPHBhdGggZD0iTTIwIDE0YTIgMiAwIDAgMSAyIDIiIC8+CiAgPHBhdGggZD0iTTE2IDE0YTIgMiAwIDAgMC0yIDIiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/file-scan\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst FileScan = createLucideIcon('FileScan', [\n  [\n    'path',\n    {\n      d: 'M20 10V7.5L14.5 2H6a2 2 0 0 0-2 2v16c0 1.1.9 2 2 2h4.5',\n      key: 'uvikde',\n    },\n  ],\n  ['polyline', { points: '14 2 14 8 20 8', key: '1ew0cm' }],\n  ['path', { d: 'M16 22a2 2 0 0 1-2-2', key: '1wqh5n' }],\n  ['path', { d: 'M20 22a2 2 0 0 0 2-2', key: '1l9q4k' }],\n  ['path', { d: 'M20 14a2 2 0 0 1 2 2', key: '1ny6zw' }],\n  ['path', { d: 'M16 14a2 2 0 0 0-2 2', key: 'ceaadl' }],\n]);\n\nexport default FileScan;\n"], "mappings": ";;;;;AAaM,MAAAA,QAAA,GAAWC,gBAAA,CAAiB,UAAY,GAC5C,CACE,QACA;EACEC,CAAG;EACHC,GAAK;AACP,EACF,EACA,CAAC,UAAY;EAAEC,MAAA,EAAQ,gBAAkB;EAAAD,GAAA,EAAK;AAAA,CAAU,GACxD,CAAC,MAAQ;EAAED,CAAA,EAAG,sBAAwB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACrD,CAAC,MAAQ;EAAED,CAAA,EAAG,sBAAwB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACrD,CAAC,MAAQ;EAAED,CAAA,EAAG,sBAAwB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACrD,CAAC,MAAQ;EAAED,CAAA,EAAG,sBAAwB;EAAAC,GAAA,EAAK;AAAA,CAAU,EACtD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}