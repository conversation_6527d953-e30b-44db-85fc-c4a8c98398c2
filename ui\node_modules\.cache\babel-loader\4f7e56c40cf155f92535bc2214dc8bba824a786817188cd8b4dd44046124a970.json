{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst AlignStartVertical = createLucideIcon(\"AlignStartVertical\", [[\"rect\", {\n  width: \"9\",\n  height: \"6\",\n  x: \"6\",\n  y: \"14\",\n  rx: \"2\",\n  key: \"lpm2y7\"\n}], [\"rect\", {\n  width: \"16\",\n  height: \"6\",\n  x: \"6\",\n  y: \"4\",\n  rx: \"2\",\n  key: \"rdj6ps\"\n}], [\"path\", {\n  d: \"M2 2v20\",\n  key: \"1ivd8o\"\n}]]);\nexport { AlignStartVertical as default };", "map": {"version": 3, "names": ["AlignStartVertical", "createLucideIcon", "width", "height", "x", "y", "rx", "key", "d"], "sources": ["D:\\repos-personal\\repos\\openapi-to-mcp\\ui\\node_modules\\lucide-react\\src\\icons\\align-start-vertical.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name AlignStartVertical\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iOSIgaGVpZ2h0PSI2IiB4PSI2IiB5PSIxNCIgcng9IjIiIC8+CiAgPHJlY3Qgd2lkdGg9IjE2IiBoZWlnaHQ9IjYiIHg9IjYiIHk9IjQiIHJ4PSIyIiAvPgogIDxwYXRoIGQ9Ik0yIDJ2MjAiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/align-start-vertical\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst AlignStartVertical = createLucideIcon('AlignStartVertical', [\n  [\n    'rect',\n    { width: '9', height: '6', x: '6', y: '14', rx: '2', key: 'lpm2y7' },\n  ],\n  [\n    'rect',\n    { width: '16', height: '6', x: '6', y: '4', rx: '2', key: 'rdj6ps' },\n  ],\n  ['path', { d: 'M2 2v20', key: '1ivd8o' }],\n]);\n\nexport default AlignStartVertical;\n"], "mappings": ";;;;;AAaM,MAAAA,kBAAA,GAAqBC,gBAAA,CAAiB,oBAAsB,GAChE,CACE,QACA;EAAEC,KAAA,EAAO,GAAK;EAAAC,MAAA,EAAQ,GAAK;EAAAC,CAAA,EAAG,GAAK;EAAAC,CAAA,EAAG,IAAM;EAAAC,EAAA,EAAI,GAAK;EAAAC,GAAA,EAAK;AAAS,EACrE,EACA,CACE,QACA;EAAEL,KAAA,EAAO,IAAM;EAAAC,MAAA,EAAQ,GAAK;EAAAC,CAAA,EAAG,GAAK;EAAAC,CAAA,EAAG,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,GAAA,EAAK;AAAS,EACrE,EACA,CAAC,MAAQ;EAAEC,CAAA,EAAG,SAAW;EAAAD,GAAA,EAAK;AAAA,CAAU,EACzC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}