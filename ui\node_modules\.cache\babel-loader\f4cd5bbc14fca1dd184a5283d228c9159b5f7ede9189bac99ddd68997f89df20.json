{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst Bath = createLucideIcon(\"Bath\", [[\"path\", {\n  d: \"M9 6 6.5 3.5a1.5 1.5 0 0 0-1-.5C4.683 3 4 3.683 4 4.5V17a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-5\",\n  key: \"1r8yf5\"\n}], [\"line\", {\n  x1: \"10\",\n  x2: \"8\",\n  y1: \"5\",\n  y2: \"7\",\n  key: \"h5g8z4\"\n}], [\"line\", {\n  x1: \"2\",\n  x2: \"22\",\n  y1: \"12\",\n  y2: \"12\",\n  key: \"1dnqot\"\n}], [\"line\", {\n  x1: \"7\",\n  x2: \"7\",\n  y1: \"19\",\n  y2: \"21\",\n  key: \"16jp00\"\n}], [\"line\", {\n  x1: \"17\",\n  x2: \"17\",\n  y1: \"19\",\n  y2: \"21\",\n  key: \"1pxrnk\"\n}]]);\nexport { Bath as default };", "map": {"version": 3, "names": ["Bath", "createLucideIcon", "d", "key", "x1", "x2", "y1", "y2"], "sources": ["D:\\repos-personal\\repos\\openapi-to-mcp\\ui\\node_modules\\lucide-react\\src\\icons\\bath.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Bath\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNOSA2IDYuNSAzLjVhMS41IDEuNSAwIDAgMC0xLS41QzQuNjgzIDMgNCAzLjY4MyA0IDQuNVYxN2EyIDIgMCAwIDAgMiAyaDEyYTIgMiAwIDAgMCAyLTJ2LTUiIC8+CiAgPGxpbmUgeDE9IjEwIiB4Mj0iOCIgeTE9IjUiIHkyPSI3IiAvPgogIDxsaW5lIHgxPSIyIiB4Mj0iMjIiIHkxPSIxMiIgeTI9IjEyIiAvPgogIDxsaW5lIHgxPSI3IiB4Mj0iNyIgeTE9IjE5IiB5Mj0iMjEiIC8+CiAgPGxpbmUgeDE9IjE3IiB4Mj0iMTciIHkxPSIxOSIgeTI9IjIxIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/bath\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Bath = createLucideIcon('Bath', [\n  [\n    'path',\n    {\n      d: 'M9 6 6.5 3.5a1.5 1.5 0 0 0-1-.5C4.683 3 4 3.683 4 4.5V17a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-5',\n      key: '1r8yf5',\n    },\n  ],\n  ['line', { x1: '10', x2: '8', y1: '5', y2: '7', key: 'h5g8z4' }],\n  ['line', { x1: '2', x2: '22', y1: '12', y2: '12', key: '1dnqot' }],\n  ['line', { x1: '7', x2: '7', y1: '19', y2: '21', key: '16jp00' }],\n  ['line', { x1: '17', x2: '17', y1: '19', y2: '21', key: '1pxrnk' }],\n]);\n\nexport default Bath;\n"], "mappings": ";;;;;AAaM,MAAAA,IAAA,GAAOC,gBAAA,CAAiB,MAAQ,GACpC,CACE,QACA;EACEC,CAAG;EACHC,GAAK;AACP,EACF,EACA,CAAC,QAAQ;EAAEC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAJ,GAAA,EAAK;AAAA,CAAU,GAC/D,CAAC,QAAQ;EAAEC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAJ,GAAA,EAAK;AAAA,CAAU,GACjE,CAAC,QAAQ;EAAEC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAJ,GAAA,EAAK;AAAA,CAAU,GAChE,CAAC,QAAQ;EAAEC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAJ,GAAA,EAAK;AAAA,CAAU,EACnE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}