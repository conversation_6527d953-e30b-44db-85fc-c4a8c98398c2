"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
/* eslint-disable @typescript-eslint/ban-ts-comment */
// @ts-ignore
const portfinder_1 = __importDefault(require("portfinder"));
const uuid_1 = require("uuid");
const serverGenerator_1 = __importDefault(require("../core/serverGenerator"));
const openapiParser_1 = __importDefault(require("../core/openapiParser"));
const bundleGenerator_1 = require("../core/bundleGenerator");
const mcpManifestGenerator_1 = require("../core/mcpManifestGenerator");
const router = express_1.default.Router();
router.post('/', async (req, res) => {
    const { openapiUrl } = req.body;
    if (!openapiUrl)
        return res.status(400).json({ error: 'Missing openapiUrl' });
    try {
        const bundleId = `${(0, uuid_1.v4)()}-${Date.now()}`;
        // Parse OpenAPI specification
        const openapiRaw = await fetch(openapiUrl).then(r => r.text());
        const parser = new openapiParser_1.default();
        const isYaml = openapiUrl.endsWith('.yaml') || openapiUrl.endsWith('.yml');
        const parsed = await parser.parseFromString(openapiRaw, isYaml);
        // Create server configuration
        const port = await portfinder_1.default.getPortPromise();
        const config = {
            name: parsed.spec.info?.title?.replace(/[^a-zA-Z0-9-]/g, '-').toLowerCase() || 'instant-mcp',
            version: parsed.spec.info?.version || '1.0.0',
            port,
            baseUrl: parsed.baseUrl || `http://localhost:${port}`,
            description: parsed.spec.info?.description || 'Instant MCP server',
            license: parsed.spec.info?.license?.name || 'MIT',
            author: parsed.spec.info?.contact?.name || 'openapi-to-mcp',
            outputDir: `/tmp/mcp-bundles/${bundleId}`
        };
        // Generate MCP manifest
        const manifestGenerator = new mcpManifestGenerator_1.MCPManifestGenerator();
        const manifest = manifestGenerator.generateManifest(parsed, config);
        // Generate server files
        const serverGen = new serverGenerator_1.default();
        const files = await serverGen.generateServer(parsed, config);
        // Create downloadable bundle
        const bundleGenerator = new bundleGenerator_1.BundleGenerator();
        const bundlePath = await bundleGenerator.createBundle(bundleId, {
            manifest,
            files,
            config
        });
        // Get bundle size for response
        const bundleSize = await bundleGenerator.getBundleSize(bundlePath);
        res.json({
            success: true,
            bundleId,
            instanceId: bundleId, // For backward compatibility
            downloadUrl: `/api/download/${bundleId}`,
            bundleSize,
            message: "MCP server generated successfully! Download the bundle to get started.",
            config: {
                name: config.name,
                version: config.version,
                port: config.port,
                baseUrl: config.baseUrl
            },
            stats: {
                endpoints: Object.keys(parsed.spec.paths || {}).length,
                tools: manifest.tools.length
            }
        });
    }
    catch (err) {
        res.status(500).json({ error: err.message || err.toString() });
    }
});
exports.default = router;
//# sourceMappingURL=instantMcp.js.map