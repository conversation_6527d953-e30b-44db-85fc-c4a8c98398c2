{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst Fish = createLucideIcon(\"Fish\", [[\"path\", {\n  d: \"M6.5 12c.94-3.46 4.94-6 8.5-6 3.56 0 6.06 2.54 7 6-.94 3.47-3.44 6-7 6s-7.56-2.53-8.5-6Z\",\n  key: \"15baut\"\n}], [\"path\", {\n  d: \"M18 12v.5\",\n  key: \"18hhni\"\n}], [\"path\", {\n  d: \"M16 17.93a9.77 9.77 0 0 1 0-11.86\",\n  key: \"16dt7o\"\n}], [\"path\", {\n  d: \"M7 10.67C7 8 5.58 5.97 2.73 5.5c-1 1.5-1 5 .23 6.5-1.24 1.5-1.24 5-.23 6.5C5.58 18.03 7 16 7 13.33\",\n  key: \"l9di03\"\n}], [\"path\", {\n  d: \"M10.46 7.26C10.2 5.88 9.17 4.24 8 3h5.8a2 2 0 0 1 1.98 1.67l.23 1.4\",\n  key: \"1kjonw\"\n}], [\"path\", {\n  d: \"m16.01 17.93-.23 1.4A2 2 0 0 1 13.8 21H9.5a5.96 5.96 0 0 0 1.49-3.98\",\n  key: \"1zlm23\"\n}]]);\nexport { Fish as default };", "map": {"version": 3, "names": ["Fish", "createLucideIcon", "d", "key"], "sources": ["D:\\repos-personal\\repos\\openapi-to-mcp\\ui\\node_modules\\lucide-react\\src\\icons\\fish.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Fish\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNi41IDEyYy45NC0zLjQ2IDQuOTQtNiA4LjUtNiAzLjU2IDAgNi4wNiAyLjU0IDcgNi0uOTQgMy40Ny0zLjQ0IDYtNyA2cy03LjU2LTIuNTMtOC41LTZaIiAvPgogIDxwYXRoIGQ9Ik0xOCAxMnYuNSIgLz4KICA8cGF0aCBkPSJNMTYgMTcuOTNhOS43NyA5Ljc3IDAgMCAxIDAtMTEuODYiIC8+CiAgPHBhdGggZD0iTTcgMTAuNjdDNyA4IDUuNTggNS45NyAyLjczIDUuNWMtMSAxLjUtMSA1IC4yMyA2LjUtMS4yNCAxLjUtMS4yNCA1LS4yMyA2LjVDNS41OCAxOC4wMyA3IDE2IDcgMTMuMzMiIC8+CiAgPHBhdGggZD0iTTEwLjQ2IDcuMjZDMTAuMiA1Ljg4IDkuMTcgNC4yNCA4IDNoNS44YTIgMiAwIDAgMSAxLjk4IDEuNjdsLjIzIDEuNCIgLz4KICA8cGF0aCBkPSJtMTYuMDEgMTcuOTMtLjIzIDEuNEEyIDIgMCAwIDEgMTMuOCAyMUg5LjVhNS45NiA1Ljk2IDAgMCAwIDEuNDktMy45OCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/fish\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Fish = createLucideIcon('Fish', [\n  [\n    'path',\n    {\n      d: 'M6.5 12c.94-3.46 4.94-6 8.5-6 3.56 0 6.06 2.54 7 6-.94 3.47-3.44 6-7 6s-7.56-2.53-8.5-6Z',\n      key: '15baut',\n    },\n  ],\n  ['path', { d: 'M18 12v.5', key: '18hhni' }],\n  ['path', { d: 'M16 17.93a9.77 9.77 0 0 1 0-11.86', key: '16dt7o' }],\n  [\n    'path',\n    {\n      d: 'M7 10.67C7 8 5.58 5.97 2.73 5.5c-1 1.5-1 5 .23 6.5-1.24 1.5-1.24 5-.23 6.5C5.58 18.03 7 16 7 13.33',\n      key: 'l9di03',\n    },\n  ],\n  [\n    'path',\n    {\n      d: 'M10.46 7.26C10.2 5.88 9.17 4.24 8 3h5.8a2 2 0 0 1 1.98 1.67l.23 1.4',\n      key: '1kjonw',\n    },\n  ],\n  [\n    'path',\n    {\n      d: 'm16.01 17.93-.23 1.4A2 2 0 0 1 13.8 21H9.5a5.96 5.96 0 0 0 1.49-3.98',\n      key: '1zlm23',\n    },\n  ],\n]);\n\nexport default Fish;\n"], "mappings": ";;;;;AAaM,MAAAA,IAAA,GAAOC,gBAAA,CAAiB,MAAQ,GACpC,CACE,QACA;EACEC,CAAG;EACHC,GAAK;AACP,EACF,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAED,CAAA,EAAG,mCAAqC;EAAAC,GAAA,EAAK;AAAA,CAAU,GAClE,CACE,QACA;EACED,CAAG;EACHC,GAAK;AACP,EACF,EACA,CACE,QACA;EACED,CAAG;EACHC,GAAK;AACP,EACF,EACA,CACE,QACA;EACED,CAAG;EACHC,GAAK;AACP,EACF,CACD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}