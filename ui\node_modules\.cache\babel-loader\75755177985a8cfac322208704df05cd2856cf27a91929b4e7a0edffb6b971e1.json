{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst AlignHorizontalJustifyStart = createLucideIcon(\"AlignHorizontalJustifyStart\", [[\"rect\", {\n  width: \"6\",\n  height: \"14\",\n  x: \"6\",\n  y: \"5\",\n  rx: \"2\",\n  key: \"hsirpf\"\n}], [\"rect\", {\n  width: \"6\",\n  height: \"10\",\n  x: \"16\",\n  y: \"7\",\n  rx: \"2\",\n  key: \"13zkjt\"\n}], [\"path\", {\n  d: \"M2 2v20\",\n  key: \"1ivd8o\"\n}]]);\nexport { AlignHorizontalJustifyStart as default };", "map": {"version": 3, "names": ["AlignHorizontalJustifyStart", "createLucideIcon", "width", "height", "x", "y", "rx", "key", "d"], "sources": ["D:\\repos-personal\\repos\\openapi-to-mcp\\ui\\node_modules\\lucide-react\\src\\icons\\align-horizontal-justify-start.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name AlignHorizontalJustifyStart\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iNiIgaGVpZ2h0PSIxNCIgeD0iNiIgeT0iNSIgcng9IjIiIC8+CiAgPHJlY3Qgd2lkdGg9IjYiIGhlaWdodD0iMTAiIHg9IjE2IiB5PSI3IiByeD0iMiIgLz4KICA8cGF0aCBkPSJNMiAydjIwIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/align-horizontal-justify-start\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst AlignHorizontalJustifyStart = createLucideIcon(\n  'AlignHorizontalJustifyStart',\n  [\n    [\n      'rect',\n      { width: '6', height: '14', x: '6', y: '5', rx: '2', key: 'hsirpf' },\n    ],\n    [\n      'rect',\n      { width: '6', height: '10', x: '16', y: '7', rx: '2', key: '13zkjt' },\n    ],\n    ['path', { d: 'M2 2v20', key: '1ivd8o' }],\n  ],\n);\n\nexport default AlignHorizontalJustifyStart;\n"], "mappings": ";;;;;AAaA,MAAMA,2BAA8B,GAAAC,gBAAA,CAClC,+BACA,CACE,CACE,QACA;EAAEC,KAAA,EAAO,GAAK;EAAAC,MAAA,EAAQ,IAAM;EAAAC,CAAA,EAAG,GAAK;EAAAC,CAAA,EAAG,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,GAAA,EAAK;AAAS,EACrE,EACA,CACE,QACA;EAAEL,KAAA,EAAO,GAAK;EAAAC,MAAA,EAAQ,IAAM;EAAAC,CAAA,EAAG,IAAM;EAAAC,CAAA,EAAG,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,GAAA,EAAK;AAAS,EACtE,EACA,CAAC,MAAQ;EAAEC,CAAA,EAAG,SAAW;EAAAD,GAAA,EAAK;AAAA,CAAU,EAE5C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}