{"name": "@types/body-parser", "version": "1.19.5", "description": "TypeScript definitions for body-parser", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/body-parser", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON>", "githubUsername": "santial<PERSON>", "url": "https://github.com/santialbo"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "vilic", "url": "https://github.com/vilic"}, {"name": "<PERSON>", "githubUsername": "dreampulse", "url": "https://github.com/dreampulse"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "blendsdk", "url": "https://github.com/blendsdk"}, {"name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/tlaziuk"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>", "url": "https://github.com/jwalton"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/body-parser"}, "scripts": {}, "dependencies": {"@types/connect": "*", "@types/node": "*"}, "typesPublisherContentHash": "7be737b78c8aabd5436be840558b283182b44c3cf9da24fb1f2ff8f414db5802", "typeScriptVersion": "4.5"}