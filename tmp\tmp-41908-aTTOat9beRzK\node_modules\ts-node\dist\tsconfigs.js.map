{"version": 3, "file": "tsconfigs.js", "sourceRoot": "", "sources": ["../src/tsconfigs.ts"], "names": [], "mappings": ";;;AAEA,MAAM,SAAS,GAAG,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;AACpE;;;;GAIG;AACH,SAAgB,oCAAoC,CAAC,EAAY;IAC/D,MAAM,UAAU,GAAG,EAAuB,CAAC;IAC3C,IAAI,SAAS,IAAI,EAAE,EAAE;QACnB,MAAM,MAAM,GAAG,OAAO,CAAC,gCAAgC,CAAC,CAAC;QACzD,IAAI,gBAAgB,CAAC,MAAM,CAAC;YAAE,OAAO,MAAM,CAAC;KAC7C;IACD,IAAI,SAAS,IAAI,EAAE,EAAE;QACnB,MAAM,MAAM,GAAG,OAAO,CAAC,gCAAgC,CAAC,CAAC;QACzD,IAAI,gBAAgB,CAAC,MAAM,CAAC;YAAE,OAAO,MAAM,CAAC;KAC7C;IACD,IAAI,SAAS,IAAI,EAAE,EAAE;QACnB,MAAM,MAAM,GAAG,OAAO,CAAC,gCAAgC,CAAC,CAAC;QACzD,IAAI,gBAAgB,CAAC,MAAM,CAAC;YAAE,OAAO,MAAM,CAAC;KAC7C;IACD,OAAO,OAAO,CAAC,gCAAgC,CAAC,CAAC;IAEjD,sFAAsF;IACtF,SAAS,gBAAgB,CAAC,MAKzB;QACC,OAAO,CACL,OAAQ,EAAE,CAAC,YAAoB,CAC7B,MAAM,CAAC,eAAe,CAAC,MAAM,CAAC,WAAW,EAAE,CAC5C,KAAK,QAAQ;YACd,UAAU,CAAC,IAAI;YACf,MAAM,CAAC,eAAe,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,UAAU,CAAC,IAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAC1E,CAAC;IACJ,CAAC;AACH,CAAC;AA/BD,oFA+BC", "sourcesContent": ["import type { TSCommon, TSInternal } from './ts-compiler-types';\n\nconst nodeMajor = parseInt(process.versions.node.split('.')[0], 10);\n/**\n * return parsed JSON of the bundled @tsconfig/bases config appropriate for the\n * running version of nodejs\n * @internal\n */\nexport function getDefaultTsconfigJsonForNodeVersion(ts: TSCommon): any {\n  const tsInternal = ts as any as TSInternal;\n  if (nodeMajor >= 16) {\n    const config = require('@tsconfig/node16/tsconfig.json');\n    if (configCompatible(config)) return config;\n  }\n  if (nodeMajor >= 14) {\n    const config = require('@tsconfig/node14/tsconfig.json');\n    if (configCompatible(config)) return config;\n  }\n  if (nodeMajor >= 12) {\n    const config = require('@tsconfig/node12/tsconfig.json');\n    if (configCompatible(config)) return config;\n  }\n  return require('@tsconfig/node10/tsconfig.json');\n\n  // Verify that tsconfig target and lib options are compatible with TypeScript compiler\n  function configCompatible(config: {\n    compilerOptions: {\n      lib: string[];\n      target: string;\n    };\n  }) {\n    return (\n      typeof (ts.ScriptTarget as any)[\n        config.compilerOptions.target.toUpperCase()\n      ] === 'number' &&\n      tsInternal.libs &&\n      config.compilerOptions.lib.every((lib) => tsInternal.libs!.includes(lib))\n    );\n  }\n}\n"]}