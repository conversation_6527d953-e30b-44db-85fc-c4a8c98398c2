{"name": "diff", "version": "4.0.2", "description": "A javascript text diff implementation.", "keywords": ["diff", "javascript"], "maintainers": ["<PERSON> <<EMAIL>> (http://incaseofstairs.com)"], "bugs": {"email": "<EMAIL>", "url": "http://github.com/kpdecker/jsdiff/issues"}, "license": "BSD-3-<PERSON><PERSON>", "repository": {"type": "git", "url": "git://github.com/kpdecker/jsdiff.git"}, "engines": {"node": ">=0.3.1"}, "main": "./lib/index.js", "module": "./lib/index.es6.js", "browser": "./dist/diff.js", "scripts": {"clean": "rm -rf lib/ dist/", "build:node": "yarn babel --out-dir lib  --source-maps=inline src", "test": "grunt"}, "devDependencies": {"@babel/cli": "^7.2.3", "@babel/core": "^7.2.2", "@babel/plugin-transform-modules-commonjs": "^7.2.0", "@babel/preset-env": "^7.2.3", "@babel/register": "^7.0.0", "babel-eslint": "^10.0.1", "babel-loader": "^8.0.5", "chai": "^4.2.0", "colors": "^1.3.3", "eslint": "^5.12.0", "grunt": "^1.0.3", "grunt-babel": "^8.0.0", "grunt-clean": "^0.4.0", "grunt-cli": "^1.3.2", "grunt-contrib-clean": "^2.0.0", "grunt-contrib-copy": "^1.0.0", "grunt-contrib-uglify": "^4.0.0", "grunt-contrib-watch": "^1.1.0", "grunt-eslint": "^21.0.0", "grunt-exec": "^3.0.0", "grunt-karma": "^3.0.1", "grunt-mocha-istanbul": "^5.0.2", "grunt-mocha-test": "^0.13.3", "grunt-webpack": "^3.1.3", "istanbul": "github:kpdecker/istanbul", "karma": "^3.1.4", "karma-chrome-launcher": "^2.2.0", "karma-mocha": "^1.3.0", "karma-mocha-reporter": "^2.0.0", "karma-sauce-launcher": "^2.0.2", "karma-sourcemap-loader": "^0.3.6", "karma-webpack": "^3.0.5", "mocha": "^5.2.0", "rollup": "^1.0.2", "rollup-plugin-babel": "^4.2.0", "semver": "^5.6.0", "webpack": "^4.28.3", "webpack-dev-server": "^3.1.14"}, "optionalDependencies": {}}