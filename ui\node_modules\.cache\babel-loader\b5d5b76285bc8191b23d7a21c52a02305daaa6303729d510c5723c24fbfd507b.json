{"ast": null, "code": "var _jsxFileName = \"D:\\\\repos-personal\\\\repos\\\\openapi-to-mcp\\\\ui\\\\src\\\\App.tsx\";\n/**\r\n * Main App component - Production-ready with all routes\r\n */\n\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport { Toaster } from 'react-hot-toast';\n\n// Components\nimport { Header } from './components/layout/Header';\n\n// Pages\nimport { LandingPage } from './pages/LandingPage';\nimport { ConversionPage } from './pages/ConversionPage';\nimport { ComingSoonPage } from './pages/ComingSoonPage';\nimport { PricingPage } from './pages/PricingPage';\n\n// Styles\nimport './App.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(Router, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gray-50\",\n      children: [/*#__PURE__*/_jsxDEV(<PERSON>er, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 25,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n        className: \"flex-1\",\n        children: /*#__PURE__*/_jsxDEV(Routes, {\n          children: [/*#__PURE__*/_jsxDEV(Route, {\n            path: \"/\",\n            element: /*#__PURE__*/_jsxDEV(LandingPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 29,\n              columnNumber: 38\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 29,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/convert\",\n            element: /*#__PURE__*/_jsxDEV(ConversionPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 32,\n              columnNumber: 45\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 32,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/pricing\",\n            element: /*#__PURE__*/_jsxDEV(PricingPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 35,\n              columnNumber: 45\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 35,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/chat\",\n            element: /*#__PURE__*/_jsxDEV(ComingSoonPage, {\n              feature: \"AI Chat Interface\",\n              description: \"Chat directly with your APIs using natural language. Ask questions, get data, and perform actions through an intuitive chat interface powered by your MCP servers.\",\n              expectedDate: \"Q1 2025\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 41,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 38,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/tools\",\n            element: /*#__PURE__*/_jsxDEV(ComingSoonPage, {\n              feature: \"MCP Tools Manager\",\n              description: \"Manage, monitor, and debug your MCP servers with our comprehensive tools dashboard. View logs, test endpoints, and optimize performance.\",\n              expectedDate: \"Q1 2025\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 51,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 48,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/playground\",\n            element: /*#__PURE__*/_jsxDEV(ComingSoonPage, {\n              feature: \"API Playground\",\n              description: \"Test and experiment with your APIs before converting them to MCP servers. Interactive documentation and testing environment.\",\n              expectedDate: \"Q2 2025\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 61,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 58,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/profile\",\n            element: /*#__PURE__*/_jsxDEV(ComingSoonPage, {\n              feature: \"User Profile\",\n              description: \"Manage your account, view conversion history, and customize your MCPify experience.\",\n              expectedDate: \"Q1 2025\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 73,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 70,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/settings\",\n            element: /*#__PURE__*/_jsxDEV(ComingSoonPage, {\n              feature: \"Settings\",\n              description: \"Configure your preferences, manage integrations, and customize your workflow.\",\n              expectedDate: \"Q1 2025\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 83,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 80,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"*\",\n            element: /*#__PURE__*/_jsxDEV(Navigate, {\n              to: \"/\",\n              replace: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 92,\n              columnNumber: 38\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 92,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 27,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Toaster, {\n        position: \"top-right\",\n        toastOptions: {\n          duration: 4000,\n          style: {\n            background: '#fff',\n            color: '#374151',\n            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)'\n          }\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 97,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 24,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 23,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Navigate", "Toaster", "Header", "LandingPage", "ConversionPage", "ComingSoonPage", "PricingPage", "jsxDEV", "_jsxDEV", "App", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "path", "element", "feature", "description", "expectedDate", "to", "replace", "position", "toastOptions", "duration", "style", "background", "color", "boxShadow", "_c", "$RefreshReg$"], "sources": ["D:/repos-personal/repos/openapi-to-mcp/ui/src/App.tsx"], "sourcesContent": ["/**\r\n * Main App component - Production-ready with all routes\r\n */\r\n\r\nimport React from 'react';\r\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\r\nimport { Toaster } from 'react-hot-toast';\r\n\r\n// Components\r\nimport { Header } from './components/layout/Header';\r\n\r\n// Pages\r\nimport { LandingPage } from './pages/LandingPage';\r\nimport { ConversionPage } from './pages/ConversionPage';\r\nimport { ComingSoonPage } from './pages/ComingSoonPage';\r\nimport { PricingPage } from './pages/PricingPage';\r\n\r\n// Styles\r\nimport './App.css';\r\n\r\nfunction App() {\r\n  return (\r\n    <Router>\r\n      <div className=\"min-h-screen bg-gray-50\">\r\n        <Header />\r\n        <main className=\"flex-1\">\r\n          <Routes>\r\n            {/* Landing page */}\r\n            <Route path=\"/\" element={<LandingPage />} />\r\n\r\n            {/* Main conversion feature */}\r\n            <Route path=\"/convert\" element={<ConversionPage />} />\r\n\r\n            {/* Pricing */}\r\n            <Route path=\"/pricing\" element={<PricingPage />} />\r\n\r\n            {/* Coming Soon pages */}\r\n            <Route\r\n              path=\"/chat\"\r\n              element={\r\n                <ComingSoonPage\r\n                  feature=\"AI Chat Interface\"\r\n                  description=\"Chat directly with your APIs using natural language. Ask questions, get data, and perform actions through an intuitive chat interface powered by your MCP servers.\"\r\n                  expectedDate=\"Q1 2025\"\r\n                />\r\n              }\r\n            />\r\n            <Route\r\n              path=\"/tools\"\r\n              element={\r\n                <ComingSoonPage\r\n                  feature=\"MCP Tools Manager\"\r\n                  description=\"Manage, monitor, and debug your MCP servers with our comprehensive tools dashboard. View logs, test endpoints, and optimize performance.\"\r\n                  expectedDate=\"Q1 2025\"\r\n                />\r\n              }\r\n            />\r\n            <Route\r\n              path=\"/playground\"\r\n              element={\r\n                <ComingSoonPage\r\n                  feature=\"API Playground\"\r\n                  description=\"Test and experiment with your APIs before converting them to MCP servers. Interactive documentation and testing environment.\"\r\n                  expectedDate=\"Q2 2025\"\r\n                />\r\n              }\r\n            />\r\n\r\n            {/* User pages (coming soon) */}\r\n            <Route\r\n              path=\"/profile\"\r\n              element={\r\n                <ComingSoonPage\r\n                  feature=\"User Profile\"\r\n                  description=\"Manage your account, view conversion history, and customize your MCPify experience.\"\r\n                  expectedDate=\"Q1 2025\"\r\n                />\r\n              }\r\n            />\r\n            <Route\r\n              path=\"/settings\"\r\n              element={\r\n                <ComingSoonPage\r\n                  feature=\"Settings\"\r\n                  description=\"Configure your preferences, manage integrations, and customize your workflow.\"\r\n                  expectedDate=\"Q1 2025\"\r\n                />\r\n              }\r\n            />\r\n\r\n            {/* Fallback */}\r\n            <Route path=\"*\" element={<Navigate to=\"/\" replace />} />\r\n          </Routes>\r\n        </main>\r\n\r\n        {/* Toast notifications */}\r\n        <Toaster\r\n          position=\"top-right\"\r\n          toastOptions={{\r\n            duration: 4000,\r\n            style: {\r\n              background: '#fff',\r\n              color: '#374151',\r\n              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',\r\n            },\r\n          }}\r\n        />\r\n      </div>\r\n    </Router>\r\n  );\r\n}\r\n\r\nexport default App;\r\n"], "mappings": ";AAAA;AACA;AACA;;AAEA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,kBAAkB;AACnF,SAASC,OAAO,QAAQ,iBAAiB;;AAEzC;AACA,SAASC,MAAM,QAAQ,4BAA4B;;AAEnD;AACA,SAASC,WAAW,QAAQ,qBAAqB;AACjD,SAASC,cAAc,QAAQ,wBAAwB;AACvD,SAASC,cAAc,QAAQ,wBAAwB;AACvD,SAASC,WAAW,QAAQ,qBAAqB;;AAEjD;AACA,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnB,SAASC,GAAGA,CAAA,EAAG;EACb,oBACED,OAAA,CAACX,MAAM;IAAAa,QAAA,eACLF,OAAA;MAAKG,SAAS,EAAC,yBAAyB;MAAAD,QAAA,gBACtCF,OAAA,CAACN,MAAM;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACVP,OAAA;QAAMG,SAAS,EAAC,QAAQ;QAAAD,QAAA,eACtBF,OAAA,CAACV,MAAM;UAAAY,QAAA,gBAELF,OAAA,CAACT,KAAK;YAACiB,IAAI,EAAC,GAAG;YAACC,OAAO,eAAET,OAAA,CAACL,WAAW;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAG5CP,OAAA,CAACT,KAAK;YAACiB,IAAI,EAAC,UAAU;YAACC,OAAO,eAAET,OAAA,CAACJ,cAAc;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAGtDP,OAAA,CAACT,KAAK;YAACiB,IAAI,EAAC,UAAU;YAACC,OAAO,eAAET,OAAA,CAACF,WAAW;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAGnDP,OAAA,CAACT,KAAK;YACJiB,IAAI,EAAC,OAAO;YACZC,OAAO,eACLT,OAAA,CAACH,cAAc;cACba,OAAO,EAAC,mBAAmB;cAC3BC,WAAW,EAAC,oKAAoK;cAChLC,YAAY,EAAC;YAAS;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB;UACF;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACFP,OAAA,CAACT,KAAK;YACJiB,IAAI,EAAC,QAAQ;YACbC,OAAO,eACLT,OAAA,CAACH,cAAc;cACba,OAAO,EAAC,mBAAmB;cAC3BC,WAAW,EAAC,0IAA0I;cACtJC,YAAY,EAAC;YAAS;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB;UACF;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACFP,OAAA,CAACT,KAAK;YACJiB,IAAI,EAAC,aAAa;YAClBC,OAAO,eACLT,OAAA,CAACH,cAAc;cACba,OAAO,EAAC,gBAAgB;cACxBC,WAAW,EAAC,8HAA8H;cAC1IC,YAAY,EAAC;YAAS;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB;UACF;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAGFP,OAAA,CAACT,KAAK;YACJiB,IAAI,EAAC,UAAU;YACfC,OAAO,eACLT,OAAA,CAACH,cAAc;cACba,OAAO,EAAC,cAAc;cACtBC,WAAW,EAAC,qFAAqF;cACjGC,YAAY,EAAC;YAAS;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB;UACF;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACFP,OAAA,CAACT,KAAK;YACJiB,IAAI,EAAC,WAAW;YAChBC,OAAO,eACLT,OAAA,CAACH,cAAc;cACba,OAAO,EAAC,UAAU;cAClBC,WAAW,EAAC,+EAA+E;cAC3FC,YAAY,EAAC;YAAS;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB;UACF;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAGFP,OAAA,CAACT,KAAK;YAACiB,IAAI,EAAC,GAAG;YAACC,OAAO,eAAET,OAAA,CAACR,QAAQ;cAACqB,EAAE,EAAC,GAAG;cAACC,OAAO;YAAA;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGPP,OAAA,CAACP,OAAO;QACNsB,QAAQ,EAAC,WAAW;QACpBC,YAAY,EAAE;UACZC,QAAQ,EAAE,IAAI;UACdC,KAAK,EAAE;YACLC,UAAU,EAAE,MAAM;YAClBC,KAAK,EAAE,SAAS;YAChBC,SAAS,EAAE;UACb;QACF;MAAE;QAAAjB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb;AAACe,EAAA,GA1FQrB,GAAG;AA4FZ,eAAeA,GAAG;AAAC,IAAAqB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}