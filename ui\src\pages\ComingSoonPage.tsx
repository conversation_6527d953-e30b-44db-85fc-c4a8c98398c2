/**
 * Coming Soon page component
 */

import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { 
  <PERSON><PERSON><PERSON>, 
  <PERSON>, 
  ArrowLeft, 
  Rocket,
  Clock,
  Star
} from 'lucide-react';

interface ComingSoonPageProps {
  feature: string;
  description: string;
  expectedDate?: string;
}

export const ComingSoonPage: React.FC<ComingSoonPageProps> = ({ 
  feature, 
  description, 
  expectedDate = "Q1 2025" 
}) => {
  const [email, setEmail] = React.useState('');
  const [isSubscribed, setIsSubscribed] = React.useState(false);

  const handleNotifyMe = (e: React.FormEvent) => {
    e.preventDefault();
    // TODO: Integrate with email service
    setIsSubscribed(true);
    setEmail('');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Back Button */}
        <Link
          to="/convert"
          className="inline-flex items-center space-x-2 text-gray-600 hover:text-gray-900 mb-8 transition-colors"
        >
          <ArrowLeft className="w-4 h-4" />
          <span>Back to Converter</span>
        </Link>

        <div className="text-center">
          {/* Icon */}
          <div className="flex justify-center mb-6">
            <div className="relative">
              <div className="w-24 h-24 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full flex items-center justify-center">
                <Sparkles className="w-12 h-12 text-white" />
              </div>
              <div className="absolute -top-2 -right-2 w-8 h-8 bg-yellow-400 rounded-full flex items-center justify-center">
                <Star className="w-4 h-4 text-yellow-800" />
              </div>
            </div>
          </div>

          {/* Title */}
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            {feature} is Coming Soon!
          </h1>

          {/* Description */}
          <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
            {description}
          </p>

          {/* Expected Date */}
          <div className="flex items-center justify-center space-x-2 mb-8">
            <Clock className="w-5 h-5 text-blue-600" />
            <span className="text-lg font-medium text-blue-600">Expected: {expectedDate}</span>
          </div>

          {/* Features Preview */}
          <div className="grid md:grid-cols-3 gap-6 mb-12">
            <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4 mx-auto">
                <Rocket className="w-6 h-6 text-blue-600" />
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">Powerful Features</h3>
              <p className="text-gray-600 text-sm">Advanced capabilities designed for professional use</p>
            </div>

            <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-4 mx-auto">
                <Sparkles className="w-6 h-6 text-purple-600" />
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">Intuitive Design</h3>
              <p className="text-gray-600 text-sm">User-friendly interface that makes complex tasks simple</p>
            </div>

            <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-4 mx-auto">
                <Star className="w-6 h-6 text-green-600" />
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">Premium Quality</h3>
              <p className="text-gray-600 text-sm">Built with attention to detail and performance</p>
            </div>
          </div>

          {/* Notify Me Form */}
          <div className="bg-white rounded-xl p-8 shadow-lg border border-gray-200 max-w-md mx-auto">
            <div className="flex items-center justify-center mb-4">
              <Bell className="w-6 h-6 text-blue-600 mr-2" />
              <h3 className="text-lg font-semibold text-gray-900">Get Notified</h3>
            </div>
            
            {isSubscribed ? (
              <div className="text-center">
                <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Star className="w-6 h-6 text-green-600" />
                </div>
                <p className="text-green-600 font-medium">Thanks! We'll notify you when it's ready.</p>
              </div>
            ) : (
              <form onSubmit={handleNotifyMe} className="space-y-4">
                <p className="text-gray-600 text-sm mb-4">
                  Be the first to know when {feature.toLowerCase()} launches
                </p>
                <div className="flex space-x-2">
                  <input
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    placeholder="Enter your email"
                    className="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    required
                  />
                  <button
                    type="submit"
                    className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-2 rounded-lg font-medium hover:from-blue-700 hover:to-purple-700 transition-all"
                  >
                    Notify Me
                  </button>
                </div>
              </form>
            )}
          </div>

          {/* CTA */}
          <div className="mt-12">
            <p className="text-gray-600 mb-4">
              In the meantime, try our API to MCP converter
            </p>
            <Link
              to="/convert"
              className="inline-flex items-center space-x-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-3 rounded-lg font-medium hover:from-blue-700 hover:to-purple-700 transition-all"
            >
              <Rocket className="w-4 h-4" />
              <span>Start Converting APIs</span>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};
