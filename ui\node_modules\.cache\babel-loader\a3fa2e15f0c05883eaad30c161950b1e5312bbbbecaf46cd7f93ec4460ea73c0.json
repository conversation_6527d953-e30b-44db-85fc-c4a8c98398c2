{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst FolderCog2 = createLucideIcon(\"FolderCog2\", [[\"path\", {\n  d: \"M4 20h16a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.93a2 2 0 0 1-1.66-.9l-.82-1.2A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13c0 1.1.9 2 2 2Z\",\n  key: \"1fr9dc\"\n}], [\"circle\", {\n  cx: \"12\",\n  cy: \"13\",\n  r: \"2\",\n  key: \"1c1ljs\"\n}], [\"path\", {\n  d: \"M12 10v1\",\n  key: \"ngorzm\"\n}], [\"path\", {\n  d: \"M12 15v1\",\n  key: \"1ovrzm\"\n}], [\"path\", {\n  d: \"m14.6 11.5-.87.5\",\n  key: \"zm6w6e\"\n}], [\"path\", {\n  d: \"m10.27 14-.87.5\",\n  key: \"idea33\"\n}], [\"path\", {\n  d: \"m14.6 14.5-.87-.5\",\n  key: \"1ii18h\"\n}], [\"path\", {\n  d: \"m10.27 12-.87-.5\",\n  key: \"tf2vd0\"\n}]]);\nexport { FolderCog2 as default };", "map": {"version": 3, "names": ["FolderCog2", "createLucideIcon", "d", "key", "cx", "cy", "r"], "sources": ["D:\\repos-personal\\repos\\openapi-to-mcp\\ui\\node_modules\\lucide-react\\src\\icons\\folder-cog-2.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name FolderCog2\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNCAyMGgxNmEyIDIgMCAwIDAgMi0yVjhhMiAyIDAgMCAwLTItMmgtNy45M2EyIDIgMCAwIDEtMS42Ni0uOWwtLjgyLTEuMkEyIDIgMCAwIDAgNy45MyAzSDRhMiAyIDAgMCAwLTIgMnYxM2MwIDEuMS45IDIgMiAyWiIgLz4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEzIiByPSIyIiAvPgogIDxwYXRoIGQ9Ik0xMiAxMHYxIiAvPgogIDxwYXRoIGQ9Ik0xMiAxNXYxIiAvPgogIDxwYXRoIGQ9Im0xNC42IDExLjUtLjg3LjUiIC8+CiAgPHBhdGggZD0ibTEwLjI3IDE0LS44Ny41IiAvPgogIDxwYXRoIGQ9Im0xNC42IDE0LjUtLjg3LS41IiAvPgogIDxwYXRoIGQ9Im0xMC4yNyAxMi0uODctLjUiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/folder-cog-2\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst FolderCog2 = createLucideIcon('FolderCog2', [\n  [\n    'path',\n    {\n      d: 'M4 20h16a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.93a2 2 0 0 1-1.66-.9l-.82-1.2A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13c0 1.1.9 2 2 2Z',\n      key: '1fr9dc',\n    },\n  ],\n  ['circle', { cx: '12', cy: '13', r: '2', key: '1c1ljs' }],\n  ['path', { d: 'M12 10v1', key: 'ngorzm' }],\n  ['path', { d: 'M12 15v1', key: '1ovrzm' }],\n  ['path', { d: 'm14.6 11.5-.87.5', key: 'zm6w6e' }],\n  ['path', { d: 'm10.27 14-.87.5', key: 'idea33' }],\n  ['path', { d: 'm14.6 14.5-.87-.5', key: '1ii18h' }],\n  ['path', { d: 'm10.27 12-.87-.5', key: 'tf2vd0' }],\n]);\n\nexport default FolderCog2;\n"], "mappings": ";;;;;AAaM,MAAAA,UAAA,GAAaC,gBAAA,CAAiB,YAAc,GAChD,CACE,QACA;EACEC,CAAG;EACHC,GAAK;AACP,EACF,EACA,CAAC,QAAU;EAAEC,EAAI;EAAMC,EAAI;EAAMC,CAAG;EAAKH,GAAK;AAAA,CAAU,GACxD,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,kBAAoB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACjD,CAAC,MAAQ;EAAED,CAAA,EAAG,iBAAmB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAChD,CAAC,MAAQ;EAAED,CAAA,EAAG,mBAAqB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAClD,CAAC,MAAQ;EAAED,CAAA,EAAG,kBAAoB;EAAAC,GAAA,EAAK;AAAA,CAAU,EAClD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}