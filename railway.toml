[build]
builder = "dockerfile"
dockerfilePath = "Dockerfile"

[deploy]
healthcheckPath = "/api/health"
healthcheckTimeout = 300
restartPolicyType = "on_failure"
restartPolicyMaxRetries = 3

[env]
NODE_ENV = "production"
PORT = { default = "3000" }
CORS_ORIGIN = { default = "*" }
MAX_FILE_SIZE = { default = "10mb" }
RATE_LIMIT_WINDOW = { default = "900000" }
RATE_LIMIT_MAX = { default = "100" }

# Optional: Database URL for future user management
# DATABASE_URL = "${{ DATABASE_URL }}"

# Optional: Redis URL for caching
# REDIS_URL = "${{ REDIS_URL }}"

# Optional: Clerk configuration for authentication
# CLERK_PUBLISHABLE_KEY = "${{ CLERK_PUBLISHABLE_KEY }}"
# CLERK_SECRET_KEY = "${{ CLERK_SECRET_KEY }}"

# Optional: Email service for notifications
# SMTP_HOST = "${{ SMTP_HOST }}"
# SMTP_PORT = "${{ SMTP_PORT }}"
# SMTP_USER = "${{ SMTP_USER }}"
# SMTP_PASS = "${{ SMTP_PASS }}"

# Optional: Analytics
# GOOGLE_ANALYTICS_ID = "${{ GOOGLE_ANALYTICS_ID }}"

# Optional: Monitoring
# SENTRY_DSN = "${{ SENTRY_DSN }}"
