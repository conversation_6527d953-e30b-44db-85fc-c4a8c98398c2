{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst ShoppingBasket = createLucideIcon(\"ShoppingBasket\", [[\"path\", {\n  d: \"m5 11 4-7\",\n  key: \"116ra9\"\n}], [\"path\", {\n  d: \"m19 11-4-7\",\n  key: \"cnml18\"\n}], [\"path\", {\n  d: \"M2 11h20\",\n  key: \"3eubbj\"\n}], [\"path\", {\n  d: \"m3.5 11 1.6 7.4a2 2 0 0 0 2 1.6h9.8c.9 0 1.8-.7 2-1.6l1.7-7.4\",\n  key: \"1x2lvw\"\n}], [\"path\", {\n  d: \"m9 11 1 9\",\n  key: \"1ojof7\"\n}], [\"path\", {\n  d: \"M4.5 15.5h15\",\n  key: \"13mye1\"\n}], [\"path\", {\n  d: \"m15 11-1 9\",\n  key: \"5wnq3a\"\n}]]);\nexport { ShoppingBasket as default };", "map": {"version": 3, "names": ["ShoppingBasket", "createLucideIcon", "d", "key"], "sources": ["D:\\repos-personal\\repos\\openapi-to-mcp\\ui\\node_modules\\lucide-react\\src\\icons\\shopping-basket.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name ShoppingBasket\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtNSAxMSA0LTciIC8+CiAgPHBhdGggZD0ibTE5IDExLTQtNyIgLz4KICA8cGF0aCBkPSJNMiAxMWgyMCIgLz4KICA8cGF0aCBkPSJtMy41IDExIDEuNiA3LjRhMiAyIDAgMCAwIDIgMS42aDkuOGMuOSAwIDEuOC0uNyAyLTEuNmwxLjctNy40IiAvPgogIDxwYXRoIGQ9Im05IDExIDEgOSIgLz4KICA8cGF0aCBkPSJNNC41IDE1LjVoMTUiIC8+CiAgPHBhdGggZD0ibTE1IDExLTEgOSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/shopping-basket\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ShoppingBasket = createLucideIcon('ShoppingBasket', [\n  ['path', { d: 'm5 11 4-7', key: '116ra9' }],\n  ['path', { d: 'm19 11-4-7', key: 'cnml18' }],\n  ['path', { d: 'M2 11h20', key: '3eubbj' }],\n  [\n    'path',\n    {\n      d: 'm3.5 11 1.6 7.4a2 2 0 0 0 2 1.6h9.8c.9 0 1.8-.7 2-1.6l1.7-7.4',\n      key: '1x2lvw',\n    },\n  ],\n  ['path', { d: 'm9 11 1 9', key: '1ojof7' }],\n  ['path', { d: 'M4.5 15.5h15', key: '13mye1' }],\n  ['path', { d: 'm15 11-1 9', key: '5wnq3a' }],\n]);\n\nexport default ShoppingBasket;\n"], "mappings": ";;;;;AAaM,MAAAA,cAAA,GAAiBC,gBAAA,CAAiB,gBAAkB,GACxD,CAAC,MAAQ;EAAEC,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAED,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CACE,QACA;EACED,CAAG;EACHC,GAAK;AACP,EACF,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAED,CAAA,EAAG,cAAgB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC7C,CAAC,MAAQ;EAAED,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC5C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}