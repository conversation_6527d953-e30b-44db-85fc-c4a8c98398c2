{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst Bot = createLucideIcon(\"Bot\", [[\"rect\", {\n  width: \"18\",\n  height: \"10\",\n  x: \"3\",\n  y: \"11\",\n  rx: \"2\",\n  key: \"1ofdy3\"\n}], [\"circle\", {\n  cx: \"12\",\n  cy: \"5\",\n  r: \"2\",\n  key: \"f1ur92\"\n}], [\"path\", {\n  d: \"M12 7v4\",\n  key: \"xawao1\"\n}], [\"line\", {\n  x1: \"8\",\n  x2: \"8\",\n  y1: \"16\",\n  y2: \"16\",\n  key: \"h6x27f\"\n}], [\"line\", {\n  x1: \"16\",\n  x2: \"16\",\n  y1: \"16\",\n  y2: \"16\",\n  key: \"5lty7f\"\n}]]);\nexport { Bot as default };", "map": {"version": 3, "names": ["Bot", "createLucideIcon", "width", "height", "x", "y", "rx", "key", "cx", "cy", "r", "d", "x1", "x2", "y1", "y2"], "sources": ["D:\\repos-personal\\repos\\openapi-to-mcp\\ui\\node_modules\\lucide-react\\src\\icons\\bot.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Bot\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMTgiIGhlaWdodD0iMTAiIHg9IjMiIHk9IjExIiByeD0iMiIgLz4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjUiIHI9IjIiIC8+CiAgPHBhdGggZD0iTTEyIDd2NCIgLz4KICA8bGluZSB4MT0iOCIgeDI9IjgiIHkxPSIxNiIgeTI9IjE2IiAvPgogIDxsaW5lIHgxPSIxNiIgeDI9IjE2IiB5MT0iMTYiIHkyPSIxNiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/bot\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Bot = createLucideIcon('Bot', [\n  [\n    'rect',\n    { width: '18', height: '10', x: '3', y: '11', rx: '2', key: '1ofdy3' },\n  ],\n  ['circle', { cx: '12', cy: '5', r: '2', key: 'f1ur92' }],\n  ['path', { d: 'M12 7v4', key: 'xawao1' }],\n  ['line', { x1: '8', x2: '8', y1: '16', y2: '16', key: 'h6x27f' }],\n  ['line', { x1: '16', x2: '16', y1: '16', y2: '16', key: '5lty7f' }],\n]);\n\nexport default Bot;\n"], "mappings": ";;;;;AAaM,MAAAA,GAAA,GAAMC,gBAAA,CAAiB,KAAO,GAClC,CACE,QACA;EAAEC,KAAA,EAAO,IAAM;EAAAC,MAAA,EAAQ,IAAM;EAAAC,CAAA,EAAG,GAAK;EAAAC,CAAA,EAAG,IAAM;EAAAC,EAAA,EAAI,GAAK;EAAAC,GAAA,EAAK;AAAS,EACvE,EACA,CAAC,QAAU;EAAEC,EAAI;EAAMC,EAAI;EAAKC,CAAG;EAAKH,GAAK;AAAA,CAAU,GACvD,CAAC,MAAQ;EAAEI,CAAA,EAAG,SAAW;EAAAJ,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,QAAQ;EAAEK,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAR,GAAA,EAAK;AAAA,CAAU,GAChE,CAAC,QAAQ;EAAEK,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAR,GAAA,EAAK;AAAA,CAAU,EACnE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}