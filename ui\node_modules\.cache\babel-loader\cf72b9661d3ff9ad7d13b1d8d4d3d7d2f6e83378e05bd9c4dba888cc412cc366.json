{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst IterationCw = createLucideIcon(\"IterationCw\", [[\"path\", {\n  d: \"M4 10c0-4.4 3.6-8 8-8s8 3.6 8 8-3.6 8-8 8H4\",\n  key: \"tuf4su\"\n}], [\"polyline\", {\n  points: \"8 22 4 18 8 14\",\n  key: \"evkj9s\"\n}]]);\nexport { IterationCw as default };", "map": {"version": 3, "names": ["IterationCw", "createLucideIcon", "d", "key", "points"], "sources": ["D:\\repos-personal\\repos\\openapi-to-mcp\\ui\\node_modules\\lucide-react\\src\\icons\\iteration-cw.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name IterationCw\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNCAxMGMwLTQuNCAzLjYtOCA4LThzOCAzLjYgOCA4LTMuNiA4LTggOEg0IiAvPgogIDxwb2x5bGluZSBwb2ludHM9IjggMjIgNCAxOCA4IDE0IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/iteration-cw\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst IterationCw = createLucideIcon('IterationCw', [\n  ['path', { d: 'M4 10c0-4.4 3.6-8 8-8s8 3.6 8 8-3.6 8-8 8H4', key: 'tuf4su' }],\n  ['polyline', { points: '8 22 4 18 8 14', key: 'evkj9s' }],\n]);\n\nexport default IterationCw;\n"], "mappings": ";;;;;AAaM,MAAAA,WAAA,GAAcC,gBAAA,CAAiB,aAAe,GAClD,CAAC,MAAQ;EAAEC,CAAA,EAAG,6CAA+C;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC5E,CAAC,UAAY;EAAEC,MAAA,EAAQ,gBAAkB;EAAAD,GAAA,EAAK;AAAA,CAAU,EACzD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}