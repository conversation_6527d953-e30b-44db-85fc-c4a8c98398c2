{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst CopyCheck = createLucideIcon(\"CopyCheck\", [[\"path\", {\n  d: \"m12 15 2 2 4-4\",\n  key: \"2c609p\"\n}], [\"rect\", {\n  width: \"14\",\n  height: \"14\",\n  x: \"8\",\n  y: \"8\",\n  rx: \"2\",\n  ry: \"2\",\n  key: \"17jyea\"\n}], [\"path\", {\n  d: \"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2\",\n  key: \"zix9uf\"\n}]]);\nexport { CopyCheck as default };", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "createLucideIcon", "d", "key", "width", "height", "x", "y", "rx", "ry"], "sources": ["D:\\repos-personal\\repos\\openapi-to-mcp\\ui\\node_modules\\lucide-react\\src\\icons\\copy-check.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name CopyCheck\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMTIgMTUgMiAyIDQtNCIgLz4KICA8cmVjdCB3aWR0aD0iMTQiIGhlaWdodD0iMTQiIHg9IjgiIHk9IjgiIHJ4PSIyIiByeT0iMiIgLz4KICA8cGF0aCBkPSJNNCAxNmMtMS4xIDAtMi0uOS0yLTJWNGMwLTEuMS45LTIgMi0yaDEwYzEuMSAwIDIgLjkgMiAyIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/copy-check\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst CopyCheck = createLucideIcon('CopyCheck', [\n  ['path', { d: 'm12 15 2 2 4-4', key: '2c609p' }],\n  [\n    'rect',\n    {\n      width: '14',\n      height: '14',\n      x: '8',\n      y: '8',\n      rx: '2',\n      ry: '2',\n      key: '17jyea',\n    },\n  ],\n  [\n    'path',\n    {\n      d: 'M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2',\n      key: 'zix9uf',\n    },\n  ],\n]);\n\nexport default CopyCheck;\n"], "mappings": ";;;;;AAaM,MAAAA,SAAA,GAAYC,gBAAA,CAAiB,WAAa,GAC9C,CAAC,MAAQ;EAAEC,CAAA,EAAG,gBAAkB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC/C,CACE,QACA;EACEC,KAAO;EACPC,MAAQ;EACRC,CAAG;EACHC,CAAG;EACHC,EAAI;EACJC,EAAI;EACJN,GAAK;AACP,EACF,EACA,CACE,QACA;EACED,CAAG;EACHC,GAAK;AACP,EACF,CACD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}