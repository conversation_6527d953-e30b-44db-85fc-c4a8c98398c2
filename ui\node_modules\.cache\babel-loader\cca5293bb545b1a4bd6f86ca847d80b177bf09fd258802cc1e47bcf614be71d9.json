{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst CircuitBoard = createLucideIcon(\"CircuitBoard\", [[\"rect\", {\n  width: \"18\",\n  height: \"18\",\n  x: \"3\",\n  y: \"3\",\n  rx: \"2\",\n  key: \"afitv7\"\n}], [\"path\", {\n  d: \"M11 9h4a2 2 0 0 0 2-2V3\",\n  key: \"1ve2rv\"\n}], [\"circle\", {\n  cx: \"9\",\n  cy: \"9\",\n  r: \"2\",\n  key: \"af1f0g\"\n}], [\"path\", {\n  d: \"M7 21v-4a2 2 0 0 1 2-2h4\",\n  key: \"1fwkro\"\n}], [\"circle\", {\n  cx: \"15\",\n  cy: \"15\",\n  r: \"2\",\n  key: \"3i40o0\"\n}]]);\nexport { CircuitBoard as default };", "map": {"version": 3, "names": ["CircuitBoard", "createLucideIcon", "width", "height", "x", "y", "rx", "key", "d", "cx", "cy", "r"], "sources": ["D:\\repos-personal\\repos\\openapi-to-mcp\\ui\\node_modules\\lucide-react\\src\\icons\\circuit-board.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name CircuitBoard\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMTgiIGhlaWdodD0iMTgiIHg9IjMiIHk9IjMiIHJ4PSIyIiAvPgogIDxwYXRoIGQ9Ik0xMSA5aDRhMiAyIDAgMCAwIDItMlYzIiAvPgogIDxjaXJjbGUgY3g9IjkiIGN5PSI5IiByPSIyIiAvPgogIDxwYXRoIGQ9Ik03IDIxdi00YTIgMiAwIDAgMSAyLTJoNCIgLz4KICA8Y2lyY2xlIGN4PSIxNSIgY3k9IjE1IiByPSIyIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/circuit-board\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst CircuitBoard = createLucideIcon('CircuitBoard', [\n  [\n    'rect',\n    { width: '18', height: '18', x: '3', y: '3', rx: '2', key: 'afitv7' },\n  ],\n  ['path', { d: 'M11 9h4a2 2 0 0 0 2-2V3', key: '1ve2rv' }],\n  ['circle', { cx: '9', cy: '9', r: '2', key: 'af1f0g' }],\n  ['path', { d: 'M7 21v-4a2 2 0 0 1 2-2h4', key: '1fwkro' }],\n  ['circle', { cx: '15', cy: '15', r: '2', key: '3i40o0' }],\n]);\n\nexport default CircuitBoard;\n"], "mappings": ";;;;;AAaM,MAAAA,YAAA,GAAeC,gBAAA,CAAiB,cAAgB,GACpD,CACE,QACA;EAAEC,KAAA,EAAO,IAAM;EAAAC,MAAA,EAAQ,IAAM;EAAAC,CAAA,EAAG,GAAK;EAAAC,CAAA,EAAG,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,GAAA,EAAK;AAAS,EACtE,EACA,CAAC,MAAQ;EAAEC,CAAA,EAAG,yBAA2B;EAAAD,GAAA,EAAK;AAAA,CAAU,GACxD,CAAC,QAAU;EAAEE,EAAI;EAAKC,EAAI;EAAKC,CAAG;EAAKJ,GAAK;AAAA,CAAU,GACtD,CAAC,MAAQ;EAAEC,CAAA,EAAG,0BAA4B;EAAAD,GAAA,EAAK;AAAA,CAAU,GACzD,CAAC,QAAU;EAAEE,EAAI;EAAMC,EAAI;EAAMC,CAAG;EAAKJ,GAAK;AAAA,CAAU,EACzD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}