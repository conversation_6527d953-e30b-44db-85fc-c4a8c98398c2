{"ast": null, "code": "var _jsxFileName = \"D:\\\\repos-personal\\\\repos\\\\openapi-to-mcp\\\\ui\\\\src\\\\pages\\\\PricingPage.tsx\";\n/**\n * Pricing Page component - Professional pricing plans\n */\n\nimport React from 'react';\nimport { Link } from 'react-router-dom';\nimport { Check, Zap, Star, ArrowRight, Shield, Headphones, Rocket } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const PricingPage = () => {\n  const plans = [{\n    name: 'Free',\n    price: '$0',\n    period: 'forever',\n    description: 'Perfect for getting started',\n    features: ['5 API conversions per month', 'Basic MCP server generation', 'Community support', 'Standard templates', 'Public repository hosting'],\n    limitations: ['No custom branding', 'Limited to 10 endpoints per API', 'Basic error handling'],\n    cta: 'Get Started',\n    ctaVariant: 'secondary',\n    popular: false\n  }, {\n    name: 'Pro',\n    price: '$29',\n    period: 'per month',\n    description: 'For professional developers',\n    features: ['Unlimited API conversions', 'Advanced MCP server generation', 'Priority support', 'Custom templates', 'Private repository hosting', 'Advanced error handling', 'Custom branding', 'Analytics dashboard', 'Team collaboration (up to 5 members)'],\n    limitations: [],\n    cta: 'Start Pro Trial',\n    ctaVariant: 'primary',\n    popular: true,\n    badge: 'Most Popular'\n  }, {\n    name: 'Enterprise',\n    price: 'Custom',\n    period: 'contact us',\n    description: 'For large teams and organizations',\n    features: ['Everything in Pro', 'Unlimited team members', 'Dedicated support', 'Custom integrations', 'On-premise deployment', 'SLA guarantees', 'Advanced security features', 'Custom training', 'White-label solution'],\n    limitations: [],\n    cta: 'Contact Sales',\n    ctaVariant: 'secondary',\n    popular: false\n  }];\n  const faqs = [{\n    question: 'What is MCP?',\n    answer: 'MCP (Model Context Protocol) is a standard that allows AI assistants to interact with external tools and APIs. Our service converts your APIs into MCP-compatible servers.'\n  }, {\n    question: 'Can I cancel anytime?',\n    answer: 'Yes, you can cancel your subscription at any time. Your access will continue until the end of your current billing period.'\n  }, {\n    question: 'Do you offer refunds?',\n    answer: 'We offer a 30-day money-back guarantee for all paid plans. If you\\'re not satisfied, we\\'ll refund your payment.'\n  }, {\n    question: 'What APIs are supported?',\n    answer: 'We support any REST API with OpenAPI 3.0+ specification. This includes most modern APIs from popular services.'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50\",\n    children: [/*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"bg-white py-16\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-4xl md:text-5xl font-bold text-gray-900 mb-6\",\n          children: \"Simple, Transparent Pricing\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xl text-gray-600 max-w-3xl mx-auto\",\n          children: \"Choose the plan that fits your needs. Start free and upgrade as you grow.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 109,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 108,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-16\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid md:grid-cols-3 gap-8\",\n          children: plans.map((plan, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `relative bg-white rounded-2xl shadow-lg border-2 transition-all hover:shadow-xl ${plan.popular ? 'border-blue-500 scale-105' : 'border-gray-200'}`,\n            children: [plan.popular && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute -top-4 left-1/2 transform -translate-x-1/2\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"bg-gradient-to-r from-blue-600 to-purple-600 text-white px-4 py-2 rounded-full text-sm font-medium flex items-center space-x-1\",\n                children: [/*#__PURE__*/_jsxDEV(Star, {\n                  className: \"w-4 h-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 133,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: plan.badge\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 134,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 132,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 131,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-8\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-2xl font-bold text-gray-900 mb-2\",\n                children: plan.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 140,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600 mb-6\",\n                children: plan.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 141,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mb-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-4xl font-bold text-gray-900\",\n                  children: plan.price\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 144,\n                  columnNumber: 21\n                }, this), plan.period && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-600 ml-2\",\n                  children: [\"/\", plan.period]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 146,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 143,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: `w-full py-3 px-6 rounded-lg font-medium transition-all mb-8 ${plan.ctaVariant === 'primary' ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white hover:from-blue-700 hover:to-purple-700' : 'bg-gray-100 text-gray-900 hover:bg-gray-200'}`,\n                children: plan.cta\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 150,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"font-semibold text-gray-900 flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(Check, {\n                    className: \"w-5 h-5 text-green-500 mr-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 162,\n                    columnNumber: 23\n                  }, this), \"What's included:\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 161,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                  className: \"space-y-3\",\n                  children: plan.features.map((feature, featureIndex) => /*#__PURE__*/_jsxDEV(\"li\", {\n                    className: \"flex items-start\",\n                    children: [/*#__PURE__*/_jsxDEV(Check, {\n                      className: \"w-5 h-5 text-green-500 mr-3 mt-0.5 flex-shrink-0\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 168,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-gray-600\",\n                      children: feature\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 169,\n                      columnNumber: 27\n                    }, this)]\n                  }, featureIndex, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 167,\n                    columnNumber: 25\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 165,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 160,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 139,\n              columnNumber: 17\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 122,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 121,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 120,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-16 bg-white\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mb-12\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-3xl font-bold text-gray-900 mb-4\",\n            children: \"Why Upgrade to Pro?\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 185,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xl text-gray-600\",\n            children: \"Unlock powerful features for professional development\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 184,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid md:grid-cols-3 gap-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6\",\n              children: /*#__PURE__*/_jsxDEV(Rocket, {\n                className: \"w-8 h-8 text-blue-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 196,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 195,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-semibold text-gray-900 mb-3\",\n              children: \"Unlimited Conversions\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 198,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600\",\n              children: \"Convert as many APIs as you need without any monthly limits\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 199,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 194,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-6\",\n              children: /*#__PURE__*/_jsxDEV(Shield, {\n                className: \"w-8 h-8 text-purple-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 204,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 203,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-semibold text-gray-900 mb-3\",\n              children: \"Advanced Security\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 206,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600\",\n              children: \"Enhanced security features and private repository hosting\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 207,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6\",\n              children: /*#__PURE__*/_jsxDEV(Headphones, {\n                className: \"w-8 h-8 text-green-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 212,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-semibold text-gray-900 mb-3\",\n              children: \"Priority Support\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600\",\n              children: \"Get help when you need it with our priority support team\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 210,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 183,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 182,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-16 bg-gray-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mb-12\",\n          children: /*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-3xl font-bold text-gray-900 mb-4\",\n            children: \"Frequently Asked Questions\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 225,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 224,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-6\",\n          children: faqs.map((faq, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg p-6 shadow-sm\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900 mb-3\",\n              children: faq.question\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 233,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600\",\n              children: faq.answer\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 17\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 223,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 222,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-16 bg-gradient-to-r from-blue-600 to-purple-600\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-3xl font-bold text-white mb-6\",\n          children: \"Ready to Get Started?\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 244,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xl text-blue-100 mb-8\",\n          children: \"Join thousands of developers already using MCPify\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 247,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/convert\",\n          className: \"inline-flex items-center space-x-2 bg-white text-blue-600 px-8 py-4 rounded-lg text-lg font-medium hover:bg-gray-100 transition-all\",\n          children: [/*#__PURE__*/_jsxDEV(Zap, {\n            className: \"w-5 h-5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Start Free Trial\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 255,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ArrowRight, {\n            className: \"w-5 h-5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 250,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 243,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 242,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 106,\n    columnNumber: 5\n  }, this);\n};\n_c = PricingPage;\nvar _c;\n$RefreshReg$(_c, \"PricingPage\");", "map": {"version": 3, "names": ["React", "Link", "Check", "Zap", "Star", "ArrowRight", "Shield", "Headphones", "Rocket", "jsxDEV", "_jsxDEV", "PricingPage", "plans", "name", "price", "period", "description", "features", "limitations", "cta", "ctaVariant", "popular", "badge", "faqs", "question", "answer", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "plan", "index", "feature", "featureIndex", "faq", "to", "_c", "$RefreshReg$"], "sources": ["D:/repos-personal/repos/openapi-to-mcp/ui/src/pages/PricingPage.tsx"], "sourcesContent": ["/**\n * Pricing Page component - Professional pricing plans\n */\n\nimport React from 'react';\nimport { Link } from 'react-router-dom';\nimport { \n  Check, \n  Crown, \n  Zap, \n  Star,\n  ArrowRight,\n  Shield,\n  Headphones,\n  Rocket\n} from 'lucide-react';\n\nexport const PricingPage: React.FC = () => {\n  const plans = [\n    {\n      name: 'Free',\n      price: '$0',\n      period: 'forever',\n      description: 'Perfect for getting started',\n      features: [\n        '5 API conversions per month',\n        'Basic MCP server generation',\n        'Community support',\n        'Standard templates',\n        'Public repository hosting'\n      ],\n      limitations: [\n        'No custom branding',\n        'Limited to 10 endpoints per API',\n        'Basic error handling'\n      ],\n      cta: 'Get Started',\n      ctaVariant: 'secondary' as const,\n      popular: false\n    },\n    {\n      name: 'Pro',\n      price: '$29',\n      period: 'per month',\n      description: 'For professional developers',\n      features: [\n        'Unlimited API conversions',\n        'Advanced MCP server generation',\n        'Priority support',\n        'Custom templates',\n        'Private repository hosting',\n        'Advanced error handling',\n        'Custom branding',\n        'Analytics dashboard',\n        'Team collaboration (up to 5 members)'\n      ],\n      limitations: [],\n      cta: 'Start Pro Trial',\n      ctaVariant: 'primary' as const,\n      popular: true,\n      badge: 'Most Popular'\n    },\n    {\n      name: 'Enterprise',\n      price: 'Custom',\n      period: 'contact us',\n      description: 'For large teams and organizations',\n      features: [\n        'Everything in Pro',\n        'Unlimited team members',\n        'Dedicated support',\n        'Custom integrations',\n        'On-premise deployment',\n        'SLA guarantees',\n        'Advanced security features',\n        'Custom training',\n        'White-label solution'\n      ],\n      limitations: [],\n      cta: 'Contact Sales',\n      ctaVariant: 'secondary' as const,\n      popular: false\n    }\n  ];\n\n  const faqs = [\n    {\n      question: 'What is MCP?',\n      answer: 'MCP (Model Context Protocol) is a standard that allows AI assistants to interact with external tools and APIs. Our service converts your APIs into MCP-compatible servers.'\n    },\n    {\n      question: 'Can I cancel anytime?',\n      answer: 'Yes, you can cancel your subscription at any time. Your access will continue until the end of your current billing period.'\n    },\n    {\n      question: 'Do you offer refunds?',\n      answer: 'We offer a 30-day money-back guarantee for all paid plans. If you\\'re not satisfied, we\\'ll refund your payment.'\n    },\n    {\n      question: 'What APIs are supported?',\n      answer: 'We support any REST API with OpenAPI 3.0+ specification. This includes most modern APIs from popular services.'\n    }\n  ];\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <section className=\"bg-white py-16\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n          <h1 className=\"text-4xl md:text-5xl font-bold text-gray-900 mb-6\">\n            Simple, Transparent Pricing\n          </h1>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n            Choose the plan that fits your needs. Start free and upgrade as you grow.\n          </p>\n        </div>\n      </section>\n\n      {/* Pricing Cards */}\n      <section className=\"py-16\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"grid md:grid-cols-3 gap-8\">\n            {plans.map((plan, index) => (\n              <div\n                key={index}\n                className={`relative bg-white rounded-2xl shadow-lg border-2 transition-all hover:shadow-xl ${\n                  plan.popular ? 'border-blue-500 scale-105' : 'border-gray-200'\n                }`}\n              >\n                {plan.popular && (\n                  <div className=\"absolute -top-4 left-1/2 transform -translate-x-1/2\">\n                    <span className=\"bg-gradient-to-r from-blue-600 to-purple-600 text-white px-4 py-2 rounded-full text-sm font-medium flex items-center space-x-1\">\n                      <Star className=\"w-4 h-4\" />\n                      <span>{plan.badge}</span>\n                    </span>\n                  </div>\n                )}\n\n                <div className=\"p-8\">\n                  <h3 className=\"text-2xl font-bold text-gray-900 mb-2\">{plan.name}</h3>\n                  <p className=\"text-gray-600 mb-6\">{plan.description}</p>\n                  \n                  <div className=\"mb-6\">\n                    <span className=\"text-4xl font-bold text-gray-900\">{plan.price}</span>\n                    {plan.period && (\n                      <span className=\"text-gray-600 ml-2\">/{plan.period}</span>\n                    )}\n                  </div>\n\n                  <button\n                    className={`w-full py-3 px-6 rounded-lg font-medium transition-all mb-8 ${\n                      plan.ctaVariant === 'primary'\n                        ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white hover:from-blue-700 hover:to-purple-700'\n                        : 'bg-gray-100 text-gray-900 hover:bg-gray-200'\n                    }`}\n                  >\n                    {plan.cta}\n                  </button>\n\n                  <div className=\"space-y-4\">\n                    <h4 className=\"font-semibold text-gray-900 flex items-center\">\n                      <Check className=\"w-5 h-5 text-green-500 mr-2\" />\n                      What's included:\n                    </h4>\n                    <ul className=\"space-y-3\">\n                      {plan.features.map((feature, featureIndex) => (\n                        <li key={featureIndex} className=\"flex items-start\">\n                          <Check className=\"w-5 h-5 text-green-500 mr-3 mt-0.5 flex-shrink-0\" />\n                          <span className=\"text-gray-600\">{feature}</span>\n                        </li>\n                      ))}\n                    </ul>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </section>\n\n      {/* Features Comparison */}\n      <section className=\"py-16 bg-white\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-12\">\n            <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">\n              Why Upgrade to Pro?\n            </h2>\n            <p className=\"text-xl text-gray-600\">\n              Unlock powerful features for professional development\n            </p>\n          </div>\n\n          <div className=\"grid md:grid-cols-3 gap-8\">\n            <div className=\"text-center\">\n              <div className=\"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6\">\n                <Rocket className=\"w-8 h-8 text-blue-600\" />\n              </div>\n              <h3 className=\"text-xl font-semibold text-gray-900 mb-3\">Unlimited Conversions</h3>\n              <p className=\"text-gray-600\">Convert as many APIs as you need without any monthly limits</p>\n            </div>\n\n            <div className=\"text-center\">\n              <div className=\"w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-6\">\n                <Shield className=\"w-8 h-8 text-purple-600\" />\n              </div>\n              <h3 className=\"text-xl font-semibold text-gray-900 mb-3\">Advanced Security</h3>\n              <p className=\"text-gray-600\">Enhanced security features and private repository hosting</p>\n            </div>\n\n            <div className=\"text-center\">\n              <div className=\"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6\">\n                <Headphones className=\"w-8 h-8 text-green-600\" />\n              </div>\n              <h3 className=\"text-xl font-semibold text-gray-900 mb-3\">Priority Support</h3>\n              <p className=\"text-gray-600\">Get help when you need it with our priority support team</p>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* FAQ */}\n      <section className=\"py-16 bg-gray-50\">\n        <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-12\">\n            <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">\n              Frequently Asked Questions\n            </h2>\n          </div>\n\n          <div className=\"space-y-6\">\n            {faqs.map((faq, index) => (\n              <div key={index} className=\"bg-white rounded-lg p-6 shadow-sm\">\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-3\">{faq.question}</h3>\n                <p className=\"text-gray-600\">{faq.answer}</p>\n              </div>\n            ))}\n          </div>\n        </div>\n      </section>\n\n      {/* CTA */}\n      <section className=\"py-16 bg-gradient-to-r from-blue-600 to-purple-600\">\n        <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n          <h2 className=\"text-3xl font-bold text-white mb-6\">\n            Ready to Get Started?\n          </h2>\n          <p className=\"text-xl text-blue-100 mb-8\">\n            Join thousands of developers already using MCPify\n          </p>\n          <Link\n            to=\"/convert\"\n            className=\"inline-flex items-center space-x-2 bg-white text-blue-600 px-8 py-4 rounded-lg text-lg font-medium hover:bg-gray-100 transition-all\"\n          >\n            <Zap className=\"w-5 h-5\" />\n            <span>Start Free Trial</span>\n            <ArrowRight className=\"w-5 h-5\" />\n          </Link>\n        </div>\n      </section>\n    </div>\n  );\n};\n"], "mappings": ";AAAA;AACA;AACA;;AAEA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SACEC,KAAK,EAELC,GAAG,EACHC,IAAI,EACJC,UAAU,EACVC,MAAM,EACNC,UAAU,EACVC,MAAM,QACD,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtB,OAAO,MAAMC,WAAqB,GAAGA,CAAA,KAAM;EACzC,MAAMC,KAAK,GAAG,CACZ;IACEC,IAAI,EAAE,MAAM;IACZC,KAAK,EAAE,IAAI;IACXC,MAAM,EAAE,SAAS;IACjBC,WAAW,EAAE,6BAA6B;IAC1CC,QAAQ,EAAE,CACR,6BAA6B,EAC7B,6BAA6B,EAC7B,mBAAmB,EACnB,oBAAoB,EACpB,2BAA2B,CAC5B;IACDC,WAAW,EAAE,CACX,oBAAoB,EACpB,iCAAiC,EACjC,sBAAsB,CACvB;IACDC,GAAG,EAAE,aAAa;IAClBC,UAAU,EAAE,WAAoB;IAChCC,OAAO,EAAE;EACX,CAAC,EACD;IACER,IAAI,EAAE,KAAK;IACXC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,WAAW;IACnBC,WAAW,EAAE,6BAA6B;IAC1CC,QAAQ,EAAE,CACR,2BAA2B,EAC3B,gCAAgC,EAChC,kBAAkB,EAClB,kBAAkB,EAClB,4BAA4B,EAC5B,yBAAyB,EACzB,iBAAiB,EACjB,qBAAqB,EACrB,sCAAsC,CACvC;IACDC,WAAW,EAAE,EAAE;IACfC,GAAG,EAAE,iBAAiB;IACtBC,UAAU,EAAE,SAAkB;IAC9BC,OAAO,EAAE,IAAI;IACbC,KAAK,EAAE;EACT,CAAC,EACD;IACET,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE,QAAQ;IACfC,MAAM,EAAE,YAAY;IACpBC,WAAW,EAAE,mCAAmC;IAChDC,QAAQ,EAAE,CACR,mBAAmB,EACnB,wBAAwB,EACxB,mBAAmB,EACnB,qBAAqB,EACrB,uBAAuB,EACvB,gBAAgB,EAChB,4BAA4B,EAC5B,iBAAiB,EACjB,sBAAsB,CACvB;IACDC,WAAW,EAAE,EAAE;IACfC,GAAG,EAAE,eAAe;IACpBC,UAAU,EAAE,WAAoB;IAChCC,OAAO,EAAE;EACX,CAAC,CACF;EAED,MAAME,IAAI,GAAG,CACX;IACEC,QAAQ,EAAE,cAAc;IACxBC,MAAM,EAAE;EACV,CAAC,EACD;IACED,QAAQ,EAAE,uBAAuB;IACjCC,MAAM,EAAE;EACV,CAAC,EACD;IACED,QAAQ,EAAE,uBAAuB;IACjCC,MAAM,EAAE;EACV,CAAC,EACD;IACED,QAAQ,EAAE,0BAA0B;IACpCC,MAAM,EAAE;EACV,CAAC,CACF;EAED,oBACEf,OAAA;IAAKgB,SAAS,EAAC,yBAAyB;IAAAC,QAAA,gBAEtCjB,OAAA;MAASgB,SAAS,EAAC,gBAAgB;MAAAC,QAAA,eACjCjB,OAAA;QAAKgB,SAAS,EAAC,oDAAoD;QAAAC,QAAA,gBACjEjB,OAAA;UAAIgB,SAAS,EAAC,mDAAmD;UAAAC,QAAA,EAAC;QAElE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLrB,OAAA;UAAGgB,SAAS,EAAC,yCAAyC;UAAAC,QAAA,EAAC;QAEvD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVrB,OAAA;MAASgB,SAAS,EAAC,OAAO;MAAAC,QAAA,eACxBjB,OAAA;QAAKgB,SAAS,EAAC,wCAAwC;QAAAC,QAAA,eACrDjB,OAAA;UAAKgB,SAAS,EAAC,2BAA2B;UAAAC,QAAA,EACvCf,KAAK,CAACoB,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBACrBxB,OAAA;YAEEgB,SAAS,EAAE,mFACTO,IAAI,CAACZ,OAAO,GAAG,2BAA2B,GAAG,iBAAiB,EAC7D;YAAAM,QAAA,GAEFM,IAAI,CAACZ,OAAO,iBACXX,OAAA;cAAKgB,SAAS,EAAC,qDAAqD;cAAAC,QAAA,eAClEjB,OAAA;gBAAMgB,SAAS,EAAC,gIAAgI;gBAAAC,QAAA,gBAC9IjB,OAAA,CAACN,IAAI;kBAACsB,SAAS,EAAC;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC5BrB,OAAA;kBAAAiB,QAAA,EAAOM,IAAI,CAACX;gBAAK;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CACN,eAEDrB,OAAA;cAAKgB,SAAS,EAAC,KAAK;cAAAC,QAAA,gBAClBjB,OAAA;gBAAIgB,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,EAAEM,IAAI,CAACpB;cAAI;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACtErB,OAAA;gBAAGgB,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAAEM,IAAI,CAACjB;cAAW;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAExDrB,OAAA;gBAAKgB,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACnBjB,OAAA;kBAAMgB,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,EAAEM,IAAI,CAACnB;gBAAK;kBAAAc,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,EACrEE,IAAI,CAAClB,MAAM,iBACVL,OAAA;kBAAMgB,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,GAAC,GAAC,EAACM,IAAI,CAAClB,MAAM;gBAAA;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAC1D;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAENrB,OAAA;gBACEgB,SAAS,EAAE,+DACTO,IAAI,CAACb,UAAU,KAAK,SAAS,GACzB,iGAAiG,GACjG,6CAA6C,EAChD;gBAAAO,QAAA,EAEFM,IAAI,CAACd;cAAG;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAETrB,OAAA;gBAAKgB,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxBjB,OAAA;kBAAIgB,SAAS,EAAC,+CAA+C;kBAAAC,QAAA,gBAC3DjB,OAAA,CAACR,KAAK;oBAACwB,SAAS,EAAC;kBAA6B;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,oBAEnD;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLrB,OAAA;kBAAIgB,SAAS,EAAC,WAAW;kBAAAC,QAAA,EACtBM,IAAI,CAAChB,QAAQ,CAACe,GAAG,CAAC,CAACG,OAAO,EAAEC,YAAY,kBACvC1B,OAAA;oBAAuBgB,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,gBACjDjB,OAAA,CAACR,KAAK;sBAACwB,SAAS,EAAC;oBAAkD;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACtErB,OAAA;sBAAMgB,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAAEQ;oBAAO;sBAAAP,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA,GAFzCK,YAAY;oBAAAR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAGjB,CACL;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA,GAjDDG,KAAK;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAkDP,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVrB,OAAA;MAASgB,SAAS,EAAC,gBAAgB;MAAAC,QAAA,eACjCjB,OAAA;QAAKgB,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrDjB,OAAA;UAAKgB,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCjB,OAAA;YAAIgB,SAAS,EAAC,uCAAuC;YAAAC,QAAA,EAAC;UAEtD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLrB,OAAA;YAAGgB,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAErC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAENrB,OAAA;UAAKgB,SAAS,EAAC,2BAA2B;UAAAC,QAAA,gBACxCjB,OAAA;YAAKgB,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BjB,OAAA;cAAKgB,SAAS,EAAC,kFAAkF;cAAAC,QAAA,eAC/FjB,OAAA,CAACF,MAAM;gBAACkB,SAAS,EAAC;cAAuB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC,CAAC,eACNrB,OAAA;cAAIgB,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAC;YAAqB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnFrB,OAAA;cAAGgB,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAA2D;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzF,CAAC,eAENrB,OAAA;YAAKgB,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BjB,OAAA;cAAKgB,SAAS,EAAC,oFAAoF;cAAAC,QAAA,eACjGjB,OAAA,CAACJ,MAAM;gBAACoB,SAAS,EAAC;cAAyB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC,eACNrB,OAAA;cAAIgB,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAC;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/ErB,OAAA;cAAGgB,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAyD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvF,CAAC,eAENrB,OAAA;YAAKgB,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BjB,OAAA;cAAKgB,SAAS,EAAC,mFAAmF;cAAAC,QAAA,eAChGjB,OAAA,CAACH,UAAU;gBAACmB,SAAS,EAAC;cAAwB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C,CAAC,eACNrB,OAAA;cAAIgB,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAC;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9ErB,OAAA;cAAGgB,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAwD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVrB,OAAA;MAASgB,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eACnCjB,OAAA;QAAKgB,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrDjB,OAAA;UAAKgB,SAAS,EAAC,mBAAmB;UAAAC,QAAA,eAChCjB,OAAA;YAAIgB,SAAS,EAAC,uCAAuC;YAAAC,QAAA,EAAC;UAEtD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAENrB,OAAA;UAAKgB,SAAS,EAAC,WAAW;UAAAC,QAAA,EACvBJ,IAAI,CAACS,GAAG,CAAC,CAACK,GAAG,EAAEH,KAAK,kBACnBxB,OAAA;YAAiBgB,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAC5DjB,OAAA;cAAIgB,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAEU,GAAG,CAACb;YAAQ;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC5ErB,OAAA;cAAGgB,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAEU,GAAG,CAACZ;YAAM;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA,GAFrCG,KAAK;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAGV,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVrB,OAAA;MAASgB,SAAS,EAAC,oDAAoD;MAAAC,QAAA,eACrEjB,OAAA;QAAKgB,SAAS,EAAC,oDAAoD;QAAAC,QAAA,gBACjEjB,OAAA;UAAIgB,SAAS,EAAC,oCAAoC;UAAAC,QAAA,EAAC;QAEnD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLrB,OAAA;UAAGgB,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EAAC;QAE1C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJrB,OAAA,CAACT,IAAI;UACHqC,EAAE,EAAC,UAAU;UACbZ,SAAS,EAAC,qIAAqI;UAAAC,QAAA,gBAE/IjB,OAAA,CAACP,GAAG;YAACuB,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC3BrB,OAAA;YAAAiB,QAAA,EAAM;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC7BrB,OAAA,CAACL,UAAU;YAACqB,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAACQ,EAAA,GApPW5B,WAAqB;AAAA,IAAA4B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}