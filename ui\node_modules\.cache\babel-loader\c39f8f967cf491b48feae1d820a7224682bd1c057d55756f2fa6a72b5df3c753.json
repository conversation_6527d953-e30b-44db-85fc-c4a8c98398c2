{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst ShieldClose = createLucideIcon(\"ShieldClose\", [[\"path\", {\n  d: \"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z\",\n  key: \"3xmgem\"\n}], [\"line\", {\n  x1: \"9.5\",\n  x2: \"14.5\",\n  y1: \"9\",\n  y2: \"14\",\n  key: \"10ywql\"\n}], [\"line\", {\n  x1: \"14.5\",\n  x2: \"9.5\",\n  y1: \"9\",\n  y2: \"14\",\n  key: \"n3a697\"\n}]]);\nexport { ShieldClose as default };", "map": {"version": 3, "names": ["ShieldClose", "createLucideIcon", "d", "key", "x1", "x2", "y1", "y2"], "sources": ["D:\\repos-personal\\repos\\openapi-to-mcp\\ui\\node_modules\\lucide-react\\src\\icons\\shield-close.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name ShieldClose\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgMjJzOC00IDgtMTBWNWwtOC0zLTggM3Y3YzAgNiA4IDEwIDggMTB6IiAvPgogIDxsaW5lIHgxPSI5LjUiIHgyPSIxNC41IiB5MT0iOSIgeTI9IjE0IiAvPgogIDxsaW5lIHgxPSIxNC41IiB4Mj0iOS41IiB5MT0iOSIgeTI9IjE0IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/shield-close\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ShieldClose = createLucideIcon('ShieldClose', [\n  ['path', { d: 'M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z', key: '3xmgem' }],\n  ['line', { x1: '9.5', x2: '14.5', y1: '9', y2: '14', key: '10ywql' }],\n  ['line', { x1: '14.5', x2: '9.5', y1: '9', y2: '14', key: 'n3a697' }],\n]);\n\nexport default ShieldClose;\n"], "mappings": ";;;;;AAaM,MAAAA,WAAA,GAAcC,gBAAA,CAAiB,aAAe,GAClD,CAAC,MAAQ;EAAEC,CAAA,EAAG,6CAA+C;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC5E,CAAC,QAAQ;EAAEC,EAAA,EAAI,KAAO;EAAAC,EAAA,EAAI,MAAQ;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,IAAM;EAAAJ,GAAA,EAAK;AAAA,CAAU,GACpE,CAAC,QAAQ;EAAEC,EAAA,EAAI,MAAQ;EAAAC,EAAA,EAAI,KAAO;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,IAAM;EAAAJ,GAAA,EAAK;AAAA,CAAU,EACrE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}