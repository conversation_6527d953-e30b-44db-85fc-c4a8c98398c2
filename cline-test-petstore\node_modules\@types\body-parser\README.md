# Installation
> `npm install --save @types/body-parser`

# Summary
This package contains type definitions for body-parser (https://github.com/expressjs/body-parser).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/body-parser.

### Additional Details
 * Last updated: Sat, 07 Jun 2025 02:15:25 GMT
 * Dependencies: [@types/connect](https://npmjs.com/package/@types/connect), [@types/node](https://npmjs.com/package/@types/node)

# Credits
These definitions were written by [<PERSON><PERSON>](https://github.com/santialbo), [<PERSON><PERSON><PERSON>](https://github.com/vilic), [<PERSON>](https://github.com/dreampulse), [<PERSON><PERSON><PERSON>](https://github.com/blendsdk), [<PERSON><PERSON>](https://github.com/tla<PERSON><PERSON>), [<PERSON>](https://github.com/j<PERSON><PERSON>), [<PERSON><PERSON><PERSON>](https://github.com/<PERSON><PERSON><PERSON><PERSON>), and [<PERSON>](https://github.com/b<PERSON>han<PERSON><PERSON>).
