"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const dotenv_1 = __importDefault(require("dotenv"));
const path = __importStar(require("path"));
const fs_1 = __importDefault(require("fs"));
// Try to load .env from both root and dist (for manual runs)
const envPathRoot = path.resolve(__dirname, '../.env');
const envPathDist = path.resolve(__dirname, '../../.env');
if (fs_1.default.existsSync(envPathRoot)) {
    dotenv_1.default.config({ path: envPathRoot });
}
else if (fs_1.default.existsSync(envPathDist)) {
    dotenv_1.default.config({ path: envPathDist });
}
else {
    dotenv_1.default.config();
}
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const helmet_1 = __importDefault(require("helmet"));
const axios_1 = __importDefault(require("axios"));
const routes_1 = require("./routes");
const app = (0, express_1.default)();
const PORT = process.env.PORT || 8001;
const BASE_URL = process.env.BASE_URL || 'http://localhost:8001';
// Security middleware
app.use((0, helmet_1.default)());
app.use((0, cors_1.default)());
// Body parsing middleware
app.use(express_1.default.json({ limit: '10mb' }));
app.use(express_1.default.urlencoded({ extended: true }));
// Health check
app.get('/health', (req, res) => {
    res.json({
        status: 'healthy',
        name: 'instant-mcp',
        version: '1.0.0',
        timestamp: new Date().toISOString(),
        baseUrl: BASE_URL
    });
});
// MCP tool endpoints
app.use('/tools', routes_1.router);
// MCP protocol handler
app.post('/mcp', async (req, res) => {
    try {
        const { tool, parameters } = req.body;
        // Route to appropriate tool handler
        const response = await fetch(`http://localhost:${PORT}/tools/${tool}`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(parameters)
        });
        const result = await response.json();
        res.json({
            success: true,
            result
        });
    }
    catch (error) {
        res.status(500).json({
            success: false,
            error: error instanceof Error ? error.message : String(error)
        });
    }
});
// LLM Chat endpoint with OpenAI function/tool calling support
app.post('/chat', async (req, res) => {
    const { message } = req.body;
    if (!message) {
        return res.status(400).json({ error: 'No message provided' });
    }
    try {
        const headers = {};
        if (process.env.LITELLM_API_KEY) {
            headers['Authorization'] = `Bearer ${process.env.LITELLM_API_KEY}`;
        }
        const llmUrl = 'https://litellm-production-744f.up.railway.app/chat/completions';
        const llmModel = 'deepseek-chat'
        // Define available tools/functions for the LLM
        const tools = [
            {
                type: "function",
                function: {
                    name: "findPets",
                    description: "Finds pets by status",
                    parameters: {
                        type: "object",
                        properties: {
                            status: {
                                type: "string",
                                description: "Status of pet (available, pending, sold)"
                            }
                        },
                        required: []
                    }
                }
            },
            {
                type: "function",
                function: {
                    name: "getPetById",
                    description: "Get a pet by its ID",
                    parameters: {
                        type: "object",
                        properties: {
                            petId: {
                                type: "integer",
                                description: "ID of the pet"
                            }
                        },
                        required: ["petId"]
                    }
                }
            }
            // Add more tool definitions here as needed
        ];
        // Compose the OpenAI/LiteLLM request with tool calling
        const payload = {
            model: llmModel,
            messages: [
                { role: 'system', content: 'You are an API assistant. Help the user interact with the API using natural language.' },
                { role: 'user', content: message }
            ],
            tools,
            tool_choice: "auto"
        };
        console.log('[MCP] LLM Chat request (with tools):', {
            url: llmUrl,
            model: llmModel,
            apiKeyPresent: !!process.env.LITELLM_API_KEY,
            headers,
            message,
            tools
        });
        const llmRes = await axios_1.default.post(llmUrl, payload, { headers });
        // Check if the LLM wants to call a function/tool
        const toolCall = llmRes.data?.choices?.[0]?.message?.tool_calls?.[0];
        if (toolCall && toolCall.function) {
            const { name, arguments: argsJson } = toolCall.function;
            let args = {};
            try {
                args = JSON.parse(argsJson);
            }
            catch (e) {
                args = {};
            }
            // Call the corresponding tool endpoint
            let toolResponse;
            if (name === "findPets") {
                // POST to /tools/findPets with { query: { status: ... } }
                const apiRes = await axios_1.default.post(`${process.env.BASE_URL || 'http://localhost:' + PORT}/tools/findPets`, { query: args });
                toolResponse = apiRes.data;
            }
            else if (name === "getPetById") {
                // POST to /tools/getPetById with { petId }
                const apiRes = await axios_1.default.post(`${process.env.BASE_URL || 'http://localhost:' + PORT}/tools/getPetById`, { petId: args.petId });
                toolResponse = apiRes.data;
            }
            else {
                toolResponse = { error: "Unknown tool called: " + name };
            }
            // Return the tool response to the user
            return res.json({ response: toolResponse });
        }
        // Otherwise, return the LLM's message as usual
        const llmMessage = llmRes.data?.choices?.[0]?.message?.content || llmRes.data?.choices?.[0]?.text || JSON.stringify(llmRes.data);
        res.json({ response: llmMessage });
    }
    catch (err) {
        console.error('[MCP] LLM call failed:', err && (err.response?.data || err.message || err.toString()), err);
        res.status(500).json({
            error: 'LLM call failed',
            details: err && (err.response?.data || err.message || err.toString())
        });
    }
});
// Error handling
app.use((error, req, res, next) => {
    console.error('Server error:', error);
    res.status(500).json({
        error: 'Internal server error',
        message: error instanceof Error ? error.message : String(error)
    });
});
// 404 handler
app.use('*', (req, res) => {
    res.status(404).json({
        error: 'Not found',
        message: `Route ${req.originalUrl} not found`
    });
});
// Start server
const server = app.listen(PORT, () => {
    console.log(`🚀 instant-mcp MCP Server running on port ${PORT}`);
    console.log(`📖 Health check: http://localhost:${PORT}/health`);
    console.log(`🔧 Base URL: ${BASE_URL}`);
});
// Graceful shutdown
process.on('SIGTERM', () => {
    console.log('SIGTERM received, shutting down gracefully');
    server.close(() => {
        console.log('Server closed');
        process.exit(0);
    });
});
exports.default = app;
//# sourceMappingURL=server.js.map