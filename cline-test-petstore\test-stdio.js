#!/usr/bin/env node

/**
 * Test script for stdio MCP communication
 * Usage: node test-stdio.js
 */

const { spawn } = require('child_process');

console.log('🧪 Testing MCP stdio communication...');

// Test data
const testRequests = [
  {
    jsonrpc: '2.0',
    id: 1,
    method: 'initialize',
    params: {
      protocolVersion: '2024-11-05',
      capabilities: {},
      clientInfo: { name: 'test-client', version: '1.0.0' }
    }
  },
  {
    jsonrpc: '2.0',
    id: 2,
    method: 'tools/list',
    params: {}
  }
];

// Spawn server in stdio mode
const server = spawn('node', ['dist/server.js'], {
  stdio: ['pipe', 'pipe', 'inherit'],
  env: { ...process.env, MCP_STDIO_MODE: 'true' }
});

let responseCount = 0;

// Handle responses
server.stdout.on('data', (data) => {
  const responses = data.toString().trim().split('\n');
  responses.forEach(response => {
    if (response.trim()) {
      try {
        const parsed = JSON.parse(response);
        console.log(`✅ Response ${++responseCount}:`, JSON.stringify(parsed, null, 2));
      } catch (error) {
        console.log(`❌ Invalid JSON:`, response);
      }
    }
  });
});

// Send test requests
testRequests.forEach((request, index) => {
  setTimeout(() => {
    console.log(`📤 Sending request ${index + 1}:`, request.method);
    server.stdin.write(JSON.stringify(request) + '\n');
  }, index * 1000);
});

// Cleanup
setTimeout(() => {
  server.kill();
  console.log('🏁 Test completed');
}, 5000);
