{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst RefreshCwOff = createLucideIcon(\"RefreshCwOff\", [[\"path\", {\n  d: \"M21 8L18.74 5.74A9.75 9.75 0 0 0 12 3C11 3 10.03 3.16 9.13 3.47\",\n  key: \"1krf6h\"\n}], [\"path\", {\n  d: \"M8 16H3v5\",\n  key: \"1cv678\"\n}], [\"path\", {\n  d: \"M3 12C3 9.51 4 7.26 5.64 5.64\",\n  key: \"ruvoct\"\n}], [\"path\", {\n  d: \"m3 16 2.26 2.26A9.75 9.75 0 0 0 12 21c2.49 0 4.74-1 6.36-2.64\",\n  key: \"19q130\"\n}], [\"path\", {\n  d: \"M21 12c0 1-.16 1.97-.47 2.87\",\n  key: \"4w8emr\"\n}], [\"path\", {\n  d: \"M21 3v5h-5\",\n  key: \"1q7to0\"\n}], [\"path\", {\n  d: \"M22 22 2 2\",\n  key: \"1r8tn9\"\n}]]);\nexport { RefreshCwOff as default };", "map": {"version": 3, "names": ["RefreshCwOff", "createLucideIcon", "d", "key"], "sources": ["D:\\repos-personal\\repos\\openapi-to-mcp\\ui\\node_modules\\lucide-react\\src\\icons\\refresh-cw-off.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name RefreshCwOff\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjEgOEwxOC43NCA1Ljc0QTkuNzUgOS43NSAwIDAgMCAxMiAzQzExIDMgMTAuMDMgMy4xNiA5LjEzIDMuNDciIC8+CiAgPHBhdGggZD0iTTggMTZIM3Y1IiAvPgogIDxwYXRoIGQ9Ik0zIDEyQzMgOS41MSA0IDcuMjYgNS42NCA1LjY0IiAvPgogIDxwYXRoIGQ9Im0zIDE2IDIuMjYgMi4yNkE5Ljc1IDkuNzUgMCAwIDAgMTIgMjFjMi40OSAwIDQuNzQtMSA2LjM2LTIuNjQiIC8+CiAgPHBhdGggZD0iTTIxIDEyYzAgMS0uMTYgMS45Ny0uNDcgMi44NyIgLz4KICA8cGF0aCBkPSJNMjEgM3Y1aC01IiAvPgogIDxwYXRoIGQ9Ik0yMiAyMiAyIDIiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/refresh-cw-off\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst RefreshCwOff = createLucideIcon('RefreshCwOff', [\n  [\n    'path',\n    {\n      d: 'M21 8L18.74 5.74A9.75 9.75 0 0 0 12 3C11 3 10.03 3.16 9.13 3.47',\n      key: '1krf6h',\n    },\n  ],\n  ['path', { d: 'M8 16H3v5', key: '1cv678' }],\n  ['path', { d: 'M3 12C3 9.51 4 7.26 5.64 5.64', key: 'ruvoct' }],\n  [\n    'path',\n    {\n      d: 'm3 16 2.26 2.26A9.75 9.75 0 0 0 12 21c2.49 0 4.74-1 6.36-2.64',\n      key: '19q130',\n    },\n  ],\n  ['path', { d: 'M21 12c0 1-.16 1.97-.47 2.87', key: '4w8emr' }],\n  ['path', { d: 'M21 3v5h-5', key: '1q7to0' }],\n  ['path', { d: 'M22 22 2 2', key: '1r8tn9' }],\n]);\n\nexport default RefreshCwOff;\n"], "mappings": ";;;;;AAaM,MAAAA,YAAA,GAAeC,gBAAA,CAAiB,cAAgB,GACpD,CACE,QACA;EACEC,CAAG;EACHC,GAAK;AACP,EACF,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAED,CAAA,EAAG,+BAAiC;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC9D,CACE,QACA;EACED,CAAG;EACHC,GAAK;AACP,EACF,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,8BAAgC;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC7D,CAAC,MAAQ;EAAED,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,MAAQ;EAAED,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC5C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}