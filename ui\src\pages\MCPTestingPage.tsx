/**
 * MCP Testing Page - Load and test existing MCP servers
 */

import React, { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { Button } from '../components/ui/Button';
import { Input } from '../components/ui/Input';
import { Card, CardHeader, CardTitle, CardContent } from '../components/ui/Card';
import { Badge } from '../components/ui/Badge';
import { TestPanel } from '../components/TestPanel';

interface MCPTool {
  name: string;
  description: string;
  inputSchema?: {
    type: string;
    properties: Record<string, any>;
    required?: string[];
  };
}

interface MCPServerInfo {
  name: string;
  version: string;
  description?: string;
  baseUrl: string;
  status: 'connected' | 'disconnected' | 'error';
}

// Helper to parse query string
function useQuery() {
  return new URLSearchParams(useLocation().search);
}

export const MCPTestingPage: React.FC = () => {
  const query = useQuery();
  const urlFromQuery = query.get('url');
  const [serverUrl, setServerUrl] = useState(urlFromQuery || 'http://localhost:3333');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [serverInfo, setServerInfo] = useState<MCPServerInfo | null>(null);
  const [tools, setTools] = useState<MCPTool[]>([]);
  const [testTool, setTestTool] = useState<MCPTool | null>(null);

  useEffect(() => {
    if (urlFromQuery) {
      setServerUrl(urlFromQuery);
      // Optionally auto-connect if url is present in query
      handleLoadServer(urlFromQuery);
    }
    // eslint-disable-next-line
  }, [urlFromQuery]);

  // Load MCP server and fetch available tools
  const handleLoadServer = async (customUrl?: string) => {
    const url = customUrl !== undefined ? customUrl : serverUrl;
    if (!url.trim()) {
      setError('Please enter a valid server URL');
      return;
    }

    setLoading(true);
    setError(null);
    setServerInfo(null);
    setTools([]);

    try {
      // First, try to get server health/info
      let healthUrl = url;
      if (!healthUrl.endsWith('/')) healthUrl += '/';
      healthUrl += 'health';

      const healthResponse = await fetch(healthUrl);
      if (!healthResponse.ok) {
        throw new Error(`Server health check failed: ${healthResponse.status}`);
      }

      const healthData = await healthResponse.json();
      
      // Set server info
      setServerInfo({
        name: healthData.name || 'Unknown MCP Server',
        version: healthData.version || '1.0.0',
        description: healthData.description,
        baseUrl: url,
        status: 'connected'
      });

      // Now try to get available tools
      let toolsUrl = url;
      if (!toolsUrl.endsWith('/')) toolsUrl += '/';
      toolsUrl += 'tools';

      const toolsResponse = await fetch(toolsUrl);
      if (!toolsResponse.ok) {
        throw new Error(`Failed to fetch tools: ${toolsResponse.status}`);
      }

      const toolsData = await toolsResponse.json();
      
      // Handle different response formats
      let toolsList: MCPTool[] = [];
      if (toolsData.tools && Array.isArray(toolsData.tools)) {
        toolsList = toolsData.tools;
      } else if (Array.isArray(toolsData)) {
        toolsList = toolsData;
      } else {
        throw new Error('Invalid tools response format');
      }

      // Flatten tools if wrapped in { type, function }
      toolsList = toolsList.map((tool: any) =>
        tool.function
          ? {
              name: tool.function.name,
              description: tool.function.description,
              inputSchema: tool.function.parameters
                ? {
                    type: tool.function.parameters.type,
                    properties: tool.function.parameters.properties,
                    required: tool.function.parameters.required,
                  }
                : undefined,
            }
          : tool
      );

      setTools(toolsList);

    } catch (err: any) {
      console.error('Error loading MCP server:', err);
      setError(err.message || 'Failed to load MCP server');
      setServerInfo(prev => prev ? { ...prev, status: 'error' } : null);
    } finally {
      setLoading(false);
    }
  };

  const handleTestTool = (tool: MCPTool) => {
    setTestTool(tool);
  };

  const handleCloseTest = () => {
    setTestTool(null);
  };

  return (
    <div className="p-6">
      <div className="max-w-6xl mx-auto">
        <div className="mb-6">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">MCP Server Testing</h1>
          <p className="text-gray-600">
            Load and test any existing MCP server by providing its URL. Test individual tools with a Postman-like interface.
          </p>
        </div>

        {/* Server Connection */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Connect to MCP Server</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex gap-4 items-end">
              <div className="flex-1">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  MCP Server URL
                </label>
                <Input
                  type="url"
                  value={serverUrl}
                  onChange={(e) => setServerUrl(e.target.value)}
                  placeholder="http://localhost:3333"
                  disabled={loading}
                />
              </div>
              <Button 
                onClick={() => handleLoadServer()}
                disabled={loading || !serverUrl.trim()}
                loading={loading}
              >
                {loading ? 'Connecting...' : 'Connect'}
              </Button>
            </div>

            {error && (
              <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-md">
                <p className="text-sm text-red-700">{error}</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Server Info */}
        {serverInfo && (
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                Server Information
                <Badge 
                  variant={
                    serverInfo.status === 'connected' ? 'default' : 
                    serverInfo.status === 'error' ? 'destructive' : 'secondary'
                  }
                >
                  {serverInfo.status}
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-500">Name</label>
                  <p className="text-gray-900">{serverInfo.name}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Version</label>
                  <p className="text-gray-900">{serverInfo.version}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Base URL</label>
                  <p className="text-gray-900 break-all">{serverInfo.baseUrl}</p>
                </div>
                {serverInfo.description && (
                  <div className="md:col-span-2 lg:col-span-3">
                    <label className="text-sm font-medium text-gray-500">Description</label>
                    <p className="text-gray-900">{serverInfo.description}</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Available Tools */}
        {tools.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle>
                Available Tools ({tools.length})
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Tool Name
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Description
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Parameters
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {tools.map((tool, index) => (
                      <tr key={tool.name || index} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900">
                            {tool.name}
                          </div>
                        </td>
                        <td className="px-6 py-4">
                          <div className="text-sm text-gray-500 max-w-xs truncate">
                            {tool.description || 'No description available'}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-500">
                            {tool.inputSchema?.properties ? (
                              <Badge variant="outline">
                                {Object.keys(tool.inputSchema.properties).length} params
                              </Badge>
                            ) : (
                              <Badge variant="secondary">No params</Badge>
                            )}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <Button
                            onClick={() => handleTestTool(tool)}
                            size="sm"
                            variant="outline"
                          >
                            Test
                          </Button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        )}

        {/* No tools message */}
        {serverInfo && serverInfo.status === 'connected' && tools.length === 0 && !loading && (
          <Card>
            <CardContent className="text-center py-8">
              <p className="text-gray-500">No tools found on this MCP server.</p>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Test Panel Modal */}
      {testTool && serverInfo && (
        <TestPanel
          tool={testTool}
          serverConfig={{
            port: parseInt(new URL(serverInfo.baseUrl).port) || 80,
            baseUrl: serverInfo.baseUrl
          }}
          onClose={handleCloseTest}
        />
      )}
    </div>
  );
};
