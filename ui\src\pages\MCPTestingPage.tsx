/**
 * MCP Testing Page - Load and test existing MCP servers
 */

import React, { useState } from 'react';
import { Button } from '../components/ui/Button';
import { Input } from '../components/ui/Input';
import { <PERSON>, CardHeader, CardTitle, CardContent } from '../components/ui/Card';
import { Badge } from '../components/ui/Badge';
import { TestPanel } from '../components/TestPanel';

interface MCPTool {
  name: string;
  description: string;;
  inputSchema?: {
    type: string;
    properties: Record<string, any>;;
    required?: string[];
  };
} operationId?: string;
  summary?: string;
interface MCPServerInfo {
  name: string;g;
  version: string;tring;
  description?: string;
  baseUrl: string;
  status: 'connected' | 'disconnected' | 'error';
}
interface MCPServerInfo {
export const MCPTestingPage: React.FC = () => {
  const [serverUrl, setServerUrl] = useState('http://localhost:3333');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [serverInfo, setServerInfo] = useState<MCPServerInfo | null>(null);
  const [tools, setTools] = useState<MCPTool[]>([]);
  const [testTool, setTestTool] = useState<MCPTool | null>(null);
export const MCPTestingPage: React.FC = () => {
  // Load MCP server and fetch available toolshttp://localhost:3333');
  const handleLoadServer = async () => {(false);
    if (!serverUrl.trim()) {useState<string | null>(null);
      setError('Please enter a valid server URL');ServerInfo | null>(null);
      return;s, setTools] = useState<MCPTool[]>([]);
    }st [testTool, setTestTool] = useState<MCPTool | null>(null);

    setLoading(true);and fetch available tools
    setError(null);erver = async () => {
    setServerInfo(null);)) {
    setTools([]);lease enter a valid server URL');
      return;
    try {
      // First, try to get server health/info
      let healthUrl = serverUrl;
      if (!healthUrl.endsWith('/')) healthUrl += '/';
      healthUrl += 'health';
    setTools([]);
      const healthResponse = await fetch(healthUrl);
      if (!healthResponse.ok) {
        throw new Error(`Server health check failed: ${healthResponse.status}`);
      }et healthUrl = serverUrl;
      if (!healthUrl.endsWith('/')) healthUrl += '/';
      const healthData = await healthResponse.json();
      
      // Set server infose = await fetch(healthUrl);
      setServerInfo({onse.ok) {
        name: healthData.name || 'Unknown MCP Server',{healthResponse.status}`);
        version: healthData.version || '1.0.0',
        description: healthData.description,
        baseUrl: serverUrl,ait healthResponse.json();
        status: 'connected'
      });Set server info
      setServerInfo({
      // Now try to get available toolswn MCP Server',
      let toolsUrl = serverUrl;sion || '1.0.0',
      if (!toolsUrl.endsWith('/')) toolsUrl += '/';
      toolsUrl += 'tools';,
        status: 'connected'
      const toolsResponse = await fetch(toolsUrl);
      if (!toolsResponse.ok) {
        throw new Error(`Failed to fetch tools: ${toolsResponse.status}`);
      }      const toolsData = await toolsResponse.json();
      console.log('Raw tools data:', toolsData);
      const toolsData = await toolsResponse.json();
      // Handle different response formats
      // Handle different response formats
      let toolsList: MCPTool[] = [];sArray(toolsData.tools)) {
      if (toolsData.tools && Array.isArray(toolsData.tools)) {
        // Transform OpenAI function-calling format if presentrmatmap((tool: any) => {
        toolsList = toolsData.tools.map((tool: any) => {l: any) => {ol.function) {
          if (tool.function) {unction' && tool.function) {
            return {turn {name: tool.function.name,
              name: tool.function.name,
              description: tool.function.description,       description: tool.function.description,       inputSchema: tool.function.parameters
              inputSchema: tool.function.parameters,              inputSchema: tool.function.parameters            };
            };
          }          }          // Handle direct format
          return tool;r other formats
        });
      } else if (Array.isArray(toolsData)) {
        toolsList = toolsData.map((tool: any) => {
          if (tool.function) {st = toolsData;
            return {ools response format');
              name: tool.function.name,   throw new Error('Invalid tools response format'); }
              description: tool.function.description,  }
              inputSchema: tool.function.parameters,      console.log('Transformed tools:', toolsList);
            };ction.name,
          }
          return tool; {'object',
        });  console.error('Error loading MCP server:', err);            properties: {},
      } else {      setError(err.message || 'Failed to load MCP server');                required: []
        throw new Error('Invalid tools response format');? { ...prev, status: 'error' } : null);
      }
  setLoading(false);      } else if (tool.name) {
      setTools(toolsList);    }            // Direct format
rn {
    } catch (err: any) {
      console.error('Error loading MCP server:', err);=> {on || tool.name,
      setError(err.message || 'Failed to load MCP server');', tool);l.inputSchema || {
      setServerInfo(prev => prev ? { ...prev, status: 'error' } : null);
    } finally {
      setLoading(false);
    }CloseTest = () => {}
  };l(null);
  };          } else {
  const handleTestTool = (tool: MCPTool) => {
    console.log('Testing tool:', tool);
    setTestTool(tool);6">nknown Tool',
  };
"mb-6">ma: {
  const handleCloseTest = () => {="text-3xl font-bold text-gray-900 mb-2">MCP Server Testing</h1>object',
    setTestTool(null);
  }; MCP server by providing its URL. Test individual tools with a Postman-like interface.

  return (
    <div className="p-6">
      <div className="max-w-6xl mx-auto">nection */}
        <div className="mb-6">6">ay(toolsData)) {
          <h1 className="text-3xl font-bold text-gray-900 mb-2">MCP Server Testing</h1>
          <p className="text-gray-600">
            Load and test any existing MCP server by providing its URL. Test individual tools with a Postman-like interface.
          </p>ame || 'No description',
        </div>lassName="flex gap-4 items-end">ema: tool.inputSchema || tool.parameters || {
lassName="flex-1">bject',
        {/* Server Connection */} className="block text-sm font-medium text-gray-700 mb-1">: {},
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Connect to MCP Server</CardTitle>
          </CardHeader>   type="url"
          <CardContent>
            <div className="flex gap-4 items-end">nge={(e) => setServerUrl(e.target.value)}
              <div className="flex-1">placeholder="http://localhost:3333"
                <label className="block text-sm font-medium text-gray-700 mb-1">                  disabled={loading}      console.log('Transformed tools:', toolsList);
                  MCP Server URL
                </label>
                <Input
                  type="url"ick={handleLoadServer}ool;
                  value={serverUrl}  disabled={loading || !serverUrl.trim()}se {
                  onChange={(e) => setServerUrl(e.target.value)}{loading}format
                  placeholder="http://localhost:3333"urn {
                  disabled={loading}                {loading ? 'Connecting...' : 'Connect'}              name: tool.name || 'Unknown Tool',
                />.description || 'No description available',
              </div>l.inputSchema || tool.parameters || { type: 'object', properties: {} }
              <Button 
                onClick={handleLoadServer}
                disabled={loading || !serverUrl.trim()}r-red-200 rounded-md">
                loading={loading}-sm text-red-700">{error}</p>lsData)) {
              >ata;
                {loading ? 'Connecting...' : 'Connect'}
              </Button>
            </div>

            {error && (r Info */}('Transformed tools:', toolsList);
              <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-md">s
                <p className="text-sm text-red-700">{error}</p>e="mb-6">
              </div>
            )}className="flex items-center gap-2">t format - already has name, description, inputSchema
          </CardContent>formation{
        </Card>
iant={ool.name,
        {/* Server Info */}
        {serverInfo && ('secondary'type: 'object', properties: {} }
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                Server Information
                <Badge tle>.name || 'Unknown Tool',
                  variant={der>on: tool.description || 'No description available',
                    serverInfo.status === 'connected' ? 'default' : 
                    serverInfo.status === 'error' ? 'destructive' : 'secondary'4">
                  }
                >font-medium text-gray-500">Name</label>
                  {serverInfo.status}}</p>
                </Badge>
              </CardTitle>
            </CardHeader> className="text-sm font-medium text-gray-500">Version</label>formed tools:', toolsList);       description: tool.function.description,
            <CardContent><p className="text-gray-900">{serverInfo.version}</p>tSchema: tool.function.parameters
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">v>
                <div>
                  <label className="text-sm font-medium text-gray-500">Name</label> <label className="text-sm font-medium text-gray-500">Base URL</label>le direct MCP format
                  <p className="text-gray-900">{serverInfo.name}</p>        <p className="text-gray-900 break-all">{serverInfo.baseUrl}</p>else if (tool.name) {
                </div>                </div>            return {
                <div>cription && (
                  <label className="text-sm font-medium text-gray-500">Version</label>me="md:col-span-2 lg:col-span-3">l.description || 'No description available',
                  <p className="text-gray-900">{serverInfo.version}</p>    <label className="text-sm font-medium text-gray-500">Description</label>putSchema: tool.inputSchema || tool.parameters
                </div>lassName="text-gray-900">{serverInfo.description}</p>
                <div>
                  <label className="text-sm font-medium text-gray-500">Base URL</label>
                  <p className="text-gray-900 break-all">{serverInfo.baseUrl}</p>
                </div>>'Unknown tool format:', tool);
                {serverInfo.description && (
                  <div className="md:col-span-2 lg:col-span-3">
                    <label className="text-sm font-medium text-gray-500">Description</label>
                    <p className="text-gray-900">{serverInfo.description}</p>
                  </div>0 && (
                )}
              </div>
            </CardContent>y(toolsData)) {
          </Card>
        )}d || 'Unknown Tool',
escription || 'No description available',
        {/* Available Tools */}
        {tools.length > 0 && (flow-x-auto">
          <Card>sName="min-w-full divide-y divide-gray-200">
            <CardHeader>
              <CardTitle>
                Available Tools ({tools.length})lassName="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              </CardTitle>ool Name tools:', toolsList);
            </CardHeader>>rray(toolsData)) {
            <CardContent>edium text-gray-500 uppercase tracking-wider">
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">t-medium text-gray-500 uppercase tracking-wider">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Tool Nameame="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">(tool: any) => {
                      </th>nsng tool:', tool);
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Description
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">me="bg-white divide-y divide-gray-200">rmat
                        Parametersp((tool, index) => (
                      </th>bg-gray-50">
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">p">
                        Actions text-gray-900">
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">Name="px-6 py-4">
                    {tools.map((tool, index) => (runcate">
                      <tr key={tool.name || index} className="hover:bg-gray-50">ool.description || 'No description available'}ry to normalize
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900">| tool.operationId || 'Unknown Tool',
                            {tool.name}
                          </div>assName="text-sm text-gray-500">utSchema || tool.parameters || { type: 'object', properties: {} }
                        </td>
                        <td className="px-6 py-4">variant="outline">
                          <div className="text-sm text-gray-500 max-w-xs truncate">tool.inputSchema.properties).length} params
                            {tool.description || 'No description available'}   </Badge>
                          </div>(ols:', toolsList);
                        </td>e variant="secondary">No params</Badge>
                        <td className="px-6 py-4 whitespace-nowrap">}
                          <div className="text-sm text-gray-500">/div>
                            {tool.inputSchema?.properties ? ( </td>ror loading MCP server:', err);
                              <Badge variant="outline">d className="px-6 py-4 whitespace-nowrap text-sm font-medium"> || 'Failed to load MCP server');
                                {Object.keys(tool.inputSchema.properties).length} params  <Button => prev ? { ...prev, status: 'error' } : null);
                              </Badge>        onClick={() => handleTestTool(tool)}
                            ) : (  size="sm"
                              <Badge variant="secondary">No params</Badge>           variant="outline"
                            )}                >
                          </div>                            Test
                        </td>on>MCPTool) => {
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <Button      </tr>tool);
                            onClick={() => handleTestTool(tool)}
                            size="sm"
                            variant="outline" => {
                          >iv>ull);
                            Test  </CardContent>
                          </Button>Card>
                        </td>        )}  return (
                      </tr>
                    ))}uto">
                  </tbody>o && serverInfo.status === 'connected' && tools.length === 0 && !loading && (Name="mb-6">
                </table>font-bold text-gray-900 mb-2">MCP Server Testing</h1>
              </div>className="text-center py-8">ext-gray-600">
            </CardContent>CP server.</p>URL. Test individual tools with a Postman-like interface.
          </Card>
        )}Card>v>

        {/* No tools message */}v>*       {/* Test Panel Modal */}
        {serverInfo && serverInfo.status === 'connected' && tools.length === 0 && !loading && (&& serverInfo && (
          <Card>Test Panel Modal */}estPanel
            <CardContent className="text-center py-8">  {testTool && serverInfo && (      tool={testTool}
              <p className="text-gray-500">No tools found on this MCP server.</p>      <TestPanel        serverConfig={{
            </CardContent>          tool={testTool}            port: parseInt(new URL(serverInfo.baseUrl).port) || 80,



















};  );    </div>      )}        />          onClose={handleCloseTest}          }}            baseUrl: serverInfo.baseUrl            port: parseInt(new URL(serverInfo.baseUrl).port) || 80,          serverConfig={{          tool={testTool}        <TestPanel      {testTool && serverInfo && (      {/* Test Panel Modal */}      </div>        )}          </Card>










};  );    </div>      )}        />          onClose={handleCloseTest}          }}            baseUrl: serverInfo.baseUrl            port: parseInt(new URL(serverInfo.baseUrl).port) || 80,          serverConfig={{            baseUrl: serverInfo.baseUrl,
            url: serverInfo.baseUrl
          }}
          onClose={handleCloseTest}
        />
      )}          type="url"
                  value={serverUrl}
                  onChange={(e) => setServerUrl(e.target.value)}
                  placeholder="http://localhost:3333"
                  disabled={loading}
                />
              </div>
              <Button 
                onClick={handleLoadServer}
                disabled={loading || !serverUrl.trim()}
                loading={loading}
              >
                {loading ? 'Connecting...' : 'Connect'}
              </Button>
            </div>

            {error && (
              <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-md">
                <p className="text-sm text-red-700">{error}</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Server Info */}
        {serverInfo && (
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                Server Information
                <Badge 
                  variant={
                    serverInfo.status === 'connected' ? 'default' : 
                    serverInfo.status === 'error' ? 'destructive' : 'secondary'
                  }
                >
                  {serverInfo.status}
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-500">Name</label>
                  <p className="text-gray-900">{serverInfo.name}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Version</label>
                  <p className="text-gray-900">{serverInfo.version}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Base URL</label>
                  <p className="text-gray-900 break-all">{serverInfo.baseUrl}</p>
                </div>
                {serverInfo.description && (
                  <div className="md:col-span-2 lg:col-span-3">
                    <label className="text-sm font-medium text-gray-500">Description</label>
                    <p className="text-gray-900">{serverInfo.description}</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Available Tools */}
        {tools.length > 0 && (
          <Card>
            <CardHeader>                      <tr key={tool.name || index} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900">
                            {tool.name || `Tool ${index + 1}`}
                          </div>
                          {tool.operationId && tool.operationId !== tool.name && (
                            <div className="text-xs text-gray-500">
                              ID: {tool.operationId}
                            </div>
                          )}
                        </td>
                        <td className="px-6 py-4">
                          <div className="text-sm text-gray-500 max-w-xs">
                            {tool.description || 'No description available'}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-500">
                            {tool.inputSchema?.properties ? (
                              <Badge variant="outline">
                                {Object.keys(tool.inputSchema.properties).length} params
                              </Badge>
                            ) : tool.inputSchema ? (
                              <Badge variant="secondary">Schema available</Badge>
                            ) : (
                              <Badge variant="secondary">No params</Badge>
                            )}
                          </div>
                        </td>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {tools.map((tool, index) => (
                      <tr key={tool.name || index} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900">
                            {tool.name}
                          </div>
                        </td>
                        <td className="px-6 py-4">
                          <div className="text-sm text-gray-500 max-w-xs truncate">
                            {tool.description || 'No description available'}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-500">
                            {tool.inputSchema?.properties ? (
                              <Badge variant="outline">
                                {Object.keys(tool.inputSchema.properties).length} params
                              </Badge>
                            ) : (
                              <Badge variant="secondary">No params</Badge>
                            )}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <Button
                            onClick={() => handleTestTool(tool)}
                            size="sm"
                            variant="outline"
                          >
                            Test
                          </Button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        )}

        {/* No tools message */}
        {serverInfo && serverInfo.status === 'connected' && tools.length === 0 && !loading && (
          <Card>
            <CardContent className="text-center py-8">
              <p className="text-gray-500">No tools found on this MCP server.</p>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Test Panel Modal */}
      {testTool && serverInfo && (
        <TestPanel
          tool={testTool}
          serverConfig={{
            port: parseInt(new URL(serverInfo.baseUrl).port) || 80,
            baseUrl: serverInfo.baseUrl
          }}
          onClose={handleCloseTest}
        />
      )}
    </div>
  );
};
