{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst PanelBottomClose = createLucideIcon(\"PanelBottomClose\", [[\"rect\", {\n  width: \"18\",\n  height: \"18\",\n  x: \"3\",\n  y: \"3\",\n  rx: \"2\",\n  ry: \"2\",\n  key: \"1m3agn\"\n}], [\"line\", {\n  x1: \"3\",\n  x2: \"21\",\n  y1: \"15\",\n  y2: \"15\",\n  key: \"o2sbyz\"\n}], [\"path\", {\n  d: \"m15 8-3 3-3-3\",\n  key: \"1oxy1z\"\n}]]);\nexport { PanelBottomClose as default };", "map": {"version": 3, "names": ["PanelBottomClose", "createLucideIcon", "width", "height", "x", "y", "rx", "ry", "key", "x1", "x2", "y1", "y2", "d"], "sources": ["D:\\repos-personal\\repos\\openapi-to-mcp\\ui\\node_modules\\lucide-react\\src\\icons\\panel-bottom-close.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name PanelBottomClose\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMTgiIGhlaWdodD0iMTgiIHg9IjMiIHk9IjMiIHJ4PSIyIiByeT0iMiIgLz4KICA8bGluZSB4MT0iMyIgeDI9IjIxIiB5MT0iMTUiIHkyPSIxNSIgLz4KICA8cGF0aCBkPSJtMTUgOC0zIDMtMy0zIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/panel-bottom-close\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst PanelBottomClose = createLucideIcon('PanelBottomClose', [\n  [\n    'rect',\n    {\n      width: '18',\n      height: '18',\n      x: '3',\n      y: '3',\n      rx: '2',\n      ry: '2',\n      key: '1m3agn',\n    },\n  ],\n  ['line', { x1: '3', x2: '21', y1: '15', y2: '15', key: 'o2sbyz' }],\n  ['path', { d: 'm15 8-3 3-3-3', key: '1oxy1z' }],\n]);\n\nexport default PanelBottomClose;\n"], "mappings": ";;;;;AAaM,MAAAA,gBAAA,GAAmBC,gBAAA,CAAiB,kBAAoB,GAC5D,CACE,QACA;EACEC,KAAO;EACPC,MAAQ;EACRC,CAAG;EACHC,CAAG;EACHC,EAAI;EACJC,EAAI;EACJC,GAAK;AACP,EACF,EACA,CAAC,QAAQ;EAAEC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAJ,GAAA,EAAK;AAAA,CAAU,GACjE,CAAC,MAAQ;EAAEK,CAAA,EAAG,eAAiB;EAAAL,GAAA,EAAK;AAAA,CAAU,EAC/C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}