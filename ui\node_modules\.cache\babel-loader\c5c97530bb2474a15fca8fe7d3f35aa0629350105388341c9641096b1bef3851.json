{"ast": null, "code": "/**\r\n * Main App component - Production-ready with all routes\r\n */import React from'react';import{BrowserRouter as Router,Routes,Route,Navigate}from'react-router-dom';import{Toaster}from'react-hot-toast';// Components\nimport{Header}from'./components/layout/Header';// Pages\nimport{LandingPage}from'./pages/LandingPage';import{ConversionPage}from'./pages/ConversionPage';import{ComingSoonPage}from'./pages/ComingSoonPage';import{PricingPage}from'./pages/PricingPage';// Styles\nimport'./App.css';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";function App(){return/*#__PURE__*/_jsx(Router,{children:/*#__PURE__*/_jsxs(\"div\",{className:\"min-h-screen bg-gray-50\",children:[/*#__PURE__*/_jsx(<PERSON><PERSON>,{}),/*#__PURE__*/_jsx(\"main\",{className:\"flex-1\",children:/*#__PURE__*/_jsxs(Routes,{children:[/*#__PURE__*/_jsx(Route,{path:\"/\",element:/*#__PURE__*/_jsx(LandingPage,{})}),/*#__PURE__*/_jsx(Route,{path:\"/convert\",element:/*#__PURE__*/_jsx(ConversionPage,{})}),/*#__PURE__*/_jsx(Route,{path:\"/pricing\",element:/*#__PURE__*/_jsx(PricingPage,{})}),/*#__PURE__*/_jsx(Route,{path:\"/chat\",element:/*#__PURE__*/_jsx(ComingSoonPage,{feature:\"AI Chat Interface\",description:\"Chat directly with your APIs using natural language. Ask questions, get data, and perform actions through an intuitive chat interface powered by your MCP servers.\",expectedDate:\"Q1 2025\"})}),/*#__PURE__*/_jsx(Route,{path:\"/tools\",element:/*#__PURE__*/_jsx(ComingSoonPage,{feature:\"MCP Tools Manager\",description:\"Manage, monitor, and debug your MCP servers with our comprehensive tools dashboard. View logs, test endpoints, and optimize performance.\",expectedDate:\"Q1 2025\"})}),/*#__PURE__*/_jsx(Route,{path:\"/playground\",element:/*#__PURE__*/_jsx(ComingSoonPage,{feature:\"API Playground\",description:\"Test and experiment with your APIs before converting them to MCP servers. Interactive documentation and testing environment.\",expectedDate:\"Q2 2025\"})}),/*#__PURE__*/_jsx(Route,{path:\"/profile\",element:/*#__PURE__*/_jsx(ComingSoonPage,{feature:\"User Profile\",description:\"Manage your account, view conversion history, and customize your MCPify experience.\",expectedDate:\"Q1 2025\"})}),/*#__PURE__*/_jsx(Route,{path:\"/settings\",element:/*#__PURE__*/_jsx(ComingSoonPage,{feature:\"Settings\",description:\"Configure your preferences, manage integrations, and customize your workflow.\",expectedDate:\"Q1 2025\"})}),/*#__PURE__*/_jsx(Route,{path:\"*\",element:/*#__PURE__*/_jsx(Navigate,{to:\"/\",replace:true})})]})}),/*#__PURE__*/_jsx(Toaster,{position:\"top-right\",toastOptions:{duration:4000,style:{background:'#fff',color:'#374151',boxShadow:'0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)'}}})]})});}export default App;", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Navigate", "Toaster", "Header", "LandingPage", "ConversionPage", "ComingSoonPage", "PricingPage", "jsx", "_jsx", "jsxs", "_jsxs", "App", "children", "className", "path", "element", "feature", "description", "expectedDate", "to", "replace", "position", "toastOptions", "duration", "style", "background", "color", "boxShadow"], "sources": ["D:/repos-personal/repos/openapi-to-mcp/ui/src/App.tsx"], "sourcesContent": ["/**\r\n * Main App component - Production-ready with all routes\r\n */\r\n\r\nimport React from 'react';\r\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\r\nimport { Toaster } from 'react-hot-toast';\r\n\r\n// Components\r\nimport { Header } from './components/layout/Header';\r\n\r\n// Pages\r\nimport { LandingPage } from './pages/LandingPage';\r\nimport { ConversionPage } from './pages/ConversionPage';\r\nimport { ComingSoonPage } from './pages/ComingSoonPage';\r\nimport { PricingPage } from './pages/PricingPage';\r\n\r\n// Styles\r\nimport './App.css';\r\n\r\nfunction App() {\r\n  return (\r\n    <Router>\r\n      <div className=\"min-h-screen bg-gray-50\">\r\n        <Header />\r\n        <main className=\"flex-1\">\r\n          <Routes>\r\n            {/* Landing page */}\r\n            <Route path=\"/\" element={<LandingPage />} />\r\n\r\n            {/* Main conversion feature */}\r\n            <Route path=\"/convert\" element={<ConversionPage />} />\r\n\r\n            {/* Pricing */}\r\n            <Route path=\"/pricing\" element={<PricingPage />} />\r\n\r\n            {/* Coming Soon pages */}\r\n            <Route\r\n              path=\"/chat\"\r\n              element={\r\n                <ComingSoonPage\r\n                  feature=\"AI Chat Interface\"\r\n                  description=\"Chat directly with your APIs using natural language. Ask questions, get data, and perform actions through an intuitive chat interface powered by your MCP servers.\"\r\n                  expectedDate=\"Q1 2025\"\r\n                />\r\n              }\r\n            />\r\n            <Route\r\n              path=\"/tools\"\r\n              element={\r\n                <ComingSoonPage\r\n                  feature=\"MCP Tools Manager\"\r\n                  description=\"Manage, monitor, and debug your MCP servers with our comprehensive tools dashboard. View logs, test endpoints, and optimize performance.\"\r\n                  expectedDate=\"Q1 2025\"\r\n                />\r\n              }\r\n            />\r\n            <Route\r\n              path=\"/playground\"\r\n              element={\r\n                <ComingSoonPage\r\n                  feature=\"API Playground\"\r\n                  description=\"Test and experiment with your APIs before converting them to MCP servers. Interactive documentation and testing environment.\"\r\n                  expectedDate=\"Q2 2025\"\r\n                />\r\n              }\r\n            />\r\n\r\n            {/* User pages (coming soon) */}\r\n            <Route\r\n              path=\"/profile\"\r\n              element={\r\n                <ComingSoonPage\r\n                  feature=\"User Profile\"\r\n                  description=\"Manage your account, view conversion history, and customize your MCPify experience.\"\r\n                  expectedDate=\"Q1 2025\"\r\n                />\r\n              }\r\n            />\r\n            <Route\r\n              path=\"/settings\"\r\n              element={\r\n                <ComingSoonPage\r\n                  feature=\"Settings\"\r\n                  description=\"Configure your preferences, manage integrations, and customize your workflow.\"\r\n                  expectedDate=\"Q1 2025\"\r\n                />\r\n              }\r\n            />\r\n\r\n            {/* Fallback */}\r\n            <Route path=\"*\" element={<Navigate to=\"/\" replace />} />\r\n          </Routes>\r\n        </main>\r\n\r\n        {/* Toast notifications */}\r\n        <Toaster\r\n          position=\"top-right\"\r\n          toastOptions={{\r\n            duration: 4000,\r\n            style: {\r\n              background: '#fff',\r\n              color: '#374151',\r\n              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',\r\n            },\r\n          }}\r\n        />\r\n      </div>\r\n    </Router>\r\n  );\r\n}\r\n\r\nexport default App;\r\n"], "mappings": "AAAA;AACA;AACA,GAEA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,aAAa,GAAI,CAAAC,MAAM,CAAEC,MAAM,CAAEC,KAAK,CAAEC,QAAQ,KAAQ,kBAAkB,CACnF,OAASC,OAAO,KAAQ,iBAAiB,CAEzC;AACA,OAASC,MAAM,KAAQ,4BAA4B,CAEnD;AACA,OAASC,WAAW,KAAQ,qBAAqB,CACjD,OAASC,cAAc,KAAQ,wBAAwB,CACvD,OAASC,cAAc,KAAQ,wBAAwB,CACvD,OAASC,WAAW,KAAQ,qBAAqB,CAEjD;AACA,MAAO,WAAW,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEnB,QAAS,CAAAC,GAAGA,CAAA,CAAG,CACb,mBACEH,IAAA,CAACX,MAAM,EAAAe,QAAA,cACLF,KAAA,QAAKG,SAAS,CAAC,yBAAyB,CAAAD,QAAA,eACtCJ,IAAA,CAACN,MAAM,GAAE,CAAC,cACVM,IAAA,SAAMK,SAAS,CAAC,QAAQ,CAAAD,QAAA,cACtBF,KAAA,CAACZ,MAAM,EAAAc,QAAA,eAELJ,IAAA,CAACT,KAAK,EAACe,IAAI,CAAC,GAAG,CAACC,OAAO,cAAEP,IAAA,CAACL,WAAW,GAAE,CAAE,CAAE,CAAC,cAG5CK,IAAA,CAACT,KAAK,EAACe,IAAI,CAAC,UAAU,CAACC,OAAO,cAAEP,IAAA,CAACJ,cAAc,GAAE,CAAE,CAAE,CAAC,cAGtDI,IAAA,CAACT,KAAK,EAACe,IAAI,CAAC,UAAU,CAACC,OAAO,cAAEP,IAAA,CAACF,WAAW,GAAE,CAAE,CAAE,CAAC,cAGnDE,IAAA,CAACT,KAAK,EACJe,IAAI,CAAC,OAAO,CACZC,OAAO,cACLP,IAAA,CAACH,cAAc,EACbW,OAAO,CAAC,mBAAmB,CAC3BC,WAAW,CAAC,oKAAoK,CAChLC,YAAY,CAAC,SAAS,CACvB,CACF,CACF,CAAC,cACFV,IAAA,CAACT,KAAK,EACJe,IAAI,CAAC,QAAQ,CACbC,OAAO,cACLP,IAAA,CAACH,cAAc,EACbW,OAAO,CAAC,mBAAmB,CAC3BC,WAAW,CAAC,0IAA0I,CACtJC,YAAY,CAAC,SAAS,CACvB,CACF,CACF,CAAC,cACFV,IAAA,CAACT,KAAK,EACJe,IAAI,CAAC,aAAa,CAClBC,OAAO,cACLP,IAAA,CAACH,cAAc,EACbW,OAAO,CAAC,gBAAgB,CACxBC,WAAW,CAAC,8HAA8H,CAC1IC,YAAY,CAAC,SAAS,CACvB,CACF,CACF,CAAC,cAGFV,IAAA,CAACT,KAAK,EACJe,IAAI,CAAC,UAAU,CACfC,OAAO,cACLP,IAAA,CAACH,cAAc,EACbW,OAAO,CAAC,cAAc,CACtBC,WAAW,CAAC,qFAAqF,CACjGC,YAAY,CAAC,SAAS,CACvB,CACF,CACF,CAAC,cACFV,IAAA,CAACT,KAAK,EACJe,IAAI,CAAC,WAAW,CAChBC,OAAO,cACLP,IAAA,CAACH,cAAc,EACbW,OAAO,CAAC,UAAU,CAClBC,WAAW,CAAC,+EAA+E,CAC3FC,YAAY,CAAC,SAAS,CACvB,CACF,CACF,CAAC,cAGFV,IAAA,CAACT,KAAK,EAACe,IAAI,CAAC,GAAG,CAACC,OAAO,cAAEP,IAAA,CAACR,QAAQ,EAACmB,EAAE,CAAC,GAAG,CAACC,OAAO,MAAE,CAAE,CAAE,CAAC,EAClD,CAAC,CACL,CAAC,cAGPZ,IAAA,CAACP,OAAO,EACNoB,QAAQ,CAAC,WAAW,CACpBC,YAAY,CAAE,CACZC,QAAQ,CAAE,IAAI,CACdC,KAAK,CAAE,CACLC,UAAU,CAAE,MAAM,CAClBC,KAAK,CAAE,SAAS,CAChBC,SAAS,CAAE,uEACb,CACF,CAAE,CACH,CAAC,EACC,CAAC,CACA,CAAC,CAEb,CAEA,cAAe,CAAAhB,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}