{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst SmartphoneCharging = createLucideIcon(\"SmartphoneCharging\", [[\"rect\", {\n  width: \"14\",\n  height: \"20\",\n  x: \"5\",\n  y: \"2\",\n  rx: \"2\",\n  ry: \"2\",\n  key: \"1yt0o3\"\n}], [\"path\", {\n  d: \"M12.667 8 10 12h4l-2.667 4\",\n  key: \"h9lk2d\"\n}]]);\nexport { SmartphoneCharging as default };", "map": {"version": 3, "names": ["SmartphoneCharging", "createLucideIcon", "width", "height", "x", "y", "rx", "ry", "key", "d"], "sources": ["D:\\repos-personal\\repos\\openapi-to-mcp\\ui\\node_modules\\lucide-react\\src\\icons\\smartphone-charging.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name SmartphoneCharging\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMTQiIGhlaWdodD0iMjAiIHg9IjUiIHk9IjIiIHJ4PSIyIiByeT0iMiIgLz4KICA8cGF0aCBkPSJNMTIuNjY3IDggMTAgMTJoNGwtMi42NjcgNCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/smartphone-charging\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst SmartphoneCharging = createLucideIcon('SmartphoneCharging', [\n  [\n    'rect',\n    {\n      width: '14',\n      height: '20',\n      x: '5',\n      y: '2',\n      rx: '2',\n      ry: '2',\n      key: '1yt0o3',\n    },\n  ],\n  ['path', { d: 'M12.667 8 10 12h4l-2.667 4', key: 'h9lk2d' }],\n]);\n\nexport default SmartphoneCharging;\n"], "mappings": ";;;;;AAaM,MAAAA,kBAAA,GAAqBC,gBAAA,CAAiB,oBAAsB,GAChE,CACE,QACA;EACEC,KAAO;EACPC,MAAQ;EACRC,CAAG;EACHC,CAAG;EACHC,EAAI;EACJC,EAAI;EACJC,GAAK;AACP,EACF,EACA,CAAC,MAAQ;EAAEC,CAAA,EAAG,4BAA8B;EAAAD,GAAA,EAAK;AAAA,CAAU,EAC5D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}