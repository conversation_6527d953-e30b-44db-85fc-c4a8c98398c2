{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst FileLock2 = createLucideIcon(\"FileLock2\", [[\"path\", {\n  d: \"M4 5V4a2 2 0 0 1 2-2h8.5L20 7.5V20a2 2 0 0 1-2 2H4\",\n  key: \"gwd2r9\"\n}], [\"polyline\", {\n  points: \"14 2 14 8 20 8\",\n  key: \"1ew0cm\"\n}], [\"rect\", {\n  width: \"8\",\n  height: \"5\",\n  x: \"2\",\n  y: \"13\",\n  rx: \"1\",\n  key: \"10y5wo\"\n}], [\"path\", {\n  d: \"M8 13v-2a2 2 0 1 0-4 0v2\",\n  key: \"1pdxzg\"\n}]]);\nexport { FileLock2 as default };", "map": {"version": 3, "names": ["FileLock2", "createLucideIcon", "d", "key", "points", "width", "height", "x", "y", "rx"], "sources": ["D:\\repos-personal\\repos\\openapi-to-mcp\\ui\\node_modules\\lucide-react\\src\\icons\\file-lock-2.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name FileLock2\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNCA1VjRhMiAyIDAgMCAxIDItMmg4LjVMMjAgNy41VjIwYTIgMiAwIDAgMS0yIDJINCIgLz4KICA8cG9seWxpbmUgcG9pbnRzPSIxNCAyIDE0IDggMjAgOCIgLz4KICA8cmVjdCB3aWR0aD0iOCIgaGVpZ2h0PSI1IiB4PSIyIiB5PSIxMyIgcng9IjEiIC8+CiAgPHBhdGggZD0iTTggMTN2LTJhMiAyIDAgMSAwLTQgMHYyIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/file-lock-2\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst FileLock2 = createLucideIcon('FileLock2', [\n  [\n    'path',\n    { d: 'M4 5V4a2 2 0 0 1 2-2h8.5L20 7.5V20a2 2 0 0 1-2 2H4', key: 'gwd2r9' },\n  ],\n  ['polyline', { points: '14 2 14 8 20 8', key: '1ew0cm' }],\n  [\n    'rect',\n    { width: '8', height: '5', x: '2', y: '13', rx: '1', key: '10y5wo' },\n  ],\n  ['path', { d: 'M8 13v-2a2 2 0 1 0-4 0v2', key: '1pdxzg' }],\n]);\n\nexport default FileLock2;\n"], "mappings": ";;;;;AAaM,MAAAA,SAAA,GAAYC,gBAAA,CAAiB,WAAa,GAC9C,CACE,QACA;EAAEC,CAAA,EAAG,oDAAsD;EAAAC,GAAA,EAAK;AAAS,EAC3E,EACA,CAAC,UAAY;EAAEC,MAAA,EAAQ,gBAAkB;EAAAD,GAAA,EAAK;AAAA,CAAU,GACxD,CACE,QACA;EAAEE,KAAA,EAAO,GAAK;EAAAC,MAAA,EAAQ,GAAK;EAAAC,CAAA,EAAG,GAAK;EAAAC,CAAA,EAAG,IAAM;EAAAC,EAAA,EAAI,GAAK;EAAAN,GAAA,EAAK;AAAS,EACrE,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,0BAA4B;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC1D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}