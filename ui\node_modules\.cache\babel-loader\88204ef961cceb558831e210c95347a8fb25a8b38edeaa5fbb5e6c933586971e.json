{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst BarChartHorizontal = createLucideIcon(\"BarChartHorizontal\", [[\"path\", {\n  d: \"M3 3v18h18\",\n  key: \"1s2lah\"\n}], [\"path\", {\n  d: \"M7 16h8\",\n  key: \"srdodz\"\n}], [\"path\", {\n  d: \"M7 11h12\",\n  key: \"127s9w\"\n}], [\"path\", {\n  d: \"M7 6h3\",\n  key: \"w9rmul\"\n}]]);\nexport { BarChartHorizontal as default };", "map": {"version": 3, "names": ["BarChartHorizontal", "createLucideIcon", "d", "key"], "sources": ["D:\\repos-personal\\repos\\openapi-to-mcp\\ui\\node_modules\\lucide-react\\src\\icons\\bar-chart-horizontal.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name BarChartHorizontal\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMyAzdjE4aDE4IiAvPgogIDxwYXRoIGQ9Ik03IDE2aDgiIC8+CiAgPHBhdGggZD0iTTcgMTFoMTIiIC8+CiAgPHBhdGggZD0iTTcgNmgzIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/bar-chart-horizontal\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst BarChartHorizontal = createLucideIcon('BarChartHorizontal', [\n  ['path', { d: 'M3 3v18h18', key: '1s2lah' }],\n  ['path', { d: 'M7 16h8', key: 'srdodz' }],\n  ['path', { d: 'M7 11h12', key: '127s9w' }],\n  ['path', { d: 'M7 6h3', key: 'w9rmul' }],\n]);\n\nexport default BarChartHorizontal;\n"], "mappings": ";;;;;AAaM,MAAAA,kBAAA,GAAqBC,gBAAA,CAAiB,oBAAsB,GAChE,CAAC,MAAQ;EAAEC,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,QAAU;EAAAC,GAAA,EAAK;AAAA,CAAU,EACxC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}