# Installation Instructions

## Quick Start

1. **Extract the bundle**:
   ```bash
   unzip swagger-petstore-openapi-3-0.zip
   cd swagger-petstore-openapi-3-0
   ```

2. **Install dependencies**:
   ```bash
   npm install
   ```

3. **Build the project**:
   ```bash
   npm run build
   ```

4. **Configure environment** (optional):
   ```bash
   cp .env.example .env
   # Edit .env file with your settings
   ```

5. **Start the server**:
   ```bash
   npm start
   ```

## Configuration

- **Port**: 8000 (configurable via PORT environment variable)
- **Base URL**: http://localhost:3000
- **Name**: swagger-petstore-openapi-3-0
- **Version**: 1.0.26

## Testing

Once the server is running, test the health endpoint:
```bash
curl http://localhost:8000/health
```

## MCP Protocol

Send requests to the MCP endpoint:
```bash
curl -X POST http://localhost:8000/mcp \
  -H "Content-Type: application/json" \
  -d '{
    "tool": "toolName",
    "parameters": { "param1": "value1" }
  }'
```

## Generated Files

- `src/server.ts` - Main server application
- `src/routes.ts` - API route handlers
- `src/types.ts` - TypeScript type definitions
- `mcp.json` - MCP manifest file
- `package.json` - Node.js dependencies
- `tsconfig.json` - TypeScript configuration

For more information, see README.md.
