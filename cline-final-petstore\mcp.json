{"name": "swagger-petstore-openapi-3-0", "version": "1.0.26", "description": "This is a sample Pet Store Server based on the OpenAPI 3.0 specification.  You can find out more about\nSwagger at [https://swagger.io](https://swagger.io). In the third iteration of the pet store, we've switched to the design first approach!\nYou can now help us improve the API whether it's by making changes to the definition itself or to the code.\nThat way, with time, we can improve the API in general, and expose some of the new features in OAS3.\n\nSome useful links:\n- [The Pet Store repository](https://github.com/swagger-api/swagger-petstore)\n- [The source API definition for the Pet Store](https://github.com/swagger-api/swagger-petstore/blob/master/src/main/resources/openapi.yaml)", "license": "MIT", "tools": [{"name": "addPet", "description": "Add a new pet to the store.", "inputSchema": {"type": "object", "properties": {"body": {"$ref": "#/components/schemas/Pet", "description": "Create a new pet in the store"}}, "required": ["body"], "additionalProperties": false}, "outputSchema": {"$ref": "#/components/schemas/Pet", "description": "Successful operation"}}, {"name": "updatePet", "description": "Update an existing pet.", "inputSchema": {"type": "object", "properties": {"body": {"$ref": "#/components/schemas/Pet", "description": "Update an existent pet in the store"}}, "required": ["body"], "additionalProperties": false}, "outputSchema": {"$ref": "#/components/schemas/Pet", "description": "Successful operation"}}, {"name": "findPetsByStatus", "description": "Finds Pets by status.", "inputSchema": {"type": "object", "properties": {"query": {"type": "object", "properties": {"status": {"type": "string", "default": "available", "enum": ["available", "pending", "sold"], "description": "Status values that need to be considered for filter"}}, "description": "Query parameters"}}, "additionalProperties": false}, "outputSchema": {"type": "array", "items": {"$ref": "#/components/schemas/Pet"}, "description": "successful operation"}}, {"name": "findPetsByTags", "description": "Finds Pets by tags.", "inputSchema": {"type": "object", "properties": {"query": {"type": "object", "properties": {"tags": {"type": "array", "items": {"type": "string"}, "description": "Tags to filter by"}}, "description": "Query parameters"}}, "additionalProperties": false}, "outputSchema": {"type": "array", "items": {"$ref": "#/components/schemas/Pet"}, "description": "successful operation"}}, {"name": "getPetById", "description": "Find pet by ID.", "inputSchema": {"type": "object", "properties": {"petId": {"type": "integer", "format": "int64", "description": "ID of pet to return"}}, "required": ["petId"], "additionalProperties": false}, "outputSchema": {"$ref": "#/components/schemas/Pet", "description": "successful operation"}}, {"name": "updatePetWithForm", "description": "Updates a pet in the store with form data.", "inputSchema": {"type": "object", "properties": {"petId": {"type": "integer", "format": "int64", "description": "ID of pet that needs to be updated"}, "query": {"type": "object", "properties": {"name": {"type": "string", "description": "Name of pet that needs to be updated"}, "status": {"type": "string", "description": "Status of pet that needs to be updated"}}, "description": "Query parameters"}}, "required": ["petId"], "additionalProperties": false}, "outputSchema": {"$ref": "#/components/schemas/Pet", "description": "successful operation"}}, {"name": "deletePet", "description": "Deletes a pet.", "inputSchema": {"type": "object", "properties": {"petId": {"type": "integer", "format": "int64", "description": "Pet id to delete"}, "headers": {"type": "object", "properties": {"api_key": {"type": "string", "description": ""}}, "description": "Request headers"}}, "required": ["petId"], "additionalProperties": false}, "outputSchema": {"type": "object", "description": "Pet deleted"}}, {"name": "uploadFile", "description": "Uploads an image.", "inputSchema": {"type": "object", "properties": {"petId": {"type": "integer", "format": "int64", "description": "ID of pet to update"}, "query": {"type": "object", "properties": {"additionalMetadata": {"type": "string", "description": "Additional Metadata"}}, "description": "Query parameters"}, "body": {"type": "string", "format": "binary", "description": "Request body"}}, "required": ["petId"], "additionalProperties": false}, "outputSchema": {"$ref": "#/components/schemas/ApiResponse", "description": "successful operation"}}, {"name": "getInventory", "description": "Returns pet inventories by status.", "inputSchema": {"type": "object", "properties": {}, "description": "No input parameters required"}, "outputSchema": {"type": "object", "additionalProperties": {"type": "integer", "format": "int32"}, "description": "successful operation"}}, {"name": "placeOrder", "description": "Place an order for a pet.", "inputSchema": {"type": "object", "properties": {"body": {"$ref": "#/components/schemas/Order", "description": "Request body"}}, "additionalProperties": false}, "outputSchema": {"$ref": "#/components/schemas/Order", "description": "successful operation"}}, {"name": "getOrderById", "description": "Find purchase order by ID.", "inputSchema": {"type": "object", "properties": {"orderId": {"type": "integer", "format": "int64", "description": "ID of order that needs to be fetched"}}, "required": ["orderId"], "additionalProperties": false}, "outputSchema": {"$ref": "#/components/schemas/Order", "description": "successful operation"}}, {"name": "deleteOrder", "description": "Delete purchase order by identifier.", "inputSchema": {"type": "object", "properties": {"orderId": {"type": "integer", "format": "int64", "description": "ID of the order that needs to be deleted"}}, "required": ["orderId"], "additionalProperties": false}, "outputSchema": {"type": "object", "description": "order deleted"}}, {"name": "createUser", "description": "Create user.", "inputSchema": {"type": "object", "properties": {"body": {"$ref": "#/components/schemas/User", "description": "Created user object"}}, "additionalProperties": false}, "outputSchema": {"$ref": "#/components/schemas/User", "description": "successful operation"}}, {"name": "createUsersWithListInput", "description": "Creates list of users with given input array.", "inputSchema": {"type": "object", "properties": {"body": {"type": "array", "items": {"$ref": "#/components/schemas/User"}, "description": "Request body"}}, "additionalProperties": false}, "outputSchema": {"$ref": "#/components/schemas/User", "description": "Successful operation"}}, {"name": "loginUser", "description": "Logs user into the system.", "inputSchema": {"type": "object", "properties": {"query": {"type": "object", "properties": {"username": {"type": "string", "description": "The user name for login"}, "password": {"type": "string", "description": "The password for login in clear text"}}, "description": "Query parameters"}}, "additionalProperties": false}, "outputSchema": {"type": "string", "description": "successful operation"}}, {"name": "logoutUser", "description": "Logs out current logged in user session.", "inputSchema": {"type": "object", "properties": {}, "description": "No input parameters required"}, "outputSchema": {"type": "object", "description": "successful operation"}}, {"name": "getUserByName", "description": "Get user by user name.", "inputSchema": {"type": "object", "properties": {"username": {"type": "string", "description": "The name that needs to be fetched. Use user1 for testing"}}, "required": ["username"], "additionalProperties": false}, "outputSchema": {"$ref": "#/components/schemas/User", "description": "successful operation"}}, {"name": "updateUser", "description": "Update user resource.", "inputSchema": {"type": "object", "properties": {"username": {"type": "string", "description": "name that need to be deleted"}, "body": {"$ref": "#/components/schemas/User", "description": "Update an existent user in the store"}}, "required": ["username"], "additionalProperties": false}, "outputSchema": {"type": "object", "description": "successful operation"}}, {"name": "deleteUser", "description": "Delete user resource.", "inputSchema": {"type": "object", "properties": {"username": {"type": "string", "description": "The name that needs to be deleted"}}, "required": ["username"], "additionalProperties": false}, "outputSchema": {"type": "object", "description": "User deleted"}}], "server": {"command": "node", "args": ["dist/server.js"], "env": {"PORT": "8000", "BASE_URL": "http://localhost:3000"}}}