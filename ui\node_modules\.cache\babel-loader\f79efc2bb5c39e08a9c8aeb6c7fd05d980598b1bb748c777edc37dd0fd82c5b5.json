{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst Candy = createLucideIcon(\"<PERSON>\", [[\"path\", {\n  d: \"m9.5 7.5-2 2a4.95 4.95 0 1 0 7 7l2-2a4.95 4.95 0 1 0-7-7Z\",\n  key: \"ue6khb\"\n}], [\"path\", {\n  d: \"M14 6.5v10\",\n  key: \"5xnk7c\"\n}], [\"path\", {\n  d: \"M10 7.5v10\",\n  key: \"1uew51\"\n}], [\"path\", {\n  d: \"m16 7 1-5 1.37.68A3 3 0 0 0 19.7 3H21v1.3c0 .********** 1.33L22 7l-5 1\",\n  key: \"b9cp6k\"\n}], [\"path\", {\n  d: \"m8 17-1 5-1.37-.68A3 3 0 0 0 4.3 21H3v-1.3a3 3 0 0 0-.32-1.33L2 17l5-1\",\n  key: \"5lney8\"\n}]]);\nexport { Candy as default };", "map": {"version": 3, "names": ["<PERSON>", "createLucideIcon", "d", "key"], "sources": ["D:\\repos-personal\\repos\\openapi-to-mcp\\ui\\node_modules\\lucide-react\\src\\icons\\candy.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Candy\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtOS41IDcuNS0yIDJhNC45NSA0Ljk1IDAgMSAwIDcgN2wyLTJhNC45NSA0Ljk1IDAgMSAwLTctN1oiIC8+CiAgPHBhdGggZD0iTTE0IDYuNXYxMCIgLz4KICA8cGF0aCBkPSJNMTAgNy41djEwIiAvPgogIDxwYXRoIGQ9Im0xNiA3IDEtNSAxLjM3LjY4QTMgMyAwIDAgMCAxOS43IDNIMjF2MS4zYzAgLjQ2LjEuOTIuMzIgMS4zM0wyMiA3bC01IDEiIC8+CiAgPHBhdGggZD0ibTggMTctMSA1LTEuMzctLjY4QTMgMyAwIDAgMCA0LjMgMjFIM3YtMS4zYTMgMyAwIDAgMC0uMzItMS4zM0wyIDE3bDUtMSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/candy\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Candy = createLucideIcon('Candy', [\n  [\n    'path',\n    {\n      d: 'm9.5 7.5-2 2a4.95 4.95 0 1 0 7 7l2-2a4.95 4.95 0 1 0-7-7Z',\n      key: 'ue6khb',\n    },\n  ],\n  ['path', { d: 'M14 6.5v10', key: '5xnk7c' }],\n  ['path', { d: 'M10 7.5v10', key: '1uew51' }],\n  [\n    'path',\n    {\n      d: 'm16 7 1-5 1.37.68A3 3 0 0 0 19.7 3H21v1.3c0 .********** 1.33L22 7l-5 1',\n      key: 'b9cp6k',\n    },\n  ],\n  [\n    'path',\n    {\n      d: 'm8 17-1 5-1.37-.68A3 3 0 0 0 4.3 21H3v-1.3a3 3 0 0 0-.32-1.33L2 17l5-1',\n      key: '5lney8',\n    },\n  ],\n]);\n\nexport default Candy;\n"], "mappings": ";;;;;AAaM,MAAAA,KAAA,GAAQC,gBAAA,CAAiB,OAAS,GACtC,CACE,QACA;EACEC,CAAG;EACHC,GAAK;AACP,EACF,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,MAAQ;EAAED,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3C,CACE,QACA;EACED,CAAG;EACHC,GAAK;AACP,EACF,EACA,CACE,QACA;EACED,CAAG;EACHC,GAAK;AACP,EACF,CACD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}