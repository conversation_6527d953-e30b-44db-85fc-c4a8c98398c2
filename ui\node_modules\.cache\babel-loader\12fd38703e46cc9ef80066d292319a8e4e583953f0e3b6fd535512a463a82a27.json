{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst Popcorn = createLucideIcon(\"Popcorn\", [[\"path\", {\n  d: \"M18 8a2 2 0 0 0 0-4 2 2 0 0 0-4 0 2 2 0 0 0-4 0 2 2 0 0 0-4 0 2 2 0 0 0 0 4\",\n  key: \"10td1f\"\n}], [\"path\", {\n  d: \"M10 22 9 8\",\n  key: \"yjptiv\"\n}], [\"path\", {\n  d: \"m14 22 1-14\",\n  key: \"8jwc8b\"\n}], [\"path\", {\n  d: \"M20 8c.5 0 .9.4.8 1l-2.6 12c-.1.5-.7 1-1.2 1H7c-.6 0-1.1-.4-1.2-1L3.2 9c-.1-.6.3-1 .8-1Z\",\n  key: \"1qo33t\"\n}]]);\nexport { Popcorn as default };", "map": {"version": 3, "names": ["Popcorn", "createLucideIcon", "d", "key"], "sources": ["D:\\repos-personal\\repos\\openapi-to-mcp\\ui\\node_modules\\lucide-react\\src\\icons\\popcorn.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name <PERSON>corn\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTggOGEyIDIgMCAwIDAgMC00IDIgMiAwIDAgMC00IDAgMiAyIDAgMCAwLTQgMCAyIDIgMCAwIDAtNCAwIDIgMiAwIDAgMCAwIDQiIC8+CiAgPHBhdGggZD0iTTEwIDIyIDkgOCIgLz4KICA8cGF0aCBkPSJtMTQgMjIgMS0xNCIgLz4KICA8cGF0aCBkPSJNMjAgOGMuNSAwIC45LjQuOCAxbC0yLjYgMTJjLS4xLjUtLjcgMS0xLjIgMUg3Yy0uNiAwLTEuMS0uNC0xLjItMUwzLjIgOWMtLjEtLjYuMy0xIC44LTFaIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/popcorn\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Popcorn = createLucideIcon('Popcorn', [\n  [\n    'path',\n    {\n      d: 'M18 8a2 2 0 0 0 0-4 2 2 0 0 0-4 0 2 2 0 0 0-4 0 2 2 0 0 0-4 0 2 2 0 0 0 0 4',\n      key: '10td1f',\n    },\n  ],\n  ['path', { d: 'M10 22 9 8', key: 'yjptiv' }],\n  ['path', { d: 'm14 22 1-14', key: '8jwc8b' }],\n  [\n    'path',\n    {\n      d: 'M20 8c.5 0 .9.4.8 1l-2.6 12c-.1.5-.7 1-1.2 1H7c-.6 0-1.1-.4-1.2-1L3.2 9c-.1-.6.3-1 .8-1Z',\n      key: '1qo33t',\n    },\n  ],\n]);\n\nexport default Popcorn;\n"], "mappings": ";;;;;AAaM,MAAAA,OAAA,GAAUC,gBAAA,CAAiB,SAAW,GAC1C,CACE,QACA;EACEC,CAAG;EACHC,GAAK;AACP,EACF,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,MAAQ;EAAED,CAAA,EAAG,aAAe;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC5C,CACE,QACA;EACED,CAAG;EACHC,GAAK;AACP,EACF,CACD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}