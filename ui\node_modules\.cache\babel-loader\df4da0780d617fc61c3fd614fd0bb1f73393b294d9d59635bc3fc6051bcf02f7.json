{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst Contact = createLucideIcon(\"Contact\", [[\"path\", {\n  d: \"M17 18a2 2 0 0 0-2-2H9a2 2 0 0 0-2 2\",\n  key: \"1mghuy\"\n}], [\"rect\", {\n  width: \"18\",\n  height: \"18\",\n  x: \"3\",\n  y: \"4\",\n  rx: \"2\",\n  key: \"1hopcy\"\n}], [\"circle\", {\n  cx: \"12\",\n  cy: \"10\",\n  r: \"2\",\n  key: \"1yojzk\"\n}], [\"line\", {\n  x1: \"8\",\n  x2: \"8\",\n  y1: \"2\",\n  y2: \"4\",\n  key: \"1ff9gb\"\n}], [\"line\", {\n  x1: \"16\",\n  x2: \"16\",\n  y1: \"2\",\n  y2: \"4\",\n  key: \"1ufoma\"\n}]]);\nexport { Contact as default };", "map": {"version": 3, "names": ["Contact", "createLucideIcon", "d", "key", "width", "height", "x", "y", "rx", "cx", "cy", "r", "x1", "x2", "y1", "y2"], "sources": ["D:\\repos-personal\\repos\\openapi-to-mcp\\ui\\node_modules\\lucide-react\\src\\icons\\contact.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Contact\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTcgMThhMiAyIDAgMCAwLTItMkg5YTIgMiAwIDAgMC0yIDIiIC8+CiAgPHJlY3Qgd2lkdGg9IjE4IiBoZWlnaHQ9IjE4IiB4PSIzIiB5PSI0IiByeD0iMiIgLz4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEwIiByPSIyIiAvPgogIDxsaW5lIHgxPSI4IiB4Mj0iOCIgeTE9IjIiIHkyPSI0IiAvPgogIDxsaW5lIHgxPSIxNiIgeDI9IjE2IiB5MT0iMiIgeTI9IjQiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/contact\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Contact = createLucideIcon('Contact', [\n  ['path', { d: 'M17 18a2 2 0 0 0-2-2H9a2 2 0 0 0-2 2', key: '1mghuy' }],\n  [\n    'rect',\n    { width: '18', height: '18', x: '3', y: '4', rx: '2', key: '1hopcy' },\n  ],\n  ['circle', { cx: '12', cy: '10', r: '2', key: '1yojzk' }],\n  ['line', { x1: '8', x2: '8', y1: '2', y2: '4', key: '1ff9gb' }],\n  ['line', { x1: '16', x2: '16', y1: '2', y2: '4', key: '1ufoma' }],\n]);\n\nexport default Contact;\n"], "mappings": ";;;;;AAaM,MAAAA,OAAA,GAAUC,gBAAA,CAAiB,SAAW,GAC1C,CAAC,MAAQ;EAAEC,CAAA,EAAG,sCAAwC;EAAAC,GAAA,EAAK;AAAA,CAAU,GACrE,CACE,QACA;EAAEC,KAAA,EAAO,IAAM;EAAAC,MAAA,EAAQ,IAAM;EAAAC,CAAA,EAAG,GAAK;EAAAC,CAAA,EAAG,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAL,GAAA,EAAK;AAAS,EACtE,EACA,CAAC,QAAU;EAAEM,EAAI;EAAMC,EAAI;EAAMC,CAAG;EAAKR,GAAK;AAAA,CAAU,GACxD,CAAC,QAAQ;EAAES,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAZ,GAAA,EAAK;AAAA,CAAU,GAC9D,CAAC,QAAQ;EAAES,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAZ,GAAA,EAAK;AAAA,CAAU,EACjE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}