# swagger-petstore-openapi-3-0

Generated MCP server from OpenAPI specification.

## Overview

- **Name**: swagger-petstore-openapi-3-0
- **Version**: 1.0.26
- **Description**: This is a sample Pet Store Server based on the OpenAPI 3.0 specification.  You can find out more about
Swagger at [https://swagger.io](https://swagger.io). In the third iteration of the pet store, we've switched to the design first approach!
You can now help us improve the API whether it's by making changes to the definition itself or to the code.
That way, with time, we can improve the API in general, and expose some of the new features in OAS3.

Some useful links:
- [The Pet Store repository](https://github.com/swagger-api/swagger-petstore)
- [The source API definition for the Pet Store](https://github.com/swagger-api/swagger-petstore/blob/master/src/main/resources/openapi.yaml)
- **Base URL**: http://localhost:3000
- **Port**: 8000

## Available Tools

- **addPet**: POST /pet - Add a new pet to the store.
- **updatePet**: PUT /pet - Update an existing pet.
- **findPetsByStatus**: GET /pet/findByStatus - Finds Pets by status.
- **findPetsByTags**: GET /pet/findByTags - Finds Pets by tags.
- **getPetById**: GET /pet/{petId} - Find pet by ID.
- **updatePetWithForm**: POST /pet/{petId} - Updates a pet in the store with form data.
- **deletePet**: DELETE /pet/{petId} - Deletes a pet.
- **uploadFile**: POST /pet/{petId}/uploadImage - Uploads an image.
- **getInventory**: GET /store/inventory - Returns pet inventories by status.
- **placeOrder**: POST /store/order - Place an order for a pet.
- **getOrderById**: GET /store/order/{orderId} - Find purchase order by ID.
- **deleteOrder**: DELETE /store/order/{orderId} - Delete purchase order by identifier.
- **createUser**: POST /user - Create user.
- **createUsersWithListInput**: POST /user/createWithList - Creates list of users with given input array.
- **loginUser**: GET /user/login - Logs user into the system.
- **logoutUser**: GET /user/logout - Logs out current logged in user session.
- **getUserByName**: GET /user/{username} - Get user by user name.
- **updateUser**: PUT /user/{username} - Update user resource.
- **deleteUser**: DELETE /user/{username} - Delete user resource.

## Installation

```bash
npm install
npm run build
```

## Usage

```bash
# Development
npm run dev

# Production
npm start
```

## Environment Variables

Create a `.env` file based on `.env.example`:

```bash
PORT=8000
BASE_URL=http://localhost:3000
NODE_ENV=production
```

## MCP Client Configuration

### Cline (Claude Dev)
Add to your Cline MCP configuration:
```json
{
  "mcpServers": {
    "swagger-petstore-openapi-3-0": {
      "command": "node",
      "args": ["path/to/your/server/dist/server.js"],
      "env": {
        "PORT": "8000",
        "BASE_URL": "http://localhost:3000"
      }
    }
  }
}
```

### Cursor
Add to your Cursor MCP configuration:
```json
{
  "mcpServers": {
    "swagger-petstore-openapi-3-0": {
      "command": "node",
      "args": ["path/to/your/server/dist/server.js"]
    }
  }
}
```

### Windsurf
Add to your Windsurf MCP configuration:
```json
{
  "mcpServers": {
    "swagger-petstore-openapi-3-0": {
      "command": "node",
      "args": ["path/to/your/server/dist/server.js"]
    }
  }
}
```

### ChatMCP
Configure as HTTP transport:
- **Transport**: stdio
- **Command**: node
- **Args**: path/to/your/server/dist/server.js

## API Endpoints

### Health Check
```
GET /health
```

### MCP Protocol (JSON-RPC 2.0)
```
POST /mcp
Content-Type: application/json

{
  "jsonrpc": "2.0",
  "id": 1,
  "method": "tools/call",
  "params": {
    "name": "toolName",
    "arguments": { ... }
  }
}
```

### SSE Streaming
```
GET /mcp/sse
```

### Direct Tool Access
```
POST /tools/{toolName}
Content-Type: application/json

{ "param1": "value1", ... }
```

## Generated by

[openapi-to-mcp](https://github.com/yourusername/openapi-to-mcp)
