{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst IceCream2 = createLucideIcon(\"IceCream2\", [[\"path\", {\n  d: \"M12 17c5 0 8-2.69 8-6H4c0 3.31 3 6 8 6Zm-4 4h8m-4-3v3M5.14 11a3.5 3.5 0 1 1 6.71 0\",\n  key: \"g86ewz\"\n}], [\"path\", {\n  d: \"M12.14 11a3.5 3.5 0 1 1 6.71 0\",\n  key: \"4k3m1s\"\n}], [\"path\", {\n  d: \"M15.5 6.5a3.5 3.5 0 1 0-7 0\",\n  key: \"zmuahr\"\n}]]);\nexport { IceCream2 as default };", "map": {"version": 3, "names": ["IceCream2", "createLucideIcon", "d", "key"], "sources": ["D:\\repos-personal\\repos\\openapi-to-mcp\\ui\\node_modules\\lucide-react\\src\\icons\\ice-cream-2.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name IceCream2\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgMTdjNSAwIDgtMi42OSA4LTZINGMwIDMuMzEgMyA2IDggNlptLTQgNGg4bS00LTN2M001LjE0IDExYTMuNSAzLjUgMCAxIDEgNi43MSAwIiAvPgogIDxwYXRoIGQ9Ik0xMi4xNCAxMWEzLjUgMy41IDAgMSAxIDYuNzEgMCIgLz4KICA8cGF0aCBkPSJNMTUuNSA2LjVhMy41IDMuNSAwIDEgMC03IDAiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/ice-cream-2\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst IceCream2 = createLucideIcon('IceCream2', [\n  [\n    'path',\n    {\n      d: 'M12 17c5 0 8-2.69 8-6H4c0 3.31 3 6 8 6Zm-4 4h8m-4-3v3M5.14 11a3.5 3.5 0 1 1 6.71 0',\n      key: 'g86ewz',\n    },\n  ],\n  ['path', { d: 'M12.14 11a3.5 3.5 0 1 1 6.71 0', key: '4k3m1s' }],\n  ['path', { d: 'M15.5 6.5a3.5 3.5 0 1 0-7 0', key: 'zmuahr' }],\n]);\n\nexport default IceCream2;\n"], "mappings": ";;;;;AAaM,MAAAA,SAAA,GAAYC,gBAAA,CAAiB,WAAa,GAC9C,CACE,QACA;EACEC,CAAG;EACHC,GAAK;AACP,EACF,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,gCAAkC;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC/D,CAAC,MAAQ;EAAED,CAAA,EAAG,6BAA+B;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC7D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}