{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst Outdent = createLucideIcon(\"Outdent\", [[\"polyline\", {\n  points: \"7 8 3 12 7 16\",\n  key: \"2j60jr\"\n}], [\"line\", {\n  x1: \"21\",\n  x2: \"11\",\n  y1: \"12\",\n  y2: \"12\",\n  key: \"1fxxak\"\n}], [\"line\", {\n  x1: \"21\",\n  x2: \"11\",\n  y1: \"6\",\n  y2: \"6\",\n  key: \"asgu94\"\n}], [\"line\", {\n  x1: \"21\",\n  x2: \"11\",\n  y1: \"18\",\n  y2: \"18\",\n  key: \"13dsj7\"\n}]]);\nexport { Outdent as default };", "map": {"version": 3, "names": ["Outdent", "createLucideIcon", "points", "key", "x1", "x2", "y1", "y2"], "sources": ["D:\\repos-personal\\repos\\openapi-to-mcp\\ui\\node_modules\\lucide-react\\src\\icons\\outdent.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Outdent\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cG9seWxpbmUgcG9pbnRzPSI3IDggMyAxMiA3IDE2IiAvPgogIDxsaW5lIHgxPSIyMSIgeDI9IjExIiB5MT0iMTIiIHkyPSIxMiIgLz4KICA8bGluZSB4MT0iMjEiIHgyPSIxMSIgeTE9IjYiIHkyPSI2IiAvPgogIDxsaW5lIHgxPSIyMSIgeDI9IjExIiB5MT0iMTgiIHkyPSIxOCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/outdent\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Outdent = createLucideIcon('Outdent', [\n  ['polyline', { points: '7 8 3 12 7 16', key: '2j60jr' }],\n  ['line', { x1: '21', x2: '11', y1: '12', y2: '12', key: '1fxxak' }],\n  ['line', { x1: '21', x2: '11', y1: '6', y2: '6', key: 'asgu94' }],\n  ['line', { x1: '21', x2: '11', y1: '18', y2: '18', key: '13dsj7' }],\n]);\n\nexport default Outdent;\n"], "mappings": ";;;;;AAaM,MAAAA,OAAA,GAAUC,gBAAA,CAAiB,SAAW,GAC1C,CAAC,UAAY;EAAEC,MAAA,EAAQ,eAAiB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACvD,CAAC,QAAQ;EAAEC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAJ,GAAA,EAAK;AAAA,CAAU,GAClE,CAAC,QAAQ;EAAEC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAJ,GAAA,EAAK;AAAA,CAAU,GAChE,CAAC,QAAQ;EAAEC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAJ,GAAA,EAAK;AAAA,CAAU,EACnE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}