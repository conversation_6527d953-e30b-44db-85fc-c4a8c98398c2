# OpenAPI to MCP Architecture & Function Calling Flow

This document explains the architecture and function calling flow of the OpenAPI to MCP (Model Context Protocol) server generator.

## 🏗️ System Overview

The system converts any OpenAPI specification into a fully functional MCP server with LLM function calling capabilities. It consists of:

- **Main Server** (Port 3000): Handles conversion and server management
- **Generated MCP Servers** (Ports 8000+): Individual servers for each OpenAPI spec
- **Web UI** (Port 3001): React-based interface for easy interaction
- **LiteLLM Gateway** (Port 4000): LLM provider abstraction layer

## 📊 System Architecture Diagram

```mermaid
graph TB
    subgraph "Frontend Layer"
        WebUI[🖥️ Web UI<br/>React App<br/>Port 3001]
        DirectAPI[📡 Direct API Access<br/>REST/cURL<br/>Port 3000]
    end

    subgraph "Main Server (Port 3000)"
        MainApp[🚀 Express Server<br/>src/server.ts]

        subgraph "Core Services"
            ConvertAPI[🔄 Convert API<br/>/api/convert]
            ServerMgr[🏗️ Server Manager<br/>Multiple MCP instances]
            InstantMCP[⚡ Instant MCP<br/>/api/instant-mcp]
        end

        subgraph "Generation Engine"
            OpenAPIParser[📋 OpenAPI Parser<br/>Parse & validate specs]
            ServerGen[⚙️ Server Generator<br/>Generate MCP code]
            ManifestGen[📄 Manifest Generator<br/>Generate MCP manifest]
        end
    end

    subgraph "Generated MCP Servers"
        subgraph "MCP Server Instance 1 (Port 8000)"
            MCPApp1[🎯 Express Server<br/>Generated from OpenAPI]

            subgraph "MCP Protocol"
                MCPEndpoint1[🔌 /mcp<br/>Protocol handler]
                ToolsRouter1[🛠️ /tools/*<br/>Tool endpoints]
                ChatEndpoint1[💬 /chat<br/>LLM integration]
            end

            subgraph "Function Calling Engine"
                ToolDefs1[📊 Tool Definitions<br/>From OpenAPI spec]
                ParamValidator1[✅ Parameter Validator<br/>Smart parameter extraction]
                ResponseFormatter1[🎨 Response Formatter<br/>User-friendly output]
            end
        end

        subgraph "MCP Server Instance N (Port 800N)"
            MCPAppN[🎯 Express Server<br/>Different OpenAPI spec]
            MCPEndpointN[🔌 /mcp]
            ToolsRouterN[🛠️ /tools/*]
            ChatEndpointN[💬 /chat]
        end
    end

    subgraph "External Services"
        LiteLLMGateway[🤖 LiteLLM Gateway<br/>localhost:4000<br/>OpenAI/Anthropic/etc]

        subgraph "Target APIs"
            PetstoreAPI[🐕 Petstore API<br/>petstore3.swagger.io]
            CustomAPI1[🔧 Custom API 1<br/>Your API service]
            CustomAPIX[🔧 Custom API X<br/>Any OpenAPI service]
        end
    end

    %% Main Flow Connections
    WebUI --> MainApp
    DirectAPI --> MainApp

    MainApp --> ConvertAPI
    MainApp --> ServerMgr
    MainApp --> InstantMCP

    ConvertAPI --> OpenAPIParser
    OpenAPIParser --> ServerGen
    OpenAPIParser --> ManifestGen
    ServerGen --> MCPApp1
    ServerGen --> MCPAppN

    ServerMgr --> MCPApp1
    ServerMgr --> MCPAppN

    %% MCP Server Internal Flow
    MCPApp1 --> MCPEndpoint1
    MCPApp1 --> ToolsRouter1
    MCPApp1 --> ChatEndpoint1

    ChatEndpoint1 --> ToolDefs1
    ToolDefs1 --> ParamValidator1
    ParamValidator1 --> MCPEndpoint1
    MCPEndpoint1 --> ToolsRouter1
    ToolsRouter1 --> ResponseFormatter1

    %% External Connections
    ChatEndpoint1 -.-> LiteLLMGateway
    ChatEndpointN -.-> LiteLLMGateway

    ToolsRouter1 -.-> PetstoreAPI
    ToolsRouter1 -.-> CustomAPI1
    ToolsRouterN -.-> CustomAPIX

    %% Styling
    classDef frontend fill:#e3f2fd
    classDef mainServer fill:#f3e5f5
    classDef mcpServer fill:#e8f5e8
    classDef external fill:#fff3e0

    class WebUI,DirectAPI frontend
    class MainApp,ConvertAPI,ServerMgr,InstantMCP,OpenAPIParser,ServerGen,ManifestGen mainServer
    class MCPApp1,MCPAppN,MCPEndpoint1,MCPEndpointN,ToolsRouter1,ToolsRouterN,ChatEndpoint1,ChatEndpointN,ToolDefs1,ParamValidator1,ResponseFormatter1 mcpServer
    class LiteLLMGateway,PetstoreAPI,CustomAPI1,CustomAPIX external
```

## 📊 Architecture Components

### Frontend Layer
- **Web UI**: React application for user interaction
- **Direct API**: REST endpoints for programmatic access

### Main Server (Port 3000)
- **Convert API** (`/api/convert`): Converts OpenAPI specs to MCP servers
- **Server Manager**: Manages multiple MCP server instances
- **Instant MCP** (`/api/instant-mcp`): Quick server generation
- **Generation Engine**: 
  - OpenAPI Parser: Validates and parses specifications
  - Server Generator: Creates MCP server code
  - Manifest Generator: Generates MCP manifests

### Generated MCP Servers (Ports 8000+)
Each OpenAPI spec generates a dedicated MCP server with:

#### MCP Protocol Endpoints
- **`/mcp`**: Main protocol handler for tool calls
- **`/tools/*`**: Individual tool endpoints for each OpenAPI operation
- **`/chat`**: LLM integration with function calling support
- **`/health`**: Health check endpoint

#### Function Calling Engine
- **Tool Definitions**: Auto-generated from OpenAPI operations
- **Parameter Validator**: Smart parameter extraction and validation
- **Response Formatter**: User-friendly output formatting

## 🔄 Function Calling Sequence Diagram

```mermaid
sequenceDiagram
    participant User
    participant UI as Web UI<br/>(localhost:3001)
    participant MainServer as Main Server<br/>(localhost:3000)
    participant MCPServer as Generated MCP Server<br/>(localhost:800X)
    participant LiteLLM as LiteLLM Gateway<br/>(localhost:4000)
    participant TargetAPI as Target OpenAPI<br/>(e.g., Petstore)

    %% Initial Setup
    Note over User,TargetAPI: 1. Server Generation Phase
    User->>UI: 1. Enter OpenAPI URL
    UI->>MainServer: POST /api/convert
    MainServer->>MainServer: Parse OpenAPI spec
    MainServer->>MainServer: Generate MCP server code
    MainServer->>MCPServer: Deploy & Start MCP Server
    MCPServer-->>MainServer: Server running on port 800X
    MainServer-->>UI: Server created successfully
    UI-->>User: Show server details

    %% Function Calling Flow
    Note over User,TargetAPI: 2. Function Calling Phase
    User->>MCPServer: POST /chat<br/>{"message": "Show me available pets"}

    %% LLM Request with Tools
    MCPServer->>MCPServer: Generate OpenAPI tools from spec
    Note right of MCPServer: Tools: [findPetsByStatus, addPet, updatePet, ...]

    MCPServer->>LiteLLM: POST /chat/completions<br/>{<br/>  "model": "gpt-3.5-turbo",<br/>  "messages": [...],<br/>  "tools": [...],<br/>  "tool_choice": "auto"<br/>}

    %% LLM Response with Tool Call
    LiteLLM-->>MCPServer: {<br/>  "choices": [{<br/>    "message": {<br/>      "tool_calls": [{<br/>        "function": {<br/>          "name": "findPetsByStatus",<br/>          "arguments": "{\"query\":{\"status\":\"available\"}}"<br/>        }<br/>      }]<br/>    }<br/>  }]<br/>}

    %% Tool Call Processing
    MCPServer->>MCPServer: Extract tool call:<br/>name="findPetsByStatus"<br/>args={"query":{"status":"available"}}

    MCPServer->>MCPServer: Validate parameters against user message
    Note right of MCPServer: Check if "available" matches user intent

    %% MCP Protocol Call
    MCPServer->>MCPServer: POST /mcp<br/>{<br/>  "tool": "findPetsByStatus",<br/>  "parameters": {"query":{"status":"available"}}<br/>}

    %% Tool Execution
    MCPServer->>MCPServer: Route to /tools/findPetsByStatus
    MCPServer->>TargetAPI: GET /pet/findByStatus?status=available
    TargetAPI-->>MCPServer: [{"id":1,"name":"Fluffy","status":"available"}, ...]

    %% Response Formatting
    MCPServer->>MCPServer: Format response for better UX:<br/>Filter pet names, limit results

    MCPServer-->>User: {<br/>  "response": "Found 25 pets (showing 20 with valid names):\n\n1. Fluffy\n2. Max\n3. Bella\n...",<br/>  "toolCall": {<br/>    "name": "findPetsByStatus",<br/>    "parameters": {...},<br/>    "result": {...}<br/>  }<br/>}

    %% Alternative Flow - No Tool Call
    Note over User,TargetAPI: 3. Alternative: Direct LLM Response
    User->>MCPServer: POST /chat<br/>{"message": "What is MCP?"}
    MCPServer->>LiteLLM: POST /chat/completions<br/>(same tools available)
    LiteLLM-->>MCPServer: {<br/>  "choices": [{<br/>    "message": {<br/>      "content": "MCP stands for Model Context Protocol..."<br/>    }<br/>  }]<br/>}
    MCPServer-->>User: {"response": "MCP stands for Model Context Protocol..."}

    %% Error Handling Flow
    Note over User,TargetAPI: 4. Error Handling & Fallback
    User->>MCPServer: POST /chat<br/>{"message": "Show pending pets"}
    MCPServer->>LiteLLM: Request with tools
    LiteLLM-->>MCPServer: Tool call: findPetsByStatus
    MCPServer->>MCPServer: MCP call fails
    MCPServer->>TargetAPI: Direct API fallback call
    TargetAPI-->>MCPServer: Fallback response
    MCPServer-->>User: Response with fallback note
```

## 🔄 Function Calling Flow

## 🌊 Overall System Flow Diagram

```mermaid
graph TB
    %% User Interaction Layer
    User[👤 User] --> UI[🖥️ Web UI<br/>localhost:3001]
    User --> API[📡 Direct API<br/>localhost:3000]

    %% Main Server Components
    UI --> MainServer[🚀 Main Server<br/>src/server.ts<br/>Port 3000]
    API --> MainServer

    %% Core Generation Flow
    MainServer --> Convert[🔄 Convert Endpoint<br/>/api/convert]
    Convert --> Parser[📋 OpenAPI Parser<br/>src/core/openapiParser.ts]
    Parser --> Generator[⚙️ Server Generator<br/>src/core/serverGenerator.ts]
    Generator --> MCPServer[🎯 Generated MCP Server<br/>tmp/xxx/src/server.ts<br/>Dynamic Port]

    %% MCP Server Components
    MCPServer --> ToolEndpoints[🛠️ Tool Endpoints<br/>/tools/xxx]
    MCPServer --> MCPProtocol[🔌 MCP Protocol<br/>/mcp]
    MCPServer --> ChatEndpoint[💬 Chat Endpoint<br/>/chat]

    %% Function Calling Flow
    subgraph "Function Calling Flow"
        ChatEndpoint --> LLMRequest[📤 LLM Request<br/>LiteLLM Gateway<br/>localhost:4000]
        LLMRequest --> LLMResponse[📥 LLM Response<br/>with tool_calls]
        LLMResponse --> ToolCall{🎯 Tool Call<br/>Detected?}

        ToolCall -->|Yes| ExtractParams[📊 Extract Parameters<br/>from tool_calls.function.arguments]
        ExtractParams --> ValidateParams[✅ Validate Parameters<br/>against user message]
        ValidateParams --> CallMCP[🔌 Call /mcp endpoint<br/>with tool + parameters]

        ToolCall -->|No| DirectResponse[💬 Return LLM Response]
    end

    %% MCP Protocol Flow
    subgraph "MCP Protocol Flow"
        CallMCP --> MCPHandler[🎛️ MCP Handler<br/>POST /mcp]
        MCPHandler --> RouteToTool[🎯 Route to Tool<br/>POST /tools/{toolName}]
        RouteToTool --> ToolHandler[⚡ Tool Handler<br/>Execute API Call]
        ToolHandler --> APICall[🌐 External API Call<br/>to target OpenAPI service]
        APICall --> ToolResponse[📋 Tool Response]
        ToolResponse --> FormatResponse[🎨 Format Response<br/>for better UX]
        FormatResponse --> FinalResponse[✨ Final Response to User]
    end

    %% External Services
    subgraph "External Services"
        LLMGateway[🤖 LiteLLM Gateway<br/>localhost:4000<br/>OpenAI/Anthropic/etc]
        TargetAPI[🎯 Target OpenAPI Service<br/>e.g., Petstore API<br/>https://petstore3.swagger.io]
    end

    LLMRequest -.-> LLMGateway
    LLMGateway -.-> LLMResponse
    APICall -.-> TargetAPI
    TargetAPI -.-> ToolResponse

    %% Server Management
    MainServer --> ServerManager[🏗️ Server Manager<br/>src/services/serverManager.ts]
    ServerManager --> MultipleServers[🔄 Multiple MCP Servers<br/>Port 8000, 8001, 8002...]

    %% Data Flow Annotations
    classDef userLayer fill:#e1f5fe
    classDef mainServer fill:#f3e5f5
    classDef mcpServer fill:#e8f5e8
    classDef external fill:#fff3e0
    classDef functionCall fill:#fce4ec

    class User,UI userLayer
    class MainServer,Convert,Parser,Generator,ServerManager mainServer
    class MCPServer,ToolEndpoints,MCPProtocol,ChatEndpoint,MCPHandler,RouteToTool,ToolHandler mcpServer
    class LLMGateway,TargetAPI external
    class LLMRequest,LLMResponse,ToolCall,ExtractParams,ValidateParams,CallMCP functionCall
```

### 1. Server Generation Phase
```
User Input (OpenAPI URL) → Parser → Generator → MCP Server Deployment
```

### 2. Function Calling Phase
```
User Message → LLM Request (with tools) → Tool Call Detection → 
Parameter Extraction → MCP Protocol → Tool Execution → API Call → 
Response Formatting → User Response
```

### 3. Detailed Sequence

1. **User sends chat message**: `"Show me available pets"`
2. **Tool definitions generated**: From OpenAPI spec operations
3. **LLM request with tools**: Sent to LiteLLM Gateway
4. **LLM responds with tool call**:
   ```json
   {
     "tool_calls": [{
       "function": {
         "name": "findPetsByStatus",
         "arguments": "{\"query\":{\"status\":\"available\"}}"
       }
     }]
   }
   ```
5. **Parameter extraction**: Parse and validate arguments
6. **MCP protocol call**: Route through `/mcp` endpoint
7. **Tool execution**: Call actual OpenAPI endpoint
8. **Response formatting**: Format for better UX
9. **User receives response**: Formatted, user-friendly output

## 🔌 MCP Protocol Integration

### Standard Interface
- **Unified endpoint**: All tool calls go through `/mcp`
- **Tool discovery**: Auto-generated from OpenAPI spec
- **Error handling**: Graceful fallbacks and error messages
- **Parameter validation**: Smart parameter extraction and correction

### Tool Definition Format
```typescript
{
  type: "function",
  function: {
    name: "findPetsByStatus",
    description: "Find pets by status",
    parameters: {
      type: "object",
      properties: {
        query: {
          type: "object",
          properties: {
            status: {
              type: "string",
              enum: ["available", "pending", "sold"]
            }
          }
        }
      }
    }
  }
}
```

## 🚀 Key Features

### Function Calling
- **Auto tool detection**: LLM automatically chooses appropriate tools
- **Parameter validation**: Smart extraction from user messages
- **Smart routing**: Efficient routing to correct endpoints
- **Error recovery**: Fallback mechanisms for failed calls

### MCP Protocol
- **Standard interface**: Consistent API across all generated servers
- **Tool discovery**: Dynamic tool generation from OpenAPI specs
- **Error handling**: Comprehensive error handling and logging
- **Multi-server support**: Multiple APIs can run simultaneously

### LLM Integration
- **Multiple providers**: Support for OpenAI, Anthropic, etc. via LiteLLM
- **Tool calling support**: Native function calling capabilities
- **Context management**: Intelligent context handling for better responses
- **Response formatting**: User-friendly output formatting

### Multi-Tenant Architecture
- **Multiple APIs**: Support for multiple OpenAPI specifications
- **Isolated servers**: Each API gets its own server instance
- **Dynamic ports**: Automatic port allocation for new servers
- **Resource management**: Efficient resource usage and cleanup

## 🔧 Technical Implementation

### Server Generation
1. **Parse OpenAPI spec**: Validate and extract operations
2. **Generate TypeScript code**: Create Express server with MCP endpoints
3. **Create tool definitions**: Convert operations to LLM-callable functions
4. **Deploy server**: Start new server instance on available port
5. **Register with manager**: Track server for management

### Function Call Processing
1. **Receive chat message**: User input via `/chat` endpoint
2. **Generate tool context**: Create tools array from OpenAPI spec
3. **Send to LLM**: Include tools in LiteLLM request
4. **Process response**: Check for tool calls in LLM response
5. **Execute tool**: Route through MCP protocol to actual API
6. **Format response**: Create user-friendly output

### Error Handling
- **Validation errors**: Parameter validation and correction
- **API errors**: Graceful handling of external API failures
- **Fallback mechanisms**: Direct API calls when MCP fails
- **Logging**: Comprehensive logging for debugging

## 📁 File Structure

```
src/
├── server.ts              # Main server entry point
├── core/
│   ├── openapiParser.ts   # OpenAPI specification parser
│   ├── serverGenerator.ts # MCP server code generator
│   └── mcpManifestGenerator.ts # MCP manifest generator
├── routes/
│   ├── convert.ts         # Conversion API endpoints
│   ├── servers.ts         # Server management endpoints
│   └── instantMcp.ts      # Instant MCP creation
├── services/
│   └── serverManager.ts   # Multi-server management
└── types.ts               # TypeScript type definitions

Generated MCP Servers:
tmp/
└── {server-id}/
    ├── src/
    │   ├── server.ts      # Generated MCP server
    │   ├── routes.ts      # Tool endpoint handlers
    │   └── types.ts       # Generated type definitions
    ├── package.json       # Dependencies
    └── tsconfig.json      # TypeScript configuration
```

## 🌐 External Services

### LiteLLM Gateway (localhost:4000)
- **Purpose**: LLM provider abstraction
- **Supported providers**: OpenAI, Anthropic, Azure, etc.
- **Function calling**: Native tool calling support
- **Configuration**: Via environment variables

### Target APIs
- **Petstore API**: Example OpenAPI service
- **Custom APIs**: Any OpenAPI 3.0+ compliant service
- **Authentication**: Supports various auth methods
- **Rate limiting**: Respects API rate limits

## 🔐 Environment Configuration

```bash
# LiteLLM Configuration
LITELLM_URL=http://localhost:4000/chat/completions
LITELLM_MODEL=gpt-3.5-turbo
LITELLM_API_KEY=your_api_key_here

# Server Configuration
PORT=3000
NODE_ENV=development
CORS_ORIGIN=*

# Generated Server Configuration
BASE_URL=https://petstore3.swagger.io/api/v3
```

## 🚀 Getting Started

1. **Start the main server**: `npm run dev`
2. **Start LiteLLM Gateway**: `litellm --config config.yaml`
3. **Access Web UI**: http://localhost:3001
4. **Generate MCP server**: Enter OpenAPI URL and click generate
5. **Test function calling**: Use the chat interface to interact with your API

## 📝 Example Usage

```bash
# Generate MCP server
curl -X POST http://localhost:3000/api/convert \
  -H "Content-Type: application/json" \
  -d '{"openapi": "https://petstore3.swagger.io/api/v3/openapi.json"}'

# Chat with generated server
curl -X POST http://localhost:8000/chat \
  -H "Content-Type: application/json" \
  -d '{"message": "Show me all available pets"}'
```

This architecture enables any OpenAPI service to become an intelligent, LLM-callable tool through the MCP protocol! 🎉
