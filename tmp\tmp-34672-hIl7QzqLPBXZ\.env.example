# Generated MCP Server Environment Configuration

# Server Configuration
PORT=8000
BASE_URL=https://petstore3.swagger.io/api/v3
NODE_ENV=development

# API Configuration
# Add any API keys or authentication tokens here
# API_KEY=your_api_key_here
# AUTH_TOKEN=your_auth_token_here

# Logging
LOG_LEVEL=info
LITELLM_URL=https://litellm-production-744f.up.railway.app/chat/completions
LITELLM_MODEL=deepseek-chat
LITELLM_API_KEY=sk-railway-litellm-2024
# CORS Configuration
# CORS_ORIGIN=http://localhost:3000
