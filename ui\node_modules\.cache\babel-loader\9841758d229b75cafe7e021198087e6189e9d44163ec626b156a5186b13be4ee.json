{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst FileAudio2 = createLucideIcon(\"FileAudio2\", [[\"path\", {\n  d: \"M4 22h14a2 2 0 0 0 2-2V7.5L14.5 2H6a2 2 0 0 0-2 2v2\",\n  key: \"fkyf72\"\n}], [\"polyline\", {\n  points: \"14 2 14 8 20 8\",\n  key: \"1ew0cm\"\n}], [\"path\", {\n  d: \"M2 17v-3a4 4 0 0 1 8 0v3\",\n  key: \"1ggdre\"\n}], [\"circle\", {\n  cx: \"9\",\n  cy: \"17\",\n  r: \"1\",\n  key: \"bc1fq4\"\n}], [\"circle\", {\n  cx: \"3\",\n  cy: \"17\",\n  r: \"1\",\n  key: \"vo6nti\"\n}]]);\nexport { FileAudio2 as default };", "map": {"version": 3, "names": ["FileAudio2", "createLucideIcon", "d", "key", "points", "cx", "cy", "r"], "sources": ["D:\\repos-personal\\repos\\openapi-to-mcp\\ui\\node_modules\\lucide-react\\src\\icons\\file-audio-2.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name FileAudio2\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNCAyMmgxNGEyIDIgMCAwIDAgMi0yVjcuNUwxNC41IDJINmEyIDIgMCAwIDAtMiAydjIiIC8+CiAgPHBvbHlsaW5lIHBvaW50cz0iMTQgMiAxNCA4IDIwIDgiIC8+CiAgPHBhdGggZD0iTTIgMTd2LTNhNCA0IDAgMCAxIDggMHYzIiAvPgogIDxjaXJjbGUgY3g9IjkiIGN5PSIxNyIgcj0iMSIgLz4KICA8Y2lyY2xlIGN4PSIzIiBjeT0iMTciIHI9IjEiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/file-audio-2\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst FileAudio2 = createLucideIcon('FileAudio2', [\n  [\n    'path',\n    { d: 'M4 22h14a2 2 0 0 0 2-2V7.5L14.5 2H6a2 2 0 0 0-2 2v2', key: 'fkyf72' },\n  ],\n  ['polyline', { points: '14 2 14 8 20 8', key: '1ew0cm' }],\n  ['path', { d: 'M2 17v-3a4 4 0 0 1 8 0v3', key: '1ggdre' }],\n  ['circle', { cx: '9', cy: '17', r: '1', key: 'bc1fq4' }],\n  ['circle', { cx: '3', cy: '17', r: '1', key: 'vo6nti' }],\n]);\n\nexport default FileAudio2;\n"], "mappings": ";;;;;AAaM,MAAAA,UAAA,GAAaC,gBAAA,CAAiB,YAAc,GAChD,CACE,QACA;EAAEC,CAAA,EAAG,qDAAuD;EAAAC,GAAA,EAAK;AAAS,EAC5E,EACA,CAAC,UAAY;EAAEC,MAAA,EAAQ,gBAAkB;EAAAD,GAAA,EAAK;AAAA,CAAU,GACxD,CAAC,MAAQ;EAAED,CAAA,EAAG,0BAA4B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzD,CAAC,QAAU;EAAEE,EAAI;EAAKC,EAAI;EAAMC,CAAG;EAAKJ,GAAK;AAAA,CAAU,GACvD,CAAC,QAAU;EAAEE,EAAI;EAAKC,EAAI;EAAMC,CAAG;EAAKJ,GAAK;AAAA,CAAU,EACxD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}