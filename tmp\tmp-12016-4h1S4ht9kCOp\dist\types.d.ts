export interface ToolRequest {
    [key: string]: any;
}
export interface ToolResponse {
    success: boolean;
    data?: any;
    error?: string;
    message?: string;
    status?: number;
}
export interface HealthResponse {
    status: string;
    name: string;
    version: string;
    timestamp: string;
    baseUrl: string;
}
export interface MCPManifest {
    tags?: any[];
}
export declare const openApiTools: ({
    type: string;
    function: {
        name: string;
        description: string;
        parameters: {
            type: string;
            properties: {
                body: {
                    type: string;
                    description: string;
                };
                query?: undefined;
                petId?: undefined;
                headers?: undefined;
                orderId?: undefined;
                username?: undefined;
            };
            required: string[];
        };
    };
} | {
    type: string;
    function: {
        name: string;
        description: string;
        parameters: {
            type: string;
            properties: {
                query: {
                    type: string;
                    description: string;
                    properties: {
                        status: {
                            type: string;
                            description: string;
                        };
                        tags?: undefined;
                        name?: undefined;
                        additionalMetadata?: undefined;
                        username?: undefined;
                        password?: undefined;
                    };
                };
                body?: undefined;
                petId?: undefined;
                headers?: undefined;
                orderId?: undefined;
                username?: undefined;
            };
            required: never[];
        };
    };
} | {
    type: string;
    function: {
        name: string;
        description: string;
        parameters: {
            type: string;
            properties: {
                query: {
                    type: string;
                    description: string;
                    properties: {
                        tags: {
                            type: string;
                            description: string;
                        };
                        status?: undefined;
                        name?: undefined;
                        additionalMetadata?: undefined;
                        username?: undefined;
                        password?: undefined;
                    };
                };
                body?: undefined;
                petId?: undefined;
                headers?: undefined;
                orderId?: undefined;
                username?: undefined;
            };
            required: never[];
        };
    };
} | {
    type: string;
    function: {
        name: string;
        description: string;
        parameters: {
            type: string;
            properties: {
                petId: {
                    type: string;
                    description: string;
                };
                body?: undefined;
                query?: undefined;
                headers?: undefined;
                orderId?: undefined;
                username?: undefined;
            };
            required: string[];
        };
    };
} | {
    type: string;
    function: {
        name: string;
        description: string;
        parameters: {
            type: string;
            properties: {
                petId: {
                    type: string;
                    description: string;
                };
                query: {
                    type: string;
                    description: string;
                    properties: {
                        name: {
                            type: string;
                            description: string;
                        };
                        status: {
                            type: string;
                            description: string;
                        };
                        tags?: undefined;
                        additionalMetadata?: undefined;
                        username?: undefined;
                        password?: undefined;
                    };
                };
                body?: undefined;
                headers?: undefined;
                orderId?: undefined;
                username?: undefined;
            };
            required: string[];
        };
    };
} | {
    type: string;
    function: {
        name: string;
        description: string;
        parameters: {
            type: string;
            properties: {
                petId: {
                    type: string;
                    description: string;
                };
                headers: {
                    type: string;
                    description: string;
                    properties: {
                        api_key: {
                            type: string;
                            description: string;
                        };
                    };
                };
                body?: undefined;
                query?: undefined;
                orderId?: undefined;
                username?: undefined;
            };
            required: string[];
        };
    };
} | {
    type: string;
    function: {
        name: string;
        description: string;
        parameters: {
            type: string;
            properties: {
                petId: {
                    type: string;
                    description: string;
                };
                query: {
                    type: string;
                    description: string;
                    properties: {
                        additionalMetadata: {
                            type: string;
                            description: string;
                        };
                        status?: undefined;
                        tags?: undefined;
                        name?: undefined;
                        username?: undefined;
                        password?: undefined;
                    };
                };
                body: {
                    type: string;
                    description: string;
                };
                headers?: undefined;
                orderId?: undefined;
                username?: undefined;
            };
            required: string[];
        };
    };
} | {
    type: string;
    function: {
        name: string;
        description: string;
        parameters: {
            type: string;
            properties: {
                body?: undefined;
                query?: undefined;
                petId?: undefined;
                headers?: undefined;
                orderId?: undefined;
                username?: undefined;
            };
            required: never[];
        };
    };
} | {
    type: string;
    function: {
        name: string;
        description: string;
        parameters: {
            type: string;
            properties: {
                orderId: {
                    type: string;
                    description: string;
                };
                body?: undefined;
                query?: undefined;
                petId?: undefined;
                headers?: undefined;
                username?: undefined;
            };
            required: string[];
        };
    };
} | {
    type: string;
    function: {
        name: string;
        description: string;
        parameters: {
            type: string;
            properties: {
                query: {
                    type: string;
                    description: string;
                    properties: {
                        username: {
                            type: string;
                            description: string;
                        };
                        password: {
                            type: string;
                            description: string;
                        };
                        status?: undefined;
                        tags?: undefined;
                        name?: undefined;
                        additionalMetadata?: undefined;
                    };
                };
                body?: undefined;
                petId?: undefined;
                headers?: undefined;
                orderId?: undefined;
                username?: undefined;
            };
            required: never[];
        };
    };
} | {
    type: string;
    function: {
        name: string;
        description: string;
        parameters: {
            type: string;
            properties: {
                username: {
                    type: string;
                    description: string;
                };
                body?: undefined;
                query?: undefined;
                petId?: undefined;
                headers?: undefined;
                orderId?: undefined;
            };
            required: string[];
        };
    };
} | {
    type: string;
    function: {
        name: string;
        description: string;
        parameters: {
            type: string;
            properties: {
                username: {
                    type: string;
                    description: string;
                };
                body: {
                    type: string;
                    description: string;
                };
                query?: undefined;
                petId?: undefined;
                headers?: undefined;
                orderId?: undefined;
            };
            required: string[];
        };
    };
})[];
export interface addPetRequest {
    body: any;
    tags?: any[];
}
export interface updatePetRequest {
    body: any;
    tags?: any[];
}
export interface findPetsByStatusRequest {
    query?: {
        status?: string;
    };
    tags?: any[];
}
export interface findPetsByTagsRequest {
    query?: {
        tags?: any[];
    };
    tags?: any[];
}
export interface getPetByIdRequest {
    petId: string;
    tags?: any[];
}
export interface updatePetWithFormRequest {
    petId: string;
    query?: {
        name?: string;
        status?: string;
    };
    tags?: any[];
}
export interface deletePetRequest {
    petId: string;
    headers?: {
        api_key?: string;
    };
    tags?: any[];
}
export interface uploadFileRequest {
    petId: string;
    query?: {
        additionalMetadata?: string;
    };
    body?: any;
    tags?: any[];
}
export interface getInventoryRequest {
    tags?: any[];
}
export interface placeOrderRequest {
    body?: any;
    tags?: any[];
}
export interface getOrderByIdRequest {
    orderId: string;
    tags?: any[];
}
export interface deleteOrderRequest {
    orderId: string;
    tags?: any[];
}
export interface createUserRequest {
    body?: any;
    tags?: any[];
}
export interface createUsersWithListInputRequest {
    body?: any;
    tags?: any[];
}
export interface loginUserRequest {
    query?: {
        username?: string;
        password?: string;
    };
    tags?: any[];
}
export interface logoutUserRequest {
    tags?: any[];
}
export interface getUserByNameRequest {
    username: string;
    tags?: any[];
}
export interface updateUserRequest {
    username: string;
    body?: any;
    tags?: any[];
}
export interface deleteUserRequest {
    username: string;
    tags?: any[];
}
