{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst CalendarClock = createLucideIcon(\"CalendarClock\", [[\"path\", {\n  d: \"M21 7.5V6a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h3.5\",\n  key: \"1osxxc\"\n}], [\"path\", {\n  d: \"M16 2v4\",\n  key: \"4m81vk\"\n}], [\"path\", {\n  d: \"M8 2v4\",\n  key: \"1cmpym\"\n}], [\"path\", {\n  d: \"M3 10h5\",\n  key: \"r794hk\"\n}], [\"path\", {\n  d: \"M17.5 17.5 16 16.25V14\",\n  key: \"re2vv1\"\n}], [\"path\", {\n  d: \"M22 16a6 6 0 1 1-12 0 6 6 0 0 1 12 0Z\",\n  key: \"ame013\"\n}]]);\nexport { CalendarClock as default };", "map": {"version": 3, "names": ["CalendarClock", "createLucideIcon", "d", "key"], "sources": ["D:\\repos-personal\\repos\\openapi-to-mcp\\ui\\node_modules\\lucide-react\\src\\icons\\calendar-clock.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name CalendarClock\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjEgNy41VjZhMiAyIDAgMCAwLTItMkg1YTIgMiAwIDAgMC0yIDJ2MTRhMiAyIDAgMCAwIDIgMmgzLjUiIC8+CiAgPHBhdGggZD0iTTE2IDJ2NCIgLz4KICA8cGF0aCBkPSJNOCAydjQiIC8+CiAgPHBhdGggZD0iTTMgMTBoNSIgLz4KICA8cGF0aCBkPSJNMTcuNSAxNy41IDE2IDE2LjI1VjE0IiAvPgogIDxwYXRoIGQ9Ik0yMiAxNmE2IDYgMCAxIDEtMTIgMCA2IDYgMCAwIDEgMTIgMFoiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/calendar-clock\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst CalendarClock = createLucideIcon('CalendarClock', [\n  [\n    'path',\n    {\n      d: 'M21 7.5V6a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h3.5',\n      key: '1osxxc',\n    },\n  ],\n  ['path', { d: 'M16 2v4', key: '4m81vk' }],\n  ['path', { d: 'M8 2v4', key: '1cmpym' }],\n  ['path', { d: 'M3 10h5', key: 'r794hk' }],\n  ['path', { d: 'M17.5 17.5 16 16.25V14', key: 're2vv1' }],\n  ['path', { d: 'M22 16a6 6 0 1 1-12 0 6 6 0 0 1 12 0Z', key: 'ame013' }],\n]);\n\nexport default CalendarClock;\n"], "mappings": ";;;;;AAaM,MAAAA,aAAA,GAAgBC,gBAAA,CAAiB,eAAiB,GACtD,CACE,QACA;EACEC,CAAG;EACHC,GAAK;AACP,EACF,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,QAAU;EAAAC,GAAA,EAAK;AAAA,CAAU,GACvC,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,wBAA0B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACvD,CAAC,MAAQ;EAAED,CAAA,EAAG,uCAAyC;EAAAC,GAAA,EAAK;AAAA,CAAU,EACvE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}