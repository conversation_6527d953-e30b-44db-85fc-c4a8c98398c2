{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst BellOff = createLucideIcon(\"BellOff\", [[\"path\", {\n  d: \"M8.7 3A6 6 0 0 1 18 8a21.3 21.3 0 0 0 .6 5\",\n  key: \"o7mx20\"\n}], [\"path\", {\n  d: \"M17 17H3s3-2 3-9a4.67 4.67 0 0 1 .3-1.7\",\n  key: \"16f1lm\"\n}], [\"path\", {\n  d: \"M10.3 21a1.94 1.94 0 0 0 3.4 0\",\n  key: \"qgo35s\"\n}], [\"path\", {\n  d: \"m2 2 20 20\",\n  key: \"1ooewy\"\n}]]);\nexport { BellOff as default };", "map": {"version": 3, "names": ["Bell<PERSON>ff", "createLucideIcon", "d", "key"], "sources": ["D:\\repos-personal\\repos\\openapi-to-mcp\\ui\\node_modules\\lucide-react\\src\\icons\\bell-off.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name BellOff\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNOC43IDNBNiA2IDAgMCAxIDE4IDhhMjEuMyAyMS4zIDAgMCAwIC42IDUiIC8+CiAgPHBhdGggZD0iTTE3IDE3SDNzMy0yIDMtOWE0LjY3IDQuNjcgMCAwIDEgLjMtMS43IiAvPgogIDxwYXRoIGQ9Ik0xMC4zIDIxYTEuOTQgMS45NCAwIDAgMCAzLjQgMCIgLz4KICA8cGF0aCBkPSJtMiAyIDIwIDIwIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/bell-off\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst BellOff = createLucideIcon('BellOff', [\n  ['path', { d: 'M8.7 3A6 6 0 0 1 18 8a21.3 21.3 0 0 0 .6 5', key: 'o7mx20' }],\n  ['path', { d: 'M17 17H3s3-2 3-9a4.67 4.67 0 0 1 .3-1.7', key: '16f1lm' }],\n  ['path', { d: 'M10.3 21a1.94 1.94 0 0 0 3.4 0', key: 'qgo35s' }],\n  ['path', { d: 'm2 2 20 20', key: '1ooewy' }],\n]);\n\nexport default BellOff;\n"], "mappings": ";;;;;AAaM,MAAAA,OAAA,GAAUC,gBAAA,CAAiB,SAAW,GAC1C,CAAC,MAAQ;EAAEC,CAAA,EAAG,4CAA8C;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3E,CAAC,MAAQ;EAAED,CAAA,EAAG,yCAA2C;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxE,CAAC,MAAQ;EAAED,CAAA,EAAG,gCAAkC;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC/D,CAAC,MAAQ;EAAED,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC5C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}