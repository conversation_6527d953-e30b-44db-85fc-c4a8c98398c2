
import dotenv from 'dotenv';
import * as path from 'path';
import fs from 'fs';

// Try to load .env from both root and dist (for manual runs)
const envPathRoot = path.resolve(__dirname, '../.env');
const envPathDist = path.resolve(__dirname, '../../.env');
if (fs.existsSync(envPathRoot)) {
  dotenv.config({ path: envPathRoot });
} else if (fs.existsSync(envPathDist)) {
  dotenv.config({ path: envPathDist });
} else {
  dotenv.config();
}

import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import axios from 'axios';
import { router as apiRoutes } from './routes';

const app = express();
const PORT = process.env.PORT || 8000;
const BASE_URL = process.env.BASE_URL || 'http://localhost:8000';

// Security middleware
app.use(helmet());
app.use(cors());

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Health check
app.get('/health', (req: express.Request, res: express.Response) => {
  res.json({
    status: 'healthy',
    name: 'instant-mcp',
    version: '1.0.0',
    timestamp: new Date().toISOString(),
    baseUrl: BASE_URL
  });
});

// MCP tool endpoints
app.use('/tools', apiRoutes);

// MCP protocol handler
app.post('/mcp', async (req: express.Request, res: express.Response) => {
  try {
    const { tool, parameters } = req.body;
    
    // Route to appropriate tool handler
    const response = await fetch(`http://localhost:${PORT}/tools/${tool}`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(parameters)
    });
    
    const result = await response.json();
    
    res.json({
      success: true,
      result
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : String(error)
    });
  }
});

// LLM Chat endpoint with OpenAI function/tool calling support
app.post('/chat', async (req: express.Request, res: express.Response) => {
  const { message } = req.body;
  if (!message) {
    return res.status(400).json({ error: 'No message provided' });
  }

  try {
    const headers: Record<string, string> = {};
    if (process.env.LITELLM_API_KEY) {
      headers['Authorization'] = `Bearer ${process.env.LITELLM_API_KEY}`;
    }

    const llmUrl = process.env.LITELLM_URL || 'http://localhost:4000/chat/completions';
    const llmModel = process.env.LITELLM_MODEL || 'gpt-3.5-turbo';

    // Define available tools/functions for the LLM
    const tools = [
      {
        type: "function",
        function: {
          name: "findPets",
          description: "Finds pets by status",
          parameters: {
            type: "object",
            properties: {
              status: {
                type: "string",
                description: "Status of pet (available, pending, sold)"
              }
            },
            required: []
          }
        }
      },
      {
        type: "function",
        function: {
          name: "getPetById",
          description: "Get a pet by its ID",
          parameters: {
            type: "object",
            properties: {
              petId: {
                type: "integer",
                description: "ID of the pet"
              }
            },
            required: ["petId"]
          }
        }
      }
      // Add more tool definitions here as needed
    ];

    // Compose the OpenAI/LiteLLM request with tool calling
    const payload: any = {
      model: llmModel,
      messages: [
        { role: 'system', content: 'You are an API assistant. Help the user interact with the API using natural language.' },
        { role: 'user', content: message }
      ],
      tools,
      tool_choice: "auto"
    };

    console.log('[MCP] LLM Chat request (with tools):', {
      url: llmUrl,
      model: llmModel,
      apiKeyPresent: !!process.env.LITELLM_API_KEY,
      headers,
      message,
      tools
    });

    const llmRes = await axios.post(llmUrl, payload, { headers });

    // Check if the LLM wants to call a function/tool
    const toolCall = llmRes.data?.choices?.[0]?.message?.tool_calls?.[0];
    if (toolCall && toolCall.function) {
      const { name, arguments: argsJson } = toolCall.function;
      let args: any = {};
      try {
        args = JSON.parse(argsJson);
      } catch (e) {
        args = {};
      }

      // Call the corresponding tool endpoint
      let toolResponse;
      if (name === "findPets") {
        // POST to /tools/findPets with { query: { status: ... } }
        const apiRes = await axios.post(
          `${process.env.BASE_URL || 'http://localhost:' + PORT}/tools/findPets`,
          { query: args }
        );
        toolResponse = apiRes.data;
      } else if (name === "getPetById") {
        // POST to /tools/getPetById with { petId }
        const apiRes = await axios.post(
          `${process.env.BASE_URL || 'http://localhost:' + PORT}/tools/getPetById`,
          { petId: args.petId }
        );
        toolResponse = apiRes.data;
      } else {
        toolResponse = { error: "Unknown tool called: " + name };
      }

      // Return the tool response to the user
      return res.json({ response: toolResponse });
    }

    // Otherwise, return the LLM's message as usual
    const llmMessage = llmRes.data?.choices?.[0]?.message?.content || llmRes.data?.choices?.[0]?.text || JSON.stringify(llmRes.data);

    res.json({ response: llmMessage });
  } catch (err: any) {
    console.error('[MCP] LLM call failed:', err && (err.response?.data || err.message || err.toString()), err);
    res.status(500).json({ 
      error: 'LLM call failed', 
      details: err && (err.response?.data || err.message || err.toString())
    });
  }
});

// Error handling
app.use((error: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  console.error('Server error:', error);
  res.status(500).json({
    error: 'Internal server error',
    message: error instanceof Error ? error.message : String(error)
  });
});

// 404 handler
app.use('*', (req: express.Request, res: express.Response) => {
  res.status(404).json({
    error: 'Not found',
    message: `Route ${req.originalUrl} not found`
  });
});

// Start server
const server = app.listen(PORT, () => {
  console.log(`🚀 instant-mcp MCP Server running on port ${PORT}`);
  console.log(`📖 Health check: http://localhost:${PORT}/health`);
  console.log(`🔧 Base URL: ${BASE_URL}`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully');
  server.close(() => {
    console.log('Server closed');
    process.exit(0);
  });
});

export default app;
