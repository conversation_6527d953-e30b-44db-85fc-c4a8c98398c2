{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst DnaOff = createLucideIcon(\"DnaOff\", [[\"path\", {\n  d: \"M15 2c-1.35 1.5-2.092 3-2.5 4.5M9 22c1.35-1.5 2.092-3 2.5-4.5\",\n  key: \"sxiaad\"\n}], [\"path\", {\n  d: \"M2 15c3.333-3 6.667-3 10-3m10-3c-1.5 1.35-3 2.092-4.5 2.5\",\n  key: \"yn4bs1\"\n}], [\"path\", {\n  d: \"m17 6-2.5-2.5\",\n  key: \"5cdfhj\"\n}], [\"path\", {\n  d: \"m14 8-1.5-1.5\",\n  key: \"1ohn8i\"\n}], [\"path\", {\n  d: \"m7 18 2.5 2.5\",\n  key: \"16tu1a\"\n}], [\"path\", {\n  d: \"m3.5 14.5.5.5\",\n  key: \"hapbhd\"\n}], [\"path\", {\n  d: \"m20 9 .5.5\",\n  key: \"1n7z02\"\n}], [\"path\", {\n  d: \"m6.5 12.5 1 1\",\n  key: \"cs35ky\"\n}], [\"path\", {\n  d: \"m16.5 10.5 1 1\",\n  key: \"696xn5\"\n}], [\"path\", {\n  d: \"m10 16 1.5 1.5\",\n  key: \"11lckj\"\n}], [\"line\", {\n  x1: \"2\",\n  x2: \"22\",\n  y1: \"2\",\n  y2: \"22\",\n  key: \"a6p6uj\"\n}]]);\nexport { DnaOff as default };", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "createLucideIcon", "d", "key", "x1", "x2", "y1", "y2"], "sources": ["D:\\repos-personal\\repos\\openapi-to-mcp\\ui\\node_modules\\lucide-react\\src\\icons\\dna-off.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name DnaOff\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************) - https://lucide.dev/icons/dna-off\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst DnaOff = createLucideIcon('DnaOff', [\n  [\n    'path',\n    {\n      d: 'M15 2c-1.35 1.5-2.092 3-2.5 4.5M9 22c1.35-1.5 2.092-3 2.5-4.5',\n      key: 'sxiaad',\n    },\n  ],\n  [\n    'path',\n    {\n      d: 'M2 15c3.333-3 6.667-3 10-3m10-3c-1.5 1.35-3 2.092-4.5 2.5',\n      key: 'yn4bs1',\n    },\n  ],\n  ['path', { d: 'm17 6-2.5-2.5', key: '5cdfhj' }],\n  ['path', { d: 'm14 8-1.5-1.5', key: '1ohn8i' }],\n  ['path', { d: 'm7 18 2.5 2.5', key: '16tu1a' }],\n  ['path', { d: 'm3.5 14.5.5.5', key: 'hapbhd' }],\n  ['path', { d: 'm20 9 .5.5', key: '1n7z02' }],\n  ['path', { d: 'm6.5 12.5 1 1', key: 'cs35ky' }],\n  ['path', { d: 'm16.5 10.5 1 1', key: '696xn5' }],\n  ['path', { d: 'm10 16 1.5 1.5', key: '11lckj' }],\n  ['line', { x1: '2', x2: '22', y1: '2', y2: '22', key: 'a6p6uj' }],\n]);\n\nexport default DnaOff;\n"], "mappings": ";;;;;AAaM,MAAAA,MAAA,GAASC,gBAAA,CAAiB,QAAU,GACxC,CACE,QACA;EACEC,CAAG;EACHC,GAAK;AACP,EACF,EACA,CACE,QACA;EACED,CAAG;EACHC,GAAK;AACP,EACF,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,eAAiB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC9C,CAAC,MAAQ;EAAED,CAAA,EAAG,eAAiB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC9C,CAAC,MAAQ;EAAED,CAAA,EAAG,eAAiB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC9C,CAAC,MAAQ;EAAED,CAAA,EAAG,eAAiB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC9C,CAAC,MAAQ;EAAED,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,MAAQ;EAAED,CAAA,EAAG,eAAiB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC9C,CAAC,MAAQ;EAAED,CAAA,EAAG,gBAAkB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC/C,CAAC,MAAQ;EAAED,CAAA,EAAG,gBAAkB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC/C,CAAC,QAAQ;EAAEC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,IAAM;EAAAJ,GAAA,EAAK;AAAA,CAAU,EACjE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}