{"version": 3, "file": "bin.js", "sourceRoot": "", "sources": ["../src/bin.ts"], "names": [], "mappings": ";;;;AAEA,+BAA4E;AAC5E,+BAA+B;AAC/B,iCAAkC;AAClC,IAAI,GAAyB,CAAC;AAC9B,iCAA4E;AAC5E,iCAWgB;AAChB,mCAQiB;AAEjB,qGAAuF;AACvF,qDAAkD;AAClD,mDAAoD;AAEpD;;;;;;;;GAQG;AACH,SAAgB,IAAI,CAClB,OAAiB,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EACtC,iBAAsC,EAAE;IAExC,MAAM,IAAI,GAAG,SAAS,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC;IAC7C,MAAM,KAAK,GAAmB;QAC5B,qBAAqB,EAAE,KAAK;QAC5B,gBAAgB,EAAE,KAAK;QACvB,KAAK,EAAE,IAAI;QACX,YAAY,EAAE,UAAU;QACxB,eAAe,EAAE,IAAI;KACtB,CAAC;IACF,OAAO,SAAS,CAAC,KAAK,CAAC,CAAC;AAC1B,CAAC;AAbD,oBAaC;AAqBD,gBAAgB;AAChB,SAAgB,SAAS,CAAC,KAAqB;IAC7C,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE;QACvB,KAAK,CAAC,YAAY,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;QACnC,IAAI,KAAK,CAAC,qBAAqB,IAAI,CAAC,KAAK,CAAC,gBAAgB,EAAE;YAC1D,kEAAkE;YAClE,uDAAuD;YACvD,OAAO,IAAA,yBAAW,EAAC,KAAK,CAAC,CAAC;SAC3B;KACF;IACD,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE;QACvB,KAAK,CAAC,YAAY,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;QACnC,IAAI,KAAK,CAAC,qBAAqB,IAAI,CAAC,KAAK,CAAC,gBAAgB,EAAE;YAC1D,kEAAkE;YAClE,uDAAuD;YACvD,OAAO,IAAA,yBAAW,EAAC,KAAK,CAAC,CAAC;SAC3B;KACF;IACD,OAAO,MAAM,CAAC,KAAK,CAAC,CAAC;AACvB,CAAC;AAlBD,8BAkBC;AAED,SAAS,SAAS,CAAC,IAAc,EAAE,cAAmC;IACpE,GAAG,aAAH,GAAG,cAAH,GAAG,IAAH,GAAG,GAAK,OAAO,CAAC,KAAK,CAAC,EAAC;IACvB,4EAA4E;IAC5E,sGAAsG;IACtG,6DAA6D;IAC7D,oDAAoD;IACpD,cAAc,GAAG,EAAE,GAAG,cAAc,EAAE,CAAC;IACvC,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,EAAE;QAC7C,cAAc,CACZ,GAAG,CAAC,OAAO,CACT,kBAAkB,EAClB,CAAC,GAAG,EAAE,EAAE,EAAE,EAAU,EAAE,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,WAAW,EAAE,EAAE,CACpD,CACF,GAAG,cAAc,CAAC,GAAG,CAAC,CAAC;KACzB;IAED,MAAM,IAAI,GAAG;QACX,GAAG,cAAc;QACjB,GAAG,GAAG,CACJ;YACE,wBAAwB;YACxB,QAAQ,EAAE,MAAM;YAChB,eAAe,EAAE,OAAO;YACxB,SAAS,EAAE,OAAO;YAClB,WAAW,EAAE,CAAC,MAAM,CAAC;YAErB,eAAe;YACf,QAAQ,EAAE,OAAO;YACjB,WAAW,EAAE,OAAO;YACpB,cAAc,EAAE,OAAO;YACvB,WAAW,EAAE,GAAG,CAAC,KAAK;YACtB,cAAc,EAAE,OAAO;YACvB,OAAO,EAAE,OAAO;YAEhB,mBAAmB;YACnB,OAAO,EAAE,MAAM;YACf,SAAS,EAAE,OAAO;YAClB,YAAY,EAAE,MAAM;YACpB,mBAAmB,EAAE,YAAK;YAC1B,WAAW,EAAE,MAAM;YACnB,qBAAqB,EAAE,CAAC,MAAM,CAAC;YAC/B,UAAU,EAAE,CAAC,MAAM,CAAC;YACpB,iBAAiB,EAAE,OAAO;YAC1B,cAAc,EAAE,MAAM;YACtB,OAAO,EAAE,OAAO;YAChB,aAAa,EAAE,OAAO;YACtB,gBAAgB,EAAE,OAAO;YACzB,UAAU,EAAE,OAAO;YACnB,eAAe,EAAE,OAAO;YACxB,cAAc,EAAE,OAAO;YACvB,gBAAgB,EAAE,OAAO;YACzB,YAAY,EAAE,OAAO;YACrB,QAAQ,EAAE,OAAO;YACjB,SAAS,EAAE,OAAO;YAClB,YAAY,EAAE,MAAM;YACpB,2BAA2B,EAAE,OAAO;YACpC,mCAAmC,EAAE,MAAM;YAE3C,WAAW;YACX,IAAI,EAAE,QAAQ;YACd,IAAI,EAAE,eAAe;YACrB,IAAI,EAAE,SAAS;YACf,IAAI,EAAE,WAAW;YACjB,IAAI,EAAE,QAAQ;YACd,IAAI,EAAE,eAAe;YACrB,IAAI,EAAE,WAAW;YACjB,IAAI,EAAE,iBAAiB;YACvB,IAAI,EAAE,gBAAgB;YACtB,IAAI,EAAE,UAAU;YAChB,IAAI,EAAE,WAAW;YACjB,IAAI,EAAE,YAAY;YAClB,IAAI,EAAE,qBAAqB;YAC3B,IAAI,EAAE,mBAAmB;YACzB,OAAO,EAAE,OAAO;YAEhB,6EAA6E;YAC7E,YAAY,EAAE,WAAW;YACzB,eAAe,EAAE,cAAc;YAC/B,eAAe,EAAE,cAAc;YAC/B,oBAAoB,EAAE,mBAAmB;YACzC,sBAAsB,EAAE,qBAAqB;YAC7C,kBAAkB,EAAE,iBAAiB;YACrC,cAAc,EAAE,aAAa;YAC7B,iBAAiB,EAAE,gBAAgB;YACnC,gBAAgB,EAAE,eAAe;YACjC,eAAe,EAAE,cAAc;YAC/B,kBAAkB,EAAE,gBAAgB;YACpC,aAAa,EAAE,YAAY;YAC3B,aAAa,EAAE,YAAY;YAC3B,8BAA8B,EAAE,2BAA2B;YAC3D,qCAAqC,EACnC,mCAAmC;SACtC,EACD;YACE,IAAI;YACJ,gBAAgB,EAAE,IAAI;SACvB,CACF;KACF,CAAC;IAEF,+CAA+C;IAC/C,4EAA4E;IAC5E,YAAY;IACZ,MAAM,EACJ,OAAO,EAAE,MAAM,EACf,QAAQ,EAAE,IAAI,GAAG,KAAK,EACtB,cAAc,EAAE,UAAU,EAC1B,WAAW,EAAE,OAAO,EACpB,WAAW,EAAE,OAAO,GAAG,CAAC,EACxB,cAAc,EAAE,UAAU,EAC1B,WAAW,EAAE,WAAW,GAAG,EAAE,EAC7B,QAAQ,EAAE,IAAI,GAAG,SAAS,EAC1B,SAAS,EAAE,KAAK,GAAG,KAAK,EACxB,eAAe,EAAE,WAAW,GAAG,KAAK,EACpC,SAAS,EAAE,KAAK,EAChB,YAAY,EAAE,QAAQ,EACtB,mBAAmB,EAAE,eAAe,EACpC,WAAW,EAAE,OAAO,EACpB,qBAAqB,EAAE,iBAAiB,EACxC,UAAU,EAAE,MAAM,EAClB,iBAAiB,EAAE,aAAa,EAChC,aAAa,EAAE,SAAS,EACxB,cAAc,EAAE,UAAU,EAC1B,OAAO,EAAE,GAAG,EACZ,gBAAgB,EAAE,YAAY,EAC9B,UAAU,EAAE,MAAM,EAClB,eAAe,EAAE,WAAW,EAC5B,cAAc,EAAE,UAAU,EAC1B,gBAAgB,EAAE,YAAY,EAC9B,YAAY,EAAE,QAAQ,EACtB,QAAQ,EAAE,IAAI,EACd,SAAS,EAAE,KAAK,GAAG,SAAS,EAC5B,YAAY,EAAE,QAAQ,GAAG,SAAS,EAClC,2BAA2B,EAAE,uBAAuB,EACpD,mCAAmC,EAAE,+BAA+B,EACpE,OAAO,EAAE,GAAG,EACZ,CAAC,EAAE,QAAQ,GACZ,GAAG,IAAI,CAAC;IACT,OAAO;QACL,8DAA8D;QAC9D,IAAI,EAAE,OAAO,CAAC,IAAI;QAClB,QAAQ;QAER,MAAM;QACN,IAAI;QACJ,UAAU;QACV,OAAO;QACP,OAAO;QACP,UAAU;QACV,WAAW;QACX,IAAI;QACJ,KAAK;QACL,WAAW;QACX,KAAK;QACL,QAAQ;QACR,eAAe;QACf,OAAO;QACP,iBAAiB;QACjB,MAAM;QACN,aAAa;QACb,SAAS;QACT,UAAU;QACV,GAAG;QACH,YAAY;QACZ,MAAM;QACN,WAAW;QACX,UAAU;QACV,YAAY;QACZ,QAAQ;QACR,IAAI;QACJ,KAAK;QACL,QAAQ;QACR,uBAAuB;QACvB,+BAA+B;QAC/B,GAAG;KACJ,CAAC;AACJ,CAAC;AAED,SAAS,MAAM,CAAC,OAAuB;IACrC,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,GAAG,OAAO,CAAC,eAAe,CAAC;IAE/D,IAAI,IAAI,EAAE;QACR,OAAO,CAAC,GAAG,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAwCf,CAAC,CAAC;QAEC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;KACjB;IAED,8BAA8B;IAC9B,IAAI,OAAO,KAAK,CAAC,EAAE;QACjB,OAAO,CAAC,GAAG,CAAC,IAAI,eAAO,EAAE,CAAC,CAAC;QAC3B,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;KACjB;IAED,MAAM,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,IAAA,cAAO,EAAC,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC;IAErD,yFAAyF;IACzF,mCAAmC;IACnC,IAAI,GAAG;QAAE,OAAO,CAAC,qBAAqB,GAAG,IAAI,CAAC;IAE9C,OAAO;QACL,GAAG;KACJ,CAAC;AACJ,CAAC;AAED,SAAS,MAAM,CAAC,OAAuB;IACrC,MAAM,EACJ,IAAI,EACJ,KAAK,EACL,MAAM,EACN,aAAa,EACb,UAAU,EACV,uBAAuB,EACvB,SAAS,EACT,GAAG,EACH,YAAY,EACZ,MAAM,EACN,YAAY,EACZ,QAAQ,EACR,UAAU,EACV,OAAO,EACP,OAAO,EACP,WAAW,EACX,UAAU,EACV,QAAQ,EACR,iBAAiB,EACjB,eAAe,EACf,WAAW,EACX,KAAK,EACL,QAAQ,EACR,GAAG,EACH,+BAA+B,GAChC,GAAG,OAAO,CAAC,eAAe,CAAC;IAC5B,MAAM,EAAE,GAAG,EAAE,GAAG,OAAO,CAAC,YAAa,CAAC;IAEtC,yFAAyF;IACzF,wFAAwF;IACxF,0FAA0F;IAC1F,kFAAkF;IAClF,oFAAoF;IACpF,0DAA0D;IAC1D,MAAM,EAAE,cAAc,EAAE,GAAG,iBAAiB,CAAC,OAAO,CAAC,CAAC;IAEtD,MAAM,eAAe,GAAG,IAAA,iCAAiB,EAAC;QACxC,GAAG;QACH,IAAI;QACJ,KAAK;QACL,MAAM;QACN,aAAa,EAAE,CAAA,aAAa,aAAb,aAAa,cAAb,aAAa,GAAI,UAAU,IAAI,IAAI,EAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS;QACrE,qBAAqB,EAAE,uBAAuB,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS;QAClE,SAAS;QACT,UAAU;QACV,GAAG;QACH,YAAY;QACZ,MAAM;QACN,QAAQ;QACR,gBAAgB,EAAE,mBAAmB,CACnC,GAAG,EACH,UAAU,EACV,OAAO,EACP,cAAc,CACf;QACD,OAAO;QACP,WAAW;QACX,UAAU;QACV,QAAQ;QACR,iBAAiB;QACjB,eAAe;QACf,OAAO,EAAE,WAAW;QACpB,KAAK;QACL,QAAQ;QACR,YAAY;QACZ,GAAG;QACH,+BAA+B,EAC7B,+BAAkE;KACrE,CAAC,CAAC;IAEH,iFAAiF;IACjF,2CAA2C;IAC3C,IAAI,eAAe,CAAC,OAAO,CAAC,GAAG;QAAE,OAAO,CAAC,qBAAqB,GAAG,IAAI,CAAC;IAEtE,OAAO,EAAE,eAAe,EAAE,CAAC;AAC7B,CAAC;AAED;;;;;;;;;;;;;;GAcG;AACH,SAAS,iBAAiB,CAAC,KAAqB;IAC9C,MAAM,EAAE,IAAI,EAAE,WAAW,EAAE,QAAQ,EAAE,GAAG,KAAK,CAAC,eAAgB,CAAC;IAC/D,MAAM,EAAE,GAAG,EAAE,GAAG,KAAK,CAAC,YAAa,CAAC;IACpC,MAAM,EAAE,KAAK,EAAE,GAAG,KAAK,CAAC;IAExB,kFAAkF;IAClF,6DAA6D;IAC7D,2CAA2C;IAC3C,MAAM,WAAW,GAAG,IAAI,IAAI,IAAI,IAAI,CAAC,CAAC,WAAW,IAAI,QAAQ,CAAC,MAAM,CAAC,CAAC;IACtE,MAAM,iBAAiB,GAAG,CAAC,WAAW,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC;IAC9D,MAAM,WAAW,GACf,CAAC,iBAAiB;QAClB,CAAC,WAAW,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;IACzD,MAAM,YAAY,GAAG,CAAC,WAAW,IAAI,CAAC,WAAW,IAAI,CAAC,iBAAiB,CAAC;IAExE;;;OAGG;IACH,MAAM,cAAc,GAAG,iBAAiB;QACtC,CAAC,CAAC,KAAK;YACL,CAAC,CAAC,IAAA,cAAO,EAAC,GAAG,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC;YAC3B,CAAC,CAAC,IAAA,cAAO,EAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QACxB,CAAC,CAAC,SAAS,CAAC;IAEd,OAAO;QACL,WAAW;QACX,iBAAiB;QACjB,WAAW;QACX,YAAY;QACZ,cAAc;KACf,CAAC;AACJ,CAAC;AAED,SAAS,MAAM,CAAC,OAAuB;;IACrC,MAAM,EAAE,gBAAgB,EAAE,YAAY,EAAE,GAAG,OAAO,CAAC;IACnD,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,GACxD,OAAO,CAAC,eAAe,CAAC;IAC1B,MAAM,EAAE,GAAG,EAAE,GAAG,OAAO,CAAC,YAAa,CAAC;IACtC,MAAM,EAAE,eAAe,EAAE,GAAG,OAAO,CAAC,YAAa,CAAC;IAElD,MAAM,EACJ,cAAc,EACd,iBAAiB,EACjB,WAAW,EACX,WAAW,EACX,YAAY,GACb,GAAG,iBAAiB,CAAC,OAAO,CAAC,CAAC;IAW/B,IAAI,SAAuC,CAAC;IAC5C,IAAI,SAAuC,CAAC;IAC5C,IAAI,UAAwC,CAAC;IAC7C,IAAI,oBAAoB,GAAqC,SAAS,CAAC;IACvE,IAAI,WAAW,EAAE;QACf,MAAM,KAAK,GAAG,IAAI,gBAAS,CAAC,IAAA,WAAI,EAAC,GAAG,EAAE,oBAAa,CAAC,CAAC,CAAC;QACtD,SAAS,GAAG;YACV,KAAK;YACL,IAAI,EAAE,IAAA,iBAAU,EAAC;gBACf,KAAK;gBACL,+BAA+B,EAAE,oBAAoB;gBACrD,iDAAiD,EAAE,KAAK;aACzD,CAAC;SACH,CAAC;QACF,CAAC,EAAE,oBAAoB,EAAE,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC;QAC5C,iDAAiD;QACjD,MAAM,MAAM,GAAG,CAAC,SAAS,CAAC,MAAM,GAAG,IAAI,MAAM,CAAC,gBAAS,CAAC,CAAC,CAAC;QAC1D,MAAM,CAAC,QAAQ,GAAG,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC;QACvC,MAAM,CAAC,KAAK,GAAI,MAAc,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;KACtD;IACD,IAAI,YAAY,EAAE;QAChB,MAAM,KAAK,GAAG,IAAI,gBAAS,CAAC,IAAA,WAAI,EAAC,GAAG,EAAE,qBAAc,CAAC,CAAC,CAAC;QACvD,UAAU,GAAG;YACX,KAAK;YACL,IAAI,EAAE,IAAA,iBAAU,EAAC;gBACf,KAAK;gBACL,+BAA+B,EAAE,oBAAoB;gBACrD,iDAAiD,EAAE,KAAK;aACzD,CAAC;SACH,CAAC;QACF,CAAC,EAAE,oBAAoB,EAAE,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC;QAC7C,iDAAiD;QACjD,MAAM,MAAM,GAAG,CAAC,UAAU,CAAC,MAAM,GAAG,IAAI,MAAM,CAAC,iBAAU,CAAC,CAAC,CAAC;QAC5D,MAAM,CAAC,QAAQ,GAAG,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC;QACxC,MAAM,CAAC,KAAK,GAAI,MAAc,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;KACtD;IACD,IAAI,WAAW,EAAE;QACf,MAAM,KAAK,GAAG,IAAI,gBAAS,CAAC,IAAA,WAAI,EAAC,GAAG,EAAE,oBAAa,CAAC,CAAC,CAAC;QACtD,SAAS,GAAG;YACV,KAAK;YACL,IAAI,EAAE,IAAA,iBAAU,EAAC;gBACf,KAAK;gBACL,+BAA+B,EAAE,oBAAoB;aACtD,CAAC;SACH,CAAC;QACF,CAAC,EAAE,oBAAoB,EAAE,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC;KAC7C;IAED,6CAA6C;IAC7C,MAAM,OAAO,GAAG,IAAA,iCAAyB,EAAC;QACxC,kGAAkG;QAClG,2BAA2B;QAC3B,GAAG,eAAe;QAClB,OAAO,EAAE;YACP,GAAG,eAAe,CAAC,OAAO;YAC1B,QAAQ,EAAE,MAAA,oBAAoB,aAApB,oBAAoB,uBAApB,oBAAoB,CAAE,QAAQ,mCAAI,SAAS;YACrD,UAAU,EAAE,MAAA,oBAAoB,aAApB,oBAAoB,uBAApB,oBAAoB,CAAE,UAAU,mCAAI,SAAS;YACzD,OAAO,EAAE,gBAAQ,CAAC,OAAO;SAC1B;KACF,CAAC,CAAC;IACH,IAAA,gBAAQ,EAAC,OAAO,CAAC,CAAC;IAClB,IAAI,gBAAgB;QAEhB,OAAO,CAAC,sBAAsB,CAC/B,CAAC,aAAa,CAAC,IAAA,sBAAc,EAAC,OAAO,CAAC,CAAC,CAAC;IAE3C,0EAA0E;IAC1E,SAAS,aAAT,SAAS,uBAAT,SAAS,CAAE,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;IACpC,SAAS,aAAT,SAAS,uBAAT,SAAS,CAAE,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;IACpC,UAAU,aAAV,UAAU,uBAAV,UAAU,CAAE,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;IAErC,8BAA8B;IAC9B,IAAI,OAAO,KAAK,CAAC,EAAE;QACjB,OAAO,CAAC,GAAG,CAAC,YAAY,eAAO,EAAE,CAAC,CAAC;QACnC,OAAO,CAAC,GAAG,CAAC,QAAQ,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC;QACvC,OAAO,CAAC,GAAG,CAAC,aAAa,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,CAAC;QAC/C,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;KACjB;IACD,IAAI,OAAO,IAAI,CAAC,EAAE;QAChB,OAAO,CAAC,GAAG,CAAC,YAAY,eAAO,IAAI,IAAA,cAAO,EAAC,SAAS,CAAC,EAAE,CAAC,CAAC;QACzD,OAAO,CAAC,GAAG,CAAC,QAAQ,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC;QACvC,OAAO,CAAC,GAAG,CACT,aAAa,OAAO,CAAC,EAAE,CAAC,OAAO,IAAI,MAAA,OAAO,CAAC,YAAY,mCAAI,EAAE,EAAE,CAChE,CAAC;QACF,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;KACjB;IAED,IAAI,UAAU,EAAE;QACd,MAAM,EAAE,GAAG,OAAO,CAAC,EAAuB,CAAC;QAC3C,IAAI,OAAO,EAAE,CAAC,iBAAiB,KAAK,UAAU,EAAE;YAC9C,OAAO,CAAC,KAAK,CACX,oFAAoF,CACrF,CAAC;YACF,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;SACjB;QACD,IAAI,WAAW,GAAG,SAAS,CAAC;QAC5B,IAAI,OAAO,CAAC,OAAO,CAAC,WAAW,EAAE;YAC/B,6GAA6G;YAC7G,MAAM,cAAc,GAAG,IAAA,cAAO,EAAC,OAAO,CAAC,cAAe,CAAC,CAAC;YACxD,WAAW,GAAG,EAA4B,CAAC;YAC3C,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE;gBACtE,WAAW,CACT,IAAA,eAAQ,EACN,cAAc,EACd,IAAA,cAAO,EAAC,MAAA,OAAO,CAAC,OAAO,CAAC,eAAe,0CAAE,WAAY,EAAE,GAAG,CAAC,CAC5D,CACF,GAAG,KAAK,CAAC;aACX;SACF;QACD,MAAM,IAAI,GAAG;YACX,CAAC,SAAS,CAAC,EAAE;gBACX,GAAG,OAAO,CAAC,OAAO;gBAClB,OAAO,EAAE,CAAA,MAAA,OAAO,CAAC,OAAO,CAAC,OAAO,0CAAE,MAAM;oBACtC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO;oBACzB,CAAC,CAAC,SAAS;gBACb,WAAW;gBACX,eAAe,EAAE,SAAS;gBAC1B,eAAe,EAAE,SAAS;gBAC1B,OAAO,EAAE,MAAA,OAAO,CAAC,cAAc,mCAAI,OAAO,CAAC,OAAO,CAAC,OAAO;aAC3D;YACD,GAAG,EAAE,CAAC,iBAAiB,CACrB,OAAO,CAAC,MAAM,EACd,MAAA,OAAO,CAAC,cAAc,mCAAI,IAAA,WAAI,EAAC,GAAG,EAAE,gCAAgC,CAAC,EACrE,OAAO,CAAC,EAAE,CAAC,GAAG,CACf;SACF,CAAC;QACF,OAAO,CAAC,GAAG;QACT,0GAA0G;QAC1G,8GAA8G;QAC9G,qBAAqB;QACrB,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,CAC9B,CAAC;QACF,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;KACjB;IAED,0DAA0D;IAC1D,OAAO,CAAC,QAAQ,CAAC,IAAI,CACnB,YAAY,EACZ,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,CAChD,CAAC;IAEF,sCAAsC;IACtC,OAAO,CAAC,IAAI,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;SAC7B,MAAM,CAAC,iBAAiB,CAAC,CAAC,CAAE,CAAC,cAAc,CAAc,CAAC,CAAC,CAAC,EAAE,CAAC;SAC/D,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAErD,4DAA4D;IAC5D,IAAI,iBAAiB,EAAE;QACrB,IACE,OAAO,CAAC,gBAAgB;YACxB,IAAA,mBAAY,EAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC,EAC7C;YACA,kCAAkC;YAClC,OAAO,CAAC,6BAA6B,CAAC,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;SAC5D;aAAM;YACL,MAAM,CAAC,OAAO,EAAE,CAAC;SAClB;KACF;SAAM;QACL,0DAA0D;QAC1D,yCAAyC;QACzC,IAAI,WAAW,EAAE;YACf,IAAA,0DAAsB,EAAC,MAAM,CAAC,CAAC;YAC/B,oBAAoB,CAClB,SAAU,CAAC,IAAI,EACf,SAAU,CAAC,MAAO,EAClB,IAAK,EACL,KAAK,EACL,MAAM,CACP,CAAC;SACH;QAED,IAAI,WAAW,EAAE;YACf,SAAU,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;SACzB;QAED,IAAI,YAAY,EAAE;YAChB,IAAI,MAAM,GAAG,IAAI,IAAI,EAAE,CAAC;YACxB,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,KAAa,EAAE,EAAE,CAAC,CAAC,MAAM,IAAI,KAAK,CAAC,CAAC,CAAC;YAC/D,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE;gBAC3B,oBAAoB,CAClB,UAAW,CAAC,IAAI,EAChB,UAAW,CAAC,MAAO,EACnB,MAAM;gBACN,wCAAwC;gBACxC,KAAK,EACL,OAAO,CACR,CAAC;YACJ,CAAC,CAAC,CAAC;SACJ;KACF;AACH,CAAC;AAED;;GAEG;AACH,SAAS,mBAAmB,CAC1B,GAAY,EACZ,UAAoB,EACpB,OAAiB,EACjB,UAAmB;IAEnB,sEAAsE;IACtE,IAAI,UAAU,IAAI,OAAO,EAAE;QACzB,MAAM,IAAI,SAAS,CAAC,kDAAkD,CAAC,CAAC;KACzE;IACD,IAAI,UAAU,IAAI,CAAC,UAAU,EAAE;QAC7B,MAAM,IAAI,SAAS,CACjB,yFAAyF,CAC1F,CAAC;KACH;IACD,MAAM,YAAY,GAChB,UAAU,KAAK,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,KAAK,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC;IACvE,IAAI,YAAY,EAAE;QAChB,mEAAmE;QACnE,2FAA2F;QAC3F,wGAAwG;QACxG,sFAAsF;QACtF,6DAA6D;QAC7D,6EAA6E;QAC7E,yEAAyE;QACzE,MAAM,IAAI,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;QAC5C,MAAM,wBAAwB,GAAa,EAAE,CAAC;QAC9C,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE;YACtB,IAAI,CAAC,IAAA,qBAAc,EAAC,OAAO,CAAC,UAAU,EAAE,GAAG,CAAC,EAAE;gBAC5C,wBAAwB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBACnC,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,cAAa,CAAC,CAAC;aAC1C;SACF;QACD,IAAI;YACF,OAAO,IAAA,cAAO,EAAC,uBAAuB,CAAC,UAAW,CAAC,CAAC,CAAC;SACtD;gBAAS;YACR,KAAK,MAAM,GAAG,IAAI,wBAAwB,EAAE;gBAC1C,OAAO,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;aAChC;SACF;KACF;IAED,OAAO,GAAG,CAAC;AACb,CAAC;AAED,MAAM,oCAAoC,GAAG,IAAA,cAAO,EAAC,SAAS,EAAE,cAAc,CAAC,CAAC;AAChF,IAAI,oCAAoC,GAAG,CAAC,CAAC;AAE7C;;;;;;GAMG;AACH,SAAS,uBAAuB,CAAC,uBAA+B;IAC9D,gFAAgF;IAChF,iFAAiF;IACjF,mFAAmF;IACnF,iCAAiC;IACjC,MAAM,sBAAsB,GAAG,IAAA,mBAAY,EAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;IAC7E,IAAI,CAAC,sBAAsB;QAAE,OAAO,OAAO,CAAC,OAAO,CAAC,uBAAuB,CAAC,CAAC;IAE7E,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,IAAA,YAAS,EAAC,uBAAuB,CAAC,CAAC;IACzD,MAAM,uBAAuB,GAAG,KAAK,IAAI,EAAE,CAAC;IAE5C,MAAM,GAAG,GAAG,IAAA,oBAAa,EACvB,IAAA,WAAI,EAAC,GAAG,EAAE,0CAA0C,CAAC,CACtD,CAAC;IACF,OAAO,GAAG,CAAC,OAAO,CAAC,uBAAuB,EAAE;QAC1C,KAAK,EAAE;YACL,GAAG,oCAAoC,GAAG,oCAAoC,EAAE,EAAE;YAClF,GAAG,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,uBAAuB,CAAC,IAAI,EAAE,CAAC;SACtD;KACF,CAAC,CAAC;AACL,CAAC;AAED;;GAEG;AACH,SAAS,oBAAoB,CAC3B,WAAwB,EACxB,MAAc,EACd,IAAY,EACZ,SAAkB,EAClB,kBAAoC;IAEpC,IAAI,MAAW,CAAC;IAChB,IAAA,mBAAY,EAAC,MAAM,EAAE,MAAM,EAAE,kBAAkB,CAAC,CAAC;IAEjD,IAAI;QACF,MAAM,GAAG,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;KACrC;IAAC,OAAO,KAAK,EAAE;QACd,IAAI,KAAK,YAAY,eAAO,EAAE;YAC5B,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YACrB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;SACjB;QAED,MAAM,KAAK,CAAC;KACb;IAED,IAAI,SAAS,EAAE;QACb,OAAO,CAAC,GAAG,CACT,OAAO,MAAM,KAAK,QAAQ;YACxB,CAAC,CAAC,MAAM;YACR,CAAC,CAAC,IAAA,cAAO,EAAC,MAAM,EAAE,EAAE,MAAM,EAAE,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CACtD,CAAC;KACH;AACH,CAAC;AAED,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,EAAE;IAC3B,IAAI,EAAE,CAAC;CACR", "sourcesContent": ["#!/usr/bin/env node\n\nimport { join, resolve, dirname, parse as parsePath, relative } from 'path';\nimport { inspect } from 'util';\nimport Module = require('module');\nlet arg: typeof import('arg');\nimport { parse, createRequire, hasOwnProperty, versionGteLt } from './util';\nimport {\n  EVAL_FILENAME,\n  EvalState,\n  createRepl,\n  ReplService,\n  setupContext,\n  STDIN_FILENAME,\n  EvalAwarePartialHost,\n  EVAL_NAME,\n  STDIN_NAME,\n  REPL_FILENAME,\n} from './repl';\nimport {\n  VERSION,\n  TSError,\n  register,\n  createEsmHooks,\n  createFromPreloadedConfig,\n  DEFAULTS,\n  ExperimentalSpecifierResolution,\n} from './index';\nimport type { TSInternal } from './ts-compiler-types';\nimport { addBuiltinLibsToObject } from '../dist-raw/node-internal-modules-cjs-helpers';\nimport { callInChild } from './child/spawn-child';\nimport { findAndReadConfig } from './configuration';\n\n/**\n * Main `bin` functionality.\n *\n * This file is split into a chain of functions (phases), each one adding to a shared state object.\n * This is done so that the next function can either be invoked in-process or, if necessary, invoked in a child process.\n *\n * The functions are intentionally given uncreative names and left in the same order as the original code, to make a\n * smaller git diff.\n */\nexport function main(\n  argv: string[] = process.argv.slice(2),\n  entrypointArgs: Record<string, any> = {}\n) {\n  const args = parseArgv(argv, entrypointArgs);\n  const state: BootstrapState = {\n    shouldUseChildProcess: false,\n    isInChildProcess: false,\n    isCli: true,\n    tsNodeScript: __filename,\n    parseArgvResult: args,\n  };\n  return bootstrap(state);\n}\n\n/**\n * @internal\n * Describes state of CLI bootstrapping.\n * Can be marshalled when necessary to resume bootstrapping in a child process.\n */\nexport interface BootstrapState {\n  isInChildProcess: boolean;\n  shouldUseChildProcess: boolean;\n  /**\n   * True if bootstrapping the ts-node CLI process or the direct child necessitated by `--esm`.\n   * false if bootstrapping a subsequently `fork()`ed child.\n   */\n  isCli: boolean;\n  tsNodeScript: string;\n  parseArgvResult: ReturnType<typeof parseArgv>;\n  phase2Result?: ReturnType<typeof phase2>;\n  phase3Result?: ReturnType<typeof phase3>;\n}\n\n/** @internal */\nexport function bootstrap(state: BootstrapState) {\n  if (!state.phase2Result) {\n    state.phase2Result = phase2(state);\n    if (state.shouldUseChildProcess && !state.isInChildProcess) {\n      // Note: When transitioning into the child-process after `phase2`,\n      // the updated working directory needs to be preserved.\n      return callInChild(state);\n    }\n  }\n  if (!state.phase3Result) {\n    state.phase3Result = phase3(state);\n    if (state.shouldUseChildProcess && !state.isInChildProcess) {\n      // Note: When transitioning into the child-process after `phase2`,\n      // the updated working directory needs to be preserved.\n      return callInChild(state);\n    }\n  }\n  return phase4(state);\n}\n\nfunction parseArgv(argv: string[], entrypointArgs: Record<string, any>) {\n  arg ??= require('arg');\n  // HACK: technically, this function is not marked @internal so it's possible\n  // that libraries in the wild are doing `require('ts-node/dist/bin').main({'--transpile-only': true})`\n  // We can mark this function @internal in next major release.\n  // For now, rewrite args to avoid a breaking change.\n  entrypointArgs = { ...entrypointArgs };\n  for (const key of Object.keys(entrypointArgs)) {\n    entrypointArgs[\n      key.replace(\n        /([a-z])-([a-z])/g,\n        (_$0, $1, $2: string) => `${$1}${$2.toUpperCase()}`\n      )\n    ] = entrypointArgs[key];\n  }\n\n  const args = {\n    ...entrypointArgs,\n    ...arg(\n      {\n        // Node.js-like options.\n        '--eval': String,\n        '--interactive': Boolean,\n        '--print': Boolean,\n        '--require': [String],\n\n        // CLI options.\n        '--help': Boolean,\n        '--cwdMode': Boolean,\n        '--scriptMode': Boolean,\n        '--version': arg.COUNT,\n        '--showConfig': Boolean,\n        '--esm': Boolean,\n\n        // Project options.\n        '--cwd': String,\n        '--files': Boolean,\n        '--compiler': String,\n        '--compilerOptions': parse,\n        '--project': String,\n        '--ignoreDiagnostics': [String],\n        '--ignore': [String],\n        '--transpileOnly': Boolean,\n        '--transpiler': String,\n        '--swc': Boolean,\n        '--typeCheck': Boolean,\n        '--compilerHost': Boolean,\n        '--pretty': Boolean,\n        '--skipProject': Boolean,\n        '--skipIgnore': Boolean,\n        '--preferTsExts': Boolean,\n        '--logError': Boolean,\n        '--emit': Boolean,\n        '--scope': Boolean,\n        '--scopeDir': String,\n        '--noExperimentalReplAwait': Boolean,\n        '--experimentalSpecifierResolution': String,\n\n        // Aliases.\n        '-e': '--eval',\n        '-i': '--interactive',\n        '-p': '--print',\n        '-r': '--require',\n        '-h': '--help',\n        '-s': '--script-mode',\n        '-v': '--version',\n        '-T': '--transpileOnly',\n        '-H': '--compilerHost',\n        '-I': '--ignore',\n        '-P': '--project',\n        '-C': '--compiler',\n        '-D': '--ignoreDiagnostics',\n        '-O': '--compilerOptions',\n        '--dir': '--cwd',\n\n        // Support both tsc-style camelCase and node-style hypen-case for *all* flags\n        '--cwd-mode': '--cwdMode',\n        '--script-mode': '--scriptMode',\n        '--show-config': '--showConfig',\n        '--compiler-options': '--compilerOptions',\n        '--ignore-diagnostics': '--ignoreDiagnostics',\n        '--transpile-only': '--transpileOnly',\n        '--type-check': '--typeCheck',\n        '--compiler-host': '--compilerHost',\n        '--skip-project': '--skipProject',\n        '--skip-ignore': '--skipIgnore',\n        '--prefer-ts-exts': '--preferTsExts',\n        '--log-error': '--logError',\n        '--scope-dir': '--scopeDir',\n        '--no-experimental-repl-await': '--noExperimentalReplAwait',\n        '--experimental-specifier-resolution':\n          '--experimentalSpecifierResolution',\n      },\n      {\n        argv,\n        stopAtPositional: true,\n      }\n    ),\n  };\n\n  // Only setting defaults for CLI-specific flags\n  // Anything passed to `register()` can be `undefined`; `create()` will apply\n  // defaults.\n  const {\n    '--cwd': cwdArg,\n    '--help': help = false,\n    '--scriptMode': scriptMode,\n    '--cwdMode': cwdMode,\n    '--version': version = 0,\n    '--showConfig': showConfig,\n    '--require': argsRequire = [],\n    '--eval': code = undefined,\n    '--print': print = false,\n    '--interactive': interactive = false,\n    '--files': files,\n    '--compiler': compiler,\n    '--compilerOptions': compilerOptions,\n    '--project': project,\n    '--ignoreDiagnostics': ignoreDiagnostics,\n    '--ignore': ignore,\n    '--transpileOnly': transpileOnly,\n    '--typeCheck': typeCheck,\n    '--transpiler': transpiler,\n    '--swc': swc,\n    '--compilerHost': compilerHost,\n    '--pretty': pretty,\n    '--skipProject': skipProject,\n    '--skipIgnore': skipIgnore,\n    '--preferTsExts': preferTsExts,\n    '--logError': logError,\n    '--emit': emit,\n    '--scope': scope = undefined,\n    '--scopeDir': scopeDir = undefined,\n    '--noExperimentalReplAwait': noExperimentalReplAwait,\n    '--experimentalSpecifierResolution': experimentalSpecifierResolution,\n    '--esm': esm,\n    _: restArgs,\n  } = args;\n  return {\n    // Note: argv and restArgs may be overwritten by child process\n    argv: process.argv,\n    restArgs,\n\n    cwdArg,\n    help,\n    scriptMode,\n    cwdMode,\n    version,\n    showConfig,\n    argsRequire,\n    code,\n    print,\n    interactive,\n    files,\n    compiler,\n    compilerOptions,\n    project,\n    ignoreDiagnostics,\n    ignore,\n    transpileOnly,\n    typeCheck,\n    transpiler,\n    swc,\n    compilerHost,\n    pretty,\n    skipProject,\n    skipIgnore,\n    preferTsExts,\n    logError,\n    emit,\n    scope,\n    scopeDir,\n    noExperimentalReplAwait,\n    experimentalSpecifierResolution,\n    esm,\n  };\n}\n\nfunction phase2(payload: BootstrapState) {\n  const { help, version, cwdArg, esm } = payload.parseArgvResult;\n\n  if (help) {\n    console.log(`\nUsage: ts-node [options] [ -e script | script.ts ] [arguments]\n\nOptions:\n\n  -e, --eval [code]               Evaluate code\n  -p, --print                     Print result of \\`--eval\\`\n  -r, --require [path]            Require a node module before execution\n  -i, --interactive               Opens the REPL even if stdin does not appear to be a terminal\n\n  --esm                           Bootstrap with the ESM loader, enabling full ESM support\n  --swc                           Use the faster swc transpiler\n\n  -h, --help                      Print CLI usage\n  -v, --version                   Print module version information.  -vvv to print additional information\n  --showConfig                    Print resolved configuration and exit\n\n  -T, --transpileOnly             Use TypeScript's faster \\`transpileModule\\` or a third-party transpiler\n  -H, --compilerHost              Use TypeScript's compiler host API\n  -I, --ignore [pattern]          Override the path patterns to skip compilation\n  -P, --project [path]            Path to TypeScript JSON project file\n  -C, --compiler [name]           Specify a custom TypeScript compiler\n  --transpiler [name]             Specify a third-party, non-typechecking transpiler\n  -D, --ignoreDiagnostics [code]  Ignore TypeScript warnings by diagnostic code\n  -O, --compilerOptions [opts]    JSON object to merge with compiler options\n\n  --cwd                           Behave as if invoked within this working directory.\n  --files                         Load \\`files\\`, \\`include\\` and \\`exclude\\` from \\`tsconfig.json\\` on startup\n  --pretty                        Use pretty diagnostic formatter (usually enabled by default)\n  --cwdMode                       Use current directory instead of <script.ts> for config resolution\n  --skipProject                   Skip reading \\`tsconfig.json\\`\n  --skipIgnore                    Skip \\`--ignore\\` checks\n  --emit                          Emit output files into \\`.ts-node\\` directory\n  --scope                         Scope compiler to files within \\`scopeDir\\`.  Anything outside this directory is ignored.\n  --scopeDir                      Directory for \\`--scope\\`\n  --preferTsExts                  Prefer importing TypeScript files over JavaScript files\n  --logError                      Logs TypeScript errors to stderr instead of throwing exceptions\n  --noExperimentalReplAwait       Disable top-level await in REPL.  Equivalent to node's --no-experimental-repl-await\n  --experimentalSpecifierResolution [node|explicit]\n                                  Equivalent to node's --experimental-specifier-resolution\n`);\n\n    process.exit(0);\n  }\n\n  // Output project information.\n  if (version === 1) {\n    console.log(`v${VERSION}`);\n    process.exit(0);\n  }\n\n  const cwd = cwdArg ? resolve(cwdArg) : process.cwd();\n\n  // If ESM is explicitly enabled through the flag, stage3 should be run in a child process\n  // with the ESM loaders configured.\n  if (esm) payload.shouldUseChildProcess = true;\n\n  return {\n    cwd,\n  };\n}\n\nfunction phase3(payload: BootstrapState) {\n  const {\n    emit,\n    files,\n    pretty,\n    transpileOnly,\n    transpiler,\n    noExperimentalReplAwait,\n    typeCheck,\n    swc,\n    compilerHost,\n    ignore,\n    preferTsExts,\n    logError,\n    scriptMode,\n    cwdMode,\n    project,\n    skipProject,\n    skipIgnore,\n    compiler,\n    ignoreDiagnostics,\n    compilerOptions,\n    argsRequire,\n    scope,\n    scopeDir,\n    esm,\n    experimentalSpecifierResolution,\n  } = payload.parseArgvResult;\n  const { cwd } = payload.phase2Result!;\n\n  // NOTE: When we transition to a child process for ESM, the entry-point script determined\n  // here might not be the one used later in `phase4`. This can happen when we execute the\n  // original entry-point but then the process forks itself using e.g. `child_process.fork`.\n  // We will always use the original TS project in forked processes anyway, so it is\n  // expected and acceptable to retrieve the entry-point information here in `phase2`.\n  // See: https://github.com/TypeStrong/ts-node/issues/1812.\n  const { entryPointPath } = getEntryPointInfo(payload);\n\n  const preloadedConfig = findAndReadConfig({\n    cwd,\n    emit,\n    files,\n    pretty,\n    transpileOnly: transpileOnly ?? transpiler != null ? true : undefined,\n    experimentalReplAwait: noExperimentalReplAwait ? false : undefined,\n    typeCheck,\n    transpiler,\n    swc,\n    compilerHost,\n    ignore,\n    logError,\n    projectSearchDir: getProjectSearchDir(\n      cwd,\n      scriptMode,\n      cwdMode,\n      entryPointPath\n    ),\n    project,\n    skipProject,\n    skipIgnore,\n    compiler,\n    ignoreDiagnostics,\n    compilerOptions,\n    require: argsRequire,\n    scope,\n    scopeDir,\n    preferTsExts,\n    esm,\n    experimentalSpecifierResolution:\n      experimentalSpecifierResolution as ExperimentalSpecifierResolution,\n  });\n\n  // If ESM is enabled through the parsed tsconfig, stage4 should be run in a child\n  // process with the ESM loaders configured.\n  if (preloadedConfig.options.esm) payload.shouldUseChildProcess = true;\n\n  return { preloadedConfig };\n}\n\n/**\n * Determines the entry-point information from the argv and phase2 result. This\n * method will be invoked in two places:\n *\n *   1. In phase 3 to be able to find a project from the potential entry-point script.\n *   2. In phase 4 to determine the actual entry-point script.\n *\n * Note that we need to explicitly re-resolve the entry-point information in the final\n * stage because the previous stage information could be modified when the bootstrap\n * invocation transitioned into a child process for ESM.\n *\n * Stages before (phase 4) can and will be cached by the child process through the Brotli\n * configuration and entry-point information is only reliable in the final phase. More\n * details can be found in here: https://github.com/TypeStrong/ts-node/issues/1812.\n */\nfunction getEntryPointInfo(state: BootstrapState) {\n  const { code, interactive, restArgs } = state.parseArgvResult!;\n  const { cwd } = state.phase2Result!;\n  const { isCli } = state;\n\n  // Figure out which we are executing: piped stdin, --eval, REPL, and/or entrypoint\n  // This is complicated because node's behavior is complicated\n  // `node -e code -i ./script.js` ignores -e\n  const executeEval = code != null && !(interactive && restArgs.length);\n  const executeEntrypoint = !executeEval && restArgs.length > 0;\n  const executeRepl =\n    !executeEntrypoint &&\n    (interactive || (process.stdin.isTTY && !executeEval));\n  const executeStdin = !executeEval && !executeRepl && !executeEntrypoint;\n\n  /**\n   * Unresolved. May point to a symlink, not realpath. May be missing file extension\n   * NOTE: resolution relative to cwd option (not `process.cwd()`) is legacy backwards-compat; should be changed in next major: https://github.com/TypeStrong/ts-node/issues/1834\n   */\n  const entryPointPath = executeEntrypoint\n    ? isCli\n      ? resolve(cwd, restArgs[0])\n      : resolve(restArgs[0])\n    : undefined;\n\n  return {\n    executeEval,\n    executeEntrypoint,\n    executeRepl,\n    executeStdin,\n    entryPointPath,\n  };\n}\n\nfunction phase4(payload: BootstrapState) {\n  const { isInChildProcess, tsNodeScript } = payload;\n  const { version, showConfig, restArgs, code, print, argv } =\n    payload.parseArgvResult;\n  const { cwd } = payload.phase2Result!;\n  const { preloadedConfig } = payload.phase3Result!;\n\n  const {\n    entryPointPath,\n    executeEntrypoint,\n    executeEval,\n    executeRepl,\n    executeStdin,\n  } = getEntryPointInfo(payload);\n\n  /**\n   * <repl>, [stdin], and [eval] are all essentially virtual files that do not exist on disc and are backed by a REPL\n   * service to handle eval-ing of code.\n   */\n  interface VirtualFileState {\n    state: EvalState;\n    repl: ReplService;\n    module?: Module;\n  }\n  let evalStuff: VirtualFileState | undefined;\n  let replStuff: VirtualFileState | undefined;\n  let stdinStuff: VirtualFileState | undefined;\n  let evalAwarePartialHost: EvalAwarePartialHost | undefined = undefined;\n  if (executeEval) {\n    const state = new EvalState(join(cwd, EVAL_FILENAME));\n    evalStuff = {\n      state,\n      repl: createRepl({\n        state,\n        composeWithEvalAwarePartialHost: evalAwarePartialHost,\n        ignoreDiagnosticsThatAreAnnoyingInInteractiveRepl: false,\n      }),\n    };\n    ({ evalAwarePartialHost } = evalStuff.repl);\n    // Create a local module instance based on `cwd`.\n    const module = (evalStuff.module = new Module(EVAL_NAME));\n    module.filename = evalStuff.state.path;\n    module.paths = (Module as any)._nodeModulePaths(cwd);\n  }\n  if (executeStdin) {\n    const state = new EvalState(join(cwd, STDIN_FILENAME));\n    stdinStuff = {\n      state,\n      repl: createRepl({\n        state,\n        composeWithEvalAwarePartialHost: evalAwarePartialHost,\n        ignoreDiagnosticsThatAreAnnoyingInInteractiveRepl: false,\n      }),\n    };\n    ({ evalAwarePartialHost } = stdinStuff.repl);\n    // Create a local module instance based on `cwd`.\n    const module = (stdinStuff.module = new Module(STDIN_NAME));\n    module.filename = stdinStuff.state.path;\n    module.paths = (Module as any)._nodeModulePaths(cwd);\n  }\n  if (executeRepl) {\n    const state = new EvalState(join(cwd, REPL_FILENAME));\n    replStuff = {\n      state,\n      repl: createRepl({\n        state,\n        composeWithEvalAwarePartialHost: evalAwarePartialHost,\n      }),\n    };\n    ({ evalAwarePartialHost } = replStuff.repl);\n  }\n\n  // Register the TypeScript compiler instance.\n  const service = createFromPreloadedConfig({\n    // Since this struct may have been marshalled across thread or process boundaries, we must restore\n    // un-marshall-able values.\n    ...preloadedConfig,\n    options: {\n      ...preloadedConfig.options,\n      readFile: evalAwarePartialHost?.readFile ?? undefined,\n      fileExists: evalAwarePartialHost?.fileExists ?? undefined,\n      tsTrace: DEFAULTS.tsTrace,\n    },\n  });\n  register(service);\n  if (isInChildProcess)\n    (\n      require('./child/child-loader') as typeof import('./child/child-loader')\n    ).lateBindHooks(createEsmHooks(service));\n\n  // Bind REPL service to ts-node compiler service (chicken-and-egg problem)\n  replStuff?.repl.setService(service);\n  evalStuff?.repl.setService(service);\n  stdinStuff?.repl.setService(service);\n\n  // Output project information.\n  if (version === 2) {\n    console.log(`ts-node v${VERSION}`);\n    console.log(`node ${process.version}`);\n    console.log(`compiler v${service.ts.version}`);\n    process.exit(0);\n  }\n  if (version >= 3) {\n    console.log(`ts-node v${VERSION} ${dirname(__dirname)}`);\n    console.log(`node ${process.version}`);\n    console.log(\n      `compiler v${service.ts.version} ${service.compilerPath ?? ''}`\n    );\n    process.exit(0);\n  }\n\n  if (showConfig) {\n    const ts = service.ts as any as TSInternal;\n    if (typeof ts.convertToTSConfig !== 'function') {\n      console.error(\n        'Error: --showConfig requires a typescript versions >=3.2 that support --showConfig'\n      );\n      process.exit(1);\n    }\n    let moduleTypes = undefined;\n    if (service.options.moduleTypes) {\n      // Assumption: this codepath requires CLI invocation, so moduleTypes must have come from a tsconfig, not API.\n      const showRelativeTo = dirname(service.configFilePath!);\n      moduleTypes = {} as Record<string, string>;\n      for (const [key, value] of Object.entries(service.options.moduleTypes)) {\n        moduleTypes[\n          relative(\n            showRelativeTo,\n            resolve(service.options.optionBasePaths?.moduleTypes!, key)\n          )\n        ] = value;\n      }\n    }\n    const json = {\n      ['ts-node']: {\n        ...service.options,\n        require: service.options.require?.length\n          ? service.options.require\n          : undefined,\n        moduleTypes,\n        optionBasePaths: undefined,\n        compilerOptions: undefined,\n        project: service.configFilePath ?? service.options.project,\n      },\n      ...ts.convertToTSConfig(\n        service.config,\n        service.configFilePath ?? join(cwd, 'ts-node-implicit-tsconfig.json'),\n        service.ts.sys\n      ),\n    };\n    console.log(\n      // Assumes that all configuration options which can possibly be specified via the CLI are JSON-compatible.\n      // If, in the future, we must log functions, for example readFile and fileExists, then we can implement a JSON\n      // replacer function.\n      JSON.stringify(json, null, 2)\n    );\n    process.exit(0);\n  }\n\n  // Prepend `ts-node` arguments to CLI for child processes.\n  process.execArgv.push(\n    tsNodeScript,\n    ...argv.slice(2, argv.length - restArgs.length)\n  );\n\n  // TODO this comes from BootstrapState\n  process.argv = [process.argv[1]]\n    .concat(executeEntrypoint ? ([entryPointPath] as string[]) : [])\n    .concat(restArgs.slice(executeEntrypoint ? 1 : 0));\n\n  // Execute the main contents (either eval, script or piped).\n  if (executeEntrypoint) {\n    if (\n      payload.isInChildProcess &&\n      versionGteLt(process.versions.node, '18.6.0')\n    ) {\n      // HACK workaround node regression\n      require('../dist-raw/runmain-hack.js').run(entryPointPath);\n    } else {\n      Module.runMain();\n    }\n  } else {\n    // Note: eval and repl may both run, but never with stdin.\n    // If stdin runs, eval and repl will not.\n    if (executeEval) {\n      addBuiltinLibsToObject(global);\n      evalAndExitOnTsError(\n        evalStuff!.repl,\n        evalStuff!.module!,\n        code!,\n        print,\n        'eval'\n      );\n    }\n\n    if (executeRepl) {\n      replStuff!.repl.start();\n    }\n\n    if (executeStdin) {\n      let buffer = code || '';\n      process.stdin.on('data', (chunk: Buffer) => (buffer += chunk));\n      process.stdin.on('end', () => {\n        evalAndExitOnTsError(\n          stdinStuff!.repl,\n          stdinStuff!.module!,\n          buffer,\n          // `echo 123 | node -p` still prints 123\n          print,\n          'stdin'\n        );\n      });\n    }\n  }\n}\n\n/**\n * Get project search path from args.\n */\nfunction getProjectSearchDir(\n  cwd?: string,\n  scriptMode?: boolean,\n  cwdMode?: boolean,\n  scriptPath?: string\n) {\n  // Validate `--script-mode` / `--cwd-mode` / `--cwd` usage is correct.\n  if (scriptMode && cwdMode) {\n    throw new TypeError('--cwd-mode cannot be combined with --script-mode');\n  }\n  if (scriptMode && !scriptPath) {\n    throw new TypeError(\n      '--script-mode must be used with a script name, e.g. `ts-node --script-mode <script.ts>`'\n    );\n  }\n  const doScriptMode =\n    scriptMode === true ? true : cwdMode === true ? false : !!scriptPath;\n  if (doScriptMode) {\n    // Use node's own resolution behavior to ensure we follow symlinks.\n    // scriptPath may omit file extension or point to a directory with or without package.json.\n    // This happens before we are registered, so we tell node's resolver to consider ts, tsx, and jsx files.\n    // In extremely rare cases, is is technically possible to resolve the wrong directory,\n    // because we do not yet know preferTsExts, jsx, nor allowJs.\n    // See also, justification why this will not happen in real-world situations:\n    // https://github.com/TypeStrong/ts-node/pull/1009#issuecomment-613017081\n    const exts = ['.js', '.jsx', '.ts', '.tsx'];\n    const extsTemporarilyInstalled: string[] = [];\n    for (const ext of exts) {\n      if (!hasOwnProperty(require.extensions, ext)) {\n        extsTemporarilyInstalled.push(ext);\n        require.extensions[ext] = function () {};\n      }\n    }\n    try {\n      return dirname(requireResolveNonCached(scriptPath!));\n    } finally {\n      for (const ext of extsTemporarilyInstalled) {\n        delete require.extensions[ext];\n      }\n    }\n  }\n\n  return cwd;\n}\n\nconst guaranteedNonexistentDirectoryPrefix = resolve(__dirname, 'doesnotexist');\nlet guaranteedNonexistentDirectorySuffix = 0;\n\n/**\n * require.resolve an absolute path, tricking node into *not* caching the results.\n * Necessary so that we do not pollute require.resolve cache prior to installing require.extensions\n *\n * Is a terrible hack, because node does not expose the necessary cache invalidation APIs\n * https://stackoverflow.com/questions/59865584/how-to-invalidate-cached-require-resolve-results\n */\nfunction requireResolveNonCached(absoluteModuleSpecifier: string) {\n  // node <= 12.1.x fallback: The trick below triggers a node bug on old versions.\n  // On these old versions, pollute the require cache instead. This is a deliberate\n  // ts-node limitation that will *rarely* manifest, and will not matter once node 12\n  // is end-of-life'd on 2022-04-30\n  const isSupportedNodeVersion = versionGteLt(process.versions.node, '12.2.0');\n  if (!isSupportedNodeVersion) return require.resolve(absoluteModuleSpecifier);\n\n  const { dir, base } = parsePath(absoluteModuleSpecifier);\n  const relativeModuleSpecifier = `./${base}`;\n\n  const req = createRequire(\n    join(dir, 'imaginaryUncacheableRequireResolveScript')\n  );\n  return req.resolve(relativeModuleSpecifier, {\n    paths: [\n      `${guaranteedNonexistentDirectoryPrefix}${guaranteedNonexistentDirectorySuffix++}`,\n      ...(req.resolve.paths(relativeModuleSpecifier) || []),\n    ],\n  });\n}\n\n/**\n * Evaluate an [eval] or [stdin] script\n */\nfunction evalAndExitOnTsError(\n  replService: ReplService,\n  module: Module,\n  code: string,\n  isPrinted: boolean,\n  filenameAndDirname: 'eval' | 'stdin'\n) {\n  let result: any;\n  setupContext(global, module, filenameAndDirname);\n\n  try {\n    result = replService.evalCode(code);\n  } catch (error) {\n    if (error instanceof TSError) {\n      console.error(error);\n      process.exit(1);\n    }\n\n    throw error;\n  }\n\n  if (isPrinted) {\n    console.log(\n      typeof result === 'string'\n        ? result\n        : inspect(result, { colors: process.stdout.isTTY })\n    );\n  }\n}\n\nif (require.main === module) {\n  main();\n}\n"]}