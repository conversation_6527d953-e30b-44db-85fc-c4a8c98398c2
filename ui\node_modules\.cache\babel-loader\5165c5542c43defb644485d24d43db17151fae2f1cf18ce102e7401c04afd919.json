{"ast": null, "code": "\"use client\";\n\nimport _taggedTemplateLiteral from \"D:/repos-personal/repos/openapi-to-mcp/ui/node_modules/@babel/runtime/helpers/esm/taggedTemplateLiteral.js\";\nimport _objectSpread from \"D:/repos-personal/repos/openapi-to-mcp/ui/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nvar _templateObject, _templateObject2, _templateObject3, _templateObject4, _templateObject5, _templateObject6, _templateObject7, _templateObject8, _templateObject9, _templateObject0, _templateObject1, _templateObject10, _templateObject11, _templateObject12, _templateObject13, _templateObject14;\nvar W = e => typeof e == \"function\",\n  f = (e, t) => W(e) ? e(t) : e;\nvar F = (() => {\n    let e = 0;\n    return () => (++e).toString();\n  })(),\n  A = (() => {\n    let e;\n    return () => {\n      if (e === void 0 && typeof window < \"u\") {\n        let t = matchMedia(\"(prefers-reduced-motion: reduce)\");\n        e = !t || t.matches;\n      }\n      return e;\n    };\n  })();\nimport { useEffect as H, useState as j, useRef as Q } from \"react\";\nvar Y = 20;\nvar U = (e, t) => {\n    switch (t.type) {\n      case 0:\n        return _objectSpread(_objectSpread({}, e), {}, {\n          toasts: [t.toast, ...e.toasts].slice(0, Y)\n        });\n      case 1:\n        return _objectSpread(_objectSpread({}, e), {}, {\n          toasts: e.toasts.map(o => o.id === t.toast.id ? _objectSpread(_objectSpread({}, o), t.toast) : o)\n        });\n      case 2:\n        let {\n          toast: r\n        } = t;\n        return U(e, {\n          type: e.toasts.find(o => o.id === r.id) ? 1 : 0,\n          toast: r\n        });\n      case 3:\n        let {\n          toastId: s\n        } = t;\n        return _objectSpread(_objectSpread({}, e), {}, {\n          toasts: e.toasts.map(o => o.id === s || s === void 0 ? _objectSpread(_objectSpread({}, o), {}, {\n            dismissed: !0,\n            visible: !1\n          }) : o)\n        });\n      case 4:\n        return t.toastId === void 0 ? _objectSpread(_objectSpread({}, e), {}, {\n          toasts: []\n        }) : _objectSpread(_objectSpread({}, e), {}, {\n          toasts: e.toasts.filter(o => o.id !== t.toastId)\n        });\n      case 5:\n        return _objectSpread(_objectSpread({}, e), {}, {\n          pausedAt: t.time\n        });\n      case 6:\n        let a = t.time - (e.pausedAt || 0);\n        return _objectSpread(_objectSpread({}, e), {}, {\n          pausedAt: void 0,\n          toasts: e.toasts.map(o => _objectSpread(_objectSpread({}, o), {}, {\n            pauseDuration: o.pauseDuration + a\n          }))\n        });\n    }\n  },\n  P = [],\n  y = {\n    toasts: [],\n    pausedAt: void 0\n  },\n  u = e => {\n    y = U(y, e), P.forEach(t => {\n      t(y);\n    });\n  },\n  q = {\n    blank: 4e3,\n    error: 4e3,\n    success: 2e3,\n    loading: 1 / 0,\n    custom: 4e3\n  },\n  D = function () {\n    let e = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    let [t, r] = j(y),\n      s = Q(y);\n    H(() => (s.current !== y && r(y), P.push(r), () => {\n      let o = P.indexOf(r);\n      o > -1 && P.splice(o, 1);\n    }), []);\n    let a = t.toasts.map(o => {\n      var n, i, p;\n      return _objectSpread(_objectSpread(_objectSpread(_objectSpread({}, e), e[o.type]), o), {}, {\n        removeDelay: o.removeDelay || ((n = e[o.type]) == null ? void 0 : n.removeDelay) || (e == null ? void 0 : e.removeDelay),\n        duration: o.duration || ((i = e[o.type]) == null ? void 0 : i.duration) || (e == null ? void 0 : e.duration) || q[o.type],\n        style: _objectSpread(_objectSpread(_objectSpread({}, e.style), (p = e[o.type]) == null ? void 0 : p.style), o.style)\n      });\n    });\n    return _objectSpread(_objectSpread({}, t), {}, {\n      toasts: a\n    });\n  };\nvar J = function (e) {\n    let t = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : \"blank\";\n    let r = arguments.length > 2 ? arguments[2] : undefined;\n    return _objectSpread(_objectSpread({\n      createdAt: Date.now(),\n      visible: !0,\n      dismissed: !1,\n      type: t,\n      ariaProps: {\n        role: \"status\",\n        \"aria-live\": \"polite\"\n      },\n      message: e,\n      pauseDuration: 0\n    }, r), {}, {\n      id: (r == null ? void 0 : r.id) || F()\n    });\n  },\n  x = e => (t, r) => {\n    let s = J(t, e, r);\n    return u({\n      type: 2,\n      toast: s\n    }), s.id;\n  },\n  c = (e, t) => x(\"blank\")(e, t);\nc.error = x(\"error\");\nc.success = x(\"success\");\nc.loading = x(\"loading\");\nc.custom = x(\"custom\");\nc.dismiss = e => {\n  u({\n    type: 3,\n    toastId: e\n  });\n};\nc.remove = e => u({\n  type: 4,\n  toastId: e\n});\nc.promise = (e, t, r) => {\n  let s = c.loading(t.loading, _objectSpread(_objectSpread({}, r), r == null ? void 0 : r.loading));\n  return typeof e == \"function\" && (e = e()), e.then(a => {\n    let o = t.success ? f(t.success, a) : void 0;\n    return o ? c.success(o, _objectSpread(_objectSpread({\n      id: s\n    }, r), r == null ? void 0 : r.success)) : c.dismiss(s), a;\n  }).catch(a => {\n    let o = t.error ? f(t.error, a) : void 0;\n    o ? c.error(o, _objectSpread(_objectSpread({\n      id: s\n    }, r), r == null ? void 0 : r.error)) : c.dismiss(s);\n  }), e;\n};\nimport { useEffect as $, useCallback as L } from \"react\";\nvar K = (e, t) => {\n    u({\n      type: 1,\n      toast: {\n        id: e,\n        height: t\n      }\n    });\n  },\n  X = () => {\n    u({\n      type: 5,\n      time: Date.now()\n    });\n  },\n  b = new Map(),\n  Z = 1e3,\n  ee = function (e) {\n    let t = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : Z;\n    if (b.has(e)) return;\n    let r = setTimeout(() => {\n      b.delete(e), u({\n        type: 4,\n        toastId: e\n      });\n    }, t);\n    b.set(e, r);\n  },\n  O = e => {\n    let {\n      toasts: t,\n      pausedAt: r\n    } = D(e);\n    $(() => {\n      if (r) return;\n      let o = Date.now(),\n        n = t.map(i => {\n          if (i.duration === 1 / 0) return;\n          let p = (i.duration || 0) + i.pauseDuration - (o - i.createdAt);\n          if (p < 0) {\n            i.visible && c.dismiss(i.id);\n            return;\n          }\n          return setTimeout(() => c.dismiss(i.id), p);\n        });\n      return () => {\n        n.forEach(i => i && clearTimeout(i));\n      };\n    }, [t, r]);\n    let s = L(() => {\n        r && u({\n          type: 6,\n          time: Date.now()\n        });\n      }, [r]),\n      a = L((o, n) => {\n        let {\n            reverseOrder: i = !1,\n            gutter: p = 8,\n            defaultPosition: d\n          } = n || {},\n          h = t.filter(m => (m.position || d) === (o.position || d) && m.height),\n          v = h.findIndex(m => m.id === o.id),\n          S = h.filter((m, E) => E < v && m.visible).length;\n        return h.filter(m => m.visible).slice(...(i ? [S + 1] : [0, S])).reduce((m, E) => m + (E.height || 0) + p, 0);\n      }, [t]);\n    return $(() => {\n      t.forEach(o => {\n        if (o.dismissed) ee(o.id, o.removeDelay);else {\n          let n = b.get(o.id);\n          n && (clearTimeout(n), b.delete(o.id));\n        }\n      });\n    }, [t]), {\n      toasts: t,\n      handlers: {\n        updateHeight: K,\n        startPause: X,\n        endPause: s,\n        calculateOffset: a\n      }\n    };\n  };\nimport * as l from \"react\";\nimport { styled as B, keyframes as z } from \"goober\";\nimport * as g from \"react\";\nimport { styled as w, keyframes as me } from \"goober\";\nimport { styled as te, keyframes as I } from \"goober\";\nvar oe = I(_templateObject || (_templateObject = _taggedTemplateLiteral([\"\\nfrom {\\n  transform: scale(0) rotate(45deg);\\n\\topacity: 0;\\n}\\nto {\\n transform: scale(1) rotate(45deg);\\n  opacity: 1;\\n}\"]))),\n  re = I(_templateObject2 || (_templateObject2 = _taggedTemplateLiteral([\"\\nfrom {\\n  transform: scale(0);\\n  opacity: 0;\\n}\\nto {\\n  transform: scale(1);\\n  opacity: 1;\\n}\"]))),\n  se = I(_templateObject3 || (_templateObject3 = _taggedTemplateLiteral([\"\\nfrom {\\n  transform: scale(0) rotate(90deg);\\n\\topacity: 0;\\n}\\nto {\\n  transform: scale(1) rotate(90deg);\\n\\topacity: 1;\\n}\"]))),\n  k = te(\"div\")(_templateObject4 || (_templateObject4 = _taggedTemplateLiteral([\"\\n  width: 20px;\\n  opacity: 0;\\n  height: 20px;\\n  border-radius: 10px;\\n  background: \", \";\\n  position: relative;\\n  transform: rotate(45deg);\\n\\n  animation: \", \" 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)\\n    forwards;\\n  animation-delay: 100ms;\\n\\n  &:after,\\n  &:before {\\n    content: '';\\n    animation: \", \" 0.15s ease-out forwards;\\n    animation-delay: 150ms;\\n    position: absolute;\\n    border-radius: 3px;\\n    opacity: 0;\\n    background: \", \";\\n    bottom: 9px;\\n    left: 4px;\\n    height: 2px;\\n    width: 12px;\\n  }\\n\\n  &:before {\\n    animation: \", \" 0.15s ease-out forwards;\\n    animation-delay: 180ms;\\n    transform: rotate(90deg);\\n  }\\n\"])), e => e.primary || \"#ff4b4b\", oe, re, e => e.secondary || \"#fff\", se);\nimport { styled as ae, keyframes as ie } from \"goober\";\nvar ne = ie(_templateObject5 || (_templateObject5 = _taggedTemplateLiteral([\"\\n  from {\\n    transform: rotate(0deg);\\n  }\\n  to {\\n    transform: rotate(360deg);\\n  }\\n\"]))),\n  V = ae(\"div\")(_templateObject6 || (_templateObject6 = _taggedTemplateLiteral([\"\\n  width: 12px;\\n  height: 12px;\\n  box-sizing: border-box;\\n  border: 2px solid;\\n  border-radius: 100%;\\n  border-color: \", \";\\n  border-right-color: \", \";\\n  animation: \", \" 1s linear infinite;\\n\"])), e => e.secondary || \"#e0e0e0\", e => e.primary || \"#616161\", ne);\nimport { styled as ce, keyframes as N } from \"goober\";\nvar pe = N(_templateObject7 || (_templateObject7 = _taggedTemplateLiteral([\"\\nfrom {\\n  transform: scale(0) rotate(45deg);\\n\\topacity: 0;\\n}\\nto {\\n  transform: scale(1) rotate(45deg);\\n\\topacity: 1;\\n}\"]))),\n  de = N(_templateObject8 || (_templateObject8 = _taggedTemplateLiteral([\"\\n0% {\\n\\theight: 0;\\n\\twidth: 0;\\n\\topacity: 0;\\n}\\n40% {\\n  height: 0;\\n\\twidth: 6px;\\n\\topacity: 1;\\n}\\n100% {\\n  opacity: 1;\\n  height: 10px;\\n}\"]))),\n  _ = ce(\"div\")(_templateObject9 || (_templateObject9 = _taggedTemplateLiteral([\"\\n  width: 20px;\\n  opacity: 0;\\n  height: 20px;\\n  border-radius: 10px;\\n  background: \", \";\\n  position: relative;\\n  transform: rotate(45deg);\\n\\n  animation: \", \" 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)\\n    forwards;\\n  animation-delay: 100ms;\\n  &:after {\\n    content: '';\\n    box-sizing: border-box;\\n    animation: \", \" 0.2s ease-out forwards;\\n    opacity: 0;\\n    animation-delay: 200ms;\\n    position: absolute;\\n    border-right: 2px solid;\\n    border-bottom: 2px solid;\\n    border-color: \", \";\\n    bottom: 6px;\\n    left: 6px;\\n    height: 10px;\\n    width: 6px;\\n  }\\n\"])), e => e.primary || \"#61d345\", pe, de, e => e.secondary || \"#fff\");\nvar ue = w(\"div\")(_templateObject0 || (_templateObject0 = _taggedTemplateLiteral([\"\\n  position: absolute;\\n\"]))),\n  le = w(\"div\")(_templateObject1 || (_templateObject1 = _taggedTemplateLiteral([\"\\n  position: relative;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  min-width: 20px;\\n  min-height: 20px;\\n\"]))),\n  fe = me(_templateObject10 || (_templateObject10 = _taggedTemplateLiteral([\"\\nfrom {\\n  transform: scale(0.6);\\n  opacity: 0.4;\\n}\\nto {\\n  transform: scale(1);\\n  opacity: 1;\\n}\"]))),\n  Te = w(\"div\")(_templateObject11 || (_templateObject11 = _taggedTemplateLiteral([\"\\n  position: relative;\\n  transform: scale(0.6);\\n  opacity: 0.4;\\n  min-width: 20px;\\n  animation: \", \" 0.3s 0.12s cubic-bezier(0.175, 0.885, 0.32, 1.275)\\n    forwards;\\n\"])), fe),\n  M = _ref => {\n    let {\n      toast: e\n    } = _ref;\n    let {\n      icon: t,\n      type: r,\n      iconTheme: s\n    } = e;\n    return t !== void 0 ? typeof t == \"string\" ? g.createElement(Te, null, t) : t : r === \"blank\" ? null : g.createElement(le, null, g.createElement(V, _objectSpread({}, s)), r !== \"loading\" && g.createElement(ue, null, r === \"error\" ? g.createElement(k, _objectSpread({}, s)) : g.createElement(_, _objectSpread({}, s))));\n  };\nvar ye = e => \"\\n0% {transform: translate3d(0,\".concat(e * -200, \"%,0) scale(.6); opacity:.5;}\\n100% {transform: translate3d(0,0,0) scale(1); opacity:1;}\\n\"),\n  ge = e => \"\\n0% {transform: translate3d(0,0,-1px) scale(1); opacity:1;}\\n100% {transform: translate3d(0,\".concat(e * -150, \"%,-1px) scale(.6); opacity:0;}\\n\"),\n  he = \"0%{opacity:0;} 100%{opacity:1;}\",\n  xe = \"0%{opacity:1;} 100%{opacity:0;}\",\n  be = B(\"div\")(_templateObject12 || (_templateObject12 = _taggedTemplateLiteral([\"\\n  display: flex;\\n  align-items: center;\\n  background: #fff;\\n  color: #363636;\\n  line-height: 1.3;\\n  will-change: transform;\\n  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);\\n  max-width: 350px;\\n  pointer-events: auto;\\n  padding: 8px 10px;\\n  border-radius: 8px;\\n\"]))),\n  Se = B(\"div\")(_templateObject13 || (_templateObject13 = _taggedTemplateLiteral([\"\\n  display: flex;\\n  justify-content: center;\\n  margin: 4px 10px;\\n  color: inherit;\\n  flex: 1 1 auto;\\n  white-space: pre-line;\\n\"]))),\n  Ae = (e, t) => {\n    let s = e.includes(\"top\") ? 1 : -1,\n      [a, o] = A() ? [he, xe] : [ye(s), ge(s)];\n    return {\n      animation: t ? \"\".concat(z(a), \" 0.35s cubic-bezier(.21,1.02,.73,1) forwards\") : \"\".concat(z(o), \" 0.4s forwards cubic-bezier(.06,.71,.55,1)\")\n    };\n  },\n  C = l.memo(_ref2 => {\n    let {\n      toast: e,\n      position: t,\n      style: r,\n      children: s\n    } = _ref2;\n    let a = e.height ? Ae(e.position || t || \"top-center\", e.visible) : {\n        opacity: 0\n      },\n      o = l.createElement(M, {\n        toast: e\n      }),\n      n = l.createElement(Se, _objectSpread({}, e.ariaProps), f(e.message, e));\n    return l.createElement(be, {\n      className: e.className,\n      style: _objectSpread(_objectSpread(_objectSpread({}, a), r), e.style)\n    }, typeof s == \"function\" ? s({\n      icon: o,\n      message: n\n    }) : l.createElement(l.Fragment, null, o, n));\n  });\nimport { css as Pe, setup as Re } from \"goober\";\nimport * as T from \"react\";\nRe(T.createElement);\nvar ve = _ref3 => {\n    let {\n      id: e,\n      className: t,\n      style: r,\n      onHeightUpdate: s,\n      children: a\n    } = _ref3;\n    let o = T.useCallback(n => {\n      if (n) {\n        let i = () => {\n          let p = n.getBoundingClientRect().height;\n          s(e, p);\n        };\n        i(), new MutationObserver(i).observe(n, {\n          subtree: !0,\n          childList: !0,\n          characterData: !0\n        });\n      }\n    }, [e, s]);\n    return T.createElement(\"div\", {\n      ref: o,\n      className: t,\n      style: r\n    }, a);\n  },\n  Ee = (e, t) => {\n    let r = e.includes(\"top\"),\n      s = r ? {\n        top: 0\n      } : {\n        bottom: 0\n      },\n      a = e.includes(\"center\") ? {\n        justifyContent: \"center\"\n      } : e.includes(\"right\") ? {\n        justifyContent: \"flex-end\"\n      } : {};\n    return _objectSpread(_objectSpread({\n      left: 0,\n      right: 0,\n      display: \"flex\",\n      position: \"absolute\",\n      transition: A() ? void 0 : \"all 230ms cubic-bezier(.21,1.02,.73,1)\",\n      transform: \"translateY(\".concat(t * (r ? 1 : -1), \"px)\")\n    }, s), a);\n  },\n  De = Pe(_templateObject14 || (_templateObject14 = _taggedTemplateLiteral([\"\\n  z-index: 9999;\\n  > * {\\n    pointer-events: auto;\\n  }\\n\"]))),\n  R = 16,\n  Oe = _ref4 => {\n    let {\n      reverseOrder: e,\n      position: t = \"top-center\",\n      toastOptions: r,\n      gutter: s,\n      children: a,\n      containerStyle: o,\n      containerClassName: n\n    } = _ref4;\n    let {\n      toasts: i,\n      handlers: p\n    } = O(r);\n    return T.createElement(\"div\", {\n      id: \"_rht_toaster\",\n      style: _objectSpread({\n        position: \"fixed\",\n        zIndex: 9999,\n        top: R,\n        left: R,\n        right: R,\n        bottom: R,\n        pointerEvents: \"none\"\n      }, o),\n      className: n,\n      onMouseEnter: p.startPause,\n      onMouseLeave: p.endPause\n    }, i.map(d => {\n      let h = d.position || t,\n        v = p.calculateOffset(d, {\n          reverseOrder: e,\n          gutter: s,\n          defaultPosition: t\n        }),\n        S = Ee(h, v);\n      return T.createElement(ve, {\n        id: d.id,\n        key: d.id,\n        onHeightUpdate: p.updateHeight,\n        className: d.visible ? De : \"\",\n        style: S\n      }, d.type === \"custom\" ? f(d.message, d) : a ? a(d) : T.createElement(C, {\n        toast: d,\n        position: h\n      }));\n    }));\n  };\nvar Vt = c;\nexport { _ as CheckmarkIcon, k as ErrorIcon, V as LoaderIcon, C as ToastBar, M as ToastIcon, Oe as Toaster, Vt as default, f as resolveValue, c as toast, O as useToaster, D as useToasterStore };", "map": {"version": 3, "names": ["W", "e", "f", "resolveValue", "t", "F", "toString", "A", "window", "matchMedia", "matches", "useEffect", "H", "useState", "j", "useRef", "Q", "Y", "U", "reducer", "type", "_objectSpread", "toasts", "toast", "slice", "map", "o", "id", "r", "find", "toastId", "s", "dismissed", "visible", "filter", "pausedAt", "time", "a", "pauseDuration", "P", "y", "u", "for<PERSON>ach", "q", "blank", "error", "success", "loading", "custom", "D", "useStore", "arguments", "length", "undefined", "current", "push", "indexOf", "splice", "n", "i", "p", "<PERSON><PERSON><PERSON><PERSON>", "duration", "style", "J", "createToast", "createdAt", "Date", "now", "ariaProps", "role", "message", "x", "c", "dismiss", "remove", "promise", "then", "catch", "$", "useCallback", "L", "K", "updateHeight", "height", "X", "startPause", "b", "Map", "Z", "ee", "addToRemoveQueue", "has", "setTimeout", "delete", "set", "O", "clearTimeout", "reverseOrder", "gutter", "defaultPosition", "d", "h", "m", "position", "v", "findIndex", "S", "E", "reduce", "get", "handlers", "endPause", "calculateOffset", "l", "styled", "B", "keyframes", "z", "g", "w", "me", "te", "I", "oe", "_templateObject", "_taggedTemplateLiteral", "re", "_templateObject2", "se", "_templateObject3", "k", "_templateObject4", "primary", "secondary", "ae", "ie", "ne", "_templateObject5", "V", "_templateObject6", "ce", "N", "pe", "_templateObject7", "de", "_templateObject8", "_", "_templateObject9", "ue", "_templateObject0", "le", "_templateObject1", "fe", "_templateObject10", "Te", "_templateObject11", "M", "_ref", "icon", "iconTheme", "createElement", "ye", "concat", "ge", "he", "xe", "be", "_templateObject12", "Se", "_templateObject13", "Ae", "getAnimationStyle", "includes", "animation", "C", "memo", "_ref2", "children", "opacity", "className", "Fragment", "css", "Pe", "setup", "Re", "T", "ve", "_ref3", "onHeightUpdate", "getBoundingClientRect", "MutationObserver", "observe", "subtree", "childList", "characterData", "ref", "Ee", "getPositionStyle", "top", "bottom", "justifyContent", "left", "right", "display", "transition", "transform", "De", "_templateObject14", "R", "Oe", "_ref4", "toastOptions", "containerStyle", "containerClassName", "zIndex", "pointerEvents", "onMouseEnter", "onMouseLeave", "key", "Vt", "CheckmarkIcon", "ErrorIcon", "LoaderIcon", "ToastBar", "ToastIcon", "Toaster", "default", "useToaster", "useToasterStore"], "sources": ["D:\\repos-personal\\repos\\openapi-to-mcp\\ui\\node_modules\\react-hot-toast\\src\\core\\types.ts", "D:\\repos-personal\\repos\\openapi-to-mcp\\ui\\node_modules\\react-hot-toast\\src\\core\\utils.ts", "D:\\repos-personal\\repos\\openapi-to-mcp\\ui\\node_modules\\react-hot-toast\\src\\core\\store.ts", "D:\\repos-personal\\repos\\openapi-to-mcp\\ui\\node_modules\\react-hot-toast\\src\\core\\toast.ts", "D:\\repos-personal\\repos\\openapi-to-mcp\\ui\\node_modules\\react-hot-toast\\src\\core\\use-toaster.ts", "D:\\repos-personal\\repos\\openapi-to-mcp\\ui\\node_modules\\react-hot-toast\\src\\components\\toast-bar.tsx", "D:\\repos-personal\\repos\\openapi-to-mcp\\ui\\node_modules\\react-hot-toast\\src\\components\\toast-icon.tsx", "D:\\repos-personal\\repos\\openapi-to-mcp\\ui\\node_modules\\react-hot-toast\\src\\components\\error.tsx", "D:\\repos-personal\\repos\\openapi-to-mcp\\ui\\node_modules\\react-hot-toast\\src\\components\\loader.tsx", "D:\\repos-personal\\repos\\openapi-to-mcp\\ui\\node_modules\\react-hot-toast\\src\\components\\checkmark.tsx", "D:\\repos-personal\\repos\\openapi-to-mcp\\ui\\node_modules\\react-hot-toast\\src\\components\\toaster.tsx", "D:\\repos-personal\\repos\\openapi-to-mcp\\ui\\node_modules\\react-hot-toast\\src\\index.ts"], "sourcesContent": ["import { CSSProperties } from 'react';\n\nexport type ToastType = 'success' | 'error' | 'loading' | 'blank' | 'custom';\nexport type ToastPosition =\n  | 'top-left'\n  | 'top-center'\n  | 'top-right'\n  | 'bottom-left'\n  | 'bottom-center'\n  | 'bottom-right';\n\nexport type Renderable = React.ReactElement | string | null;\n\nexport interface IconTheme {\n  primary: string;\n  secondary: string;\n}\n\nexport type ValueFunction<TValue, TArg> = (arg: TArg) => TValue;\nexport type ValueOrFunction<TValue, TArg> =\n  | TValue\n  | ValueFunction<TValue, TArg>;\n\nconst isFunction = <TValue, TArg>(\n  valOrFunction: ValueOrFunction<TValue, TArg>\n): valOrFunction is ValueFunction<TValue, TArg> =>\n  typeof valOrFunction === 'function';\n\nexport const resolveValue = <TValue, TArg>(\n  valOrFunction: ValueOrFunction<TValue, TArg>,\n  arg: TArg\n): TValue => (isFunction(valOrFunction) ? valOrFunction(arg) : valOrFunction);\n\nexport interface Toast {\n  type: ToastType;\n  id: string;\n  message: ValueOrFunction<Renderable, Toast>;\n  icon?: Renderable;\n  duration?: number;\n  pauseDuration: number;\n  position?: ToastPosition;\n  removeDelay?: number;\n\n  ariaProps: {\n    role: 'status' | 'alert';\n    'aria-live': 'assertive' | 'off' | 'polite';\n  };\n\n  style?: CSSProperties;\n  className?: string;\n  iconTheme?: IconTheme;\n\n  createdAt: number;\n  visible: boolean;\n  dismissed: boolean;\n  height?: number;\n}\n\nexport type ToastOptions = Partial<\n  Pick<\n    Toast,\n    | 'id'\n    | 'icon'\n    | 'duration'\n    | 'ariaProps'\n    | 'className'\n    | 'style'\n    | 'position'\n    | 'iconTheme'\n    | 'removeDelay'\n  >\n>;\n\nexport type DefaultToastOptions = ToastOptions & {\n  [key in ToastType]?: ToastOptions;\n};\n\nexport interface ToasterProps {\n  position?: ToastPosition;\n  toastOptions?: DefaultToastOptions;\n  reverseOrder?: boolean;\n  gutter?: number;\n  containerStyle?: React.CSSProperties;\n  containerClassName?: string;\n  children?: (toast: Toast) => React.ReactElement;\n}\n\nexport interface ToastWrapperProps {\n  id: string;\n  className?: string;\n  style?: React.CSSProperties;\n  onHeightUpdate: (id: string, height: number) => void;\n  children?: React.ReactNode;\n}\n", "export const genId = (() => {\n  let count = 0;\n  return () => {\n    return (++count).toString();\n  };\n})();\n\nexport const prefersReducedMotion = (() => {\n  // Cache result\n  let shouldReduceMotion: boolean | undefined = undefined;\n\n  return () => {\n    if (shouldReduceMotion === undefined && typeof window !== 'undefined') {\n      const mediaQuery = matchMedia('(prefers-reduced-motion: reduce)');\n      shouldReduceMotion = !mediaQuery || mediaQuery.matches;\n    }\n    return shouldReduceMotion;\n  };\n})();\n", "import { useEffect, useState, useRef } from 'react';\nimport { DefaultToastOptions, Toast, ToastType } from './types';\n\nconst TOAST_LIMIT = 20;\n\nexport enum ActionType {\n  ADD_TOAST,\n  UPDATE_TOAST,\n  UPSERT_TOAST,\n  DISMISS_TOAST,\n  REMOVE_TOAST,\n  START_PAUSE,\n  END_PAUSE,\n}\n\ntype Action =\n  | {\n      type: ActionType.ADD_TOAST;\n      toast: Toast;\n    }\n  | {\n      type: ActionType.UPSERT_TOAST;\n      toast: Toast;\n    }\n  | {\n      type: ActionType.UPDATE_TOAST;\n      toast: Partial<Toast>;\n    }\n  | {\n      type: ActionType.DISMISS_TOAST;\n      toastId?: string;\n    }\n  | {\n      type: ActionType.REMOVE_TOAST;\n      toastId?: string;\n    }\n  | {\n      type: ActionType.START_PAUSE;\n      time: number;\n    }\n  | {\n      type: ActionType.END_PAUSE;\n      time: number;\n    };\n\ninterface State {\n  toasts: Toast[];\n  pausedAt: number | undefined;\n}\n\nexport const reducer = (state: State, action: Action): State => {\n  switch (action.type) {\n    case ActionType.ADD_TOAST:\n      return {\n        ...state,\n        toasts: [action.toast, ...state.toasts].slice(0, TOAST_LIMIT),\n      };\n\n    case ActionType.UPDATE_TOAST:\n      return {\n        ...state,\n        toasts: state.toasts.map((t) =>\n          t.id === action.toast.id ? { ...t, ...action.toast } : t\n        ),\n      };\n\n    case ActionType.UPSERT_TOAST:\n      const { toast } = action;\n      return reducer(state, {\n        type: state.toasts.find((t) => t.id === toast.id)\n          ? ActionType.UPDATE_TOAST\n          : ActionType.ADD_TOAST,\n        toast,\n      });\n\n    case ActionType.DISMISS_TOAST:\n      const { toastId } = action;\n\n      return {\n        ...state,\n        toasts: state.toasts.map((t) =>\n          t.id === toastId || toastId === undefined\n            ? {\n                ...t,\n                dismissed: true,\n                visible: false,\n              }\n            : t\n        ),\n      };\n    case ActionType.REMOVE_TOAST:\n      if (action.toastId === undefined) {\n        return {\n          ...state,\n          toasts: [],\n        };\n      }\n      return {\n        ...state,\n        toasts: state.toasts.filter((t) => t.id !== action.toastId),\n      };\n\n    case ActionType.START_PAUSE:\n      return {\n        ...state,\n        pausedAt: action.time,\n      };\n\n    case ActionType.END_PAUSE:\n      const diff = action.time - (state.pausedAt || 0);\n\n      return {\n        ...state,\n        pausedAt: undefined,\n        toasts: state.toasts.map((t) => ({\n          ...t,\n          pauseDuration: t.pauseDuration + diff,\n        })),\n      };\n  }\n};\n\nconst listeners: Array<(state: State) => void> = [];\n\nlet memoryState: State = { toasts: [], pausedAt: undefined };\n\nexport const dispatch = (action: Action) => {\n  memoryState = reducer(memoryState, action);\n  listeners.forEach((listener) => {\n    listener(memoryState);\n  });\n};\n\nexport const defaultTimeouts: {\n  [key in ToastType]: number;\n} = {\n  blank: 4000,\n  error: 4000,\n  success: 2000,\n  loading: Infinity,\n  custom: 4000,\n};\n\nexport const useStore = (toastOptions: DefaultToastOptions = {}): State => {\n  const [state, setState] = useState<State>(memoryState);\n  const initial = useRef(memoryState);\n\n  // TODO: Switch to useSyncExternalStore when targeting React 18+\n  useEffect(() => {\n    if (initial.current !== memoryState) {\n      setState(memoryState);\n    }\n    listeners.push(setState);\n    return () => {\n      const index = listeners.indexOf(setState);\n      if (index > -1) {\n        listeners.splice(index, 1);\n      }\n    };\n  }, []);\n\n  const mergedToasts = state.toasts.map((t) => ({\n    ...toastOptions,\n    ...toastOptions[t.type],\n    ...t,\n    removeDelay:\n      t.removeDelay ||\n      toastOptions[t.type]?.removeDelay ||\n      toastOptions?.removeDelay,\n    duration:\n      t.duration ||\n      toastOptions[t.type]?.duration ||\n      toastOptions?.duration ||\n      defaultTimeouts[t.type],\n    style: {\n      ...toastOptions.style,\n      ...toastOptions[t.type]?.style,\n      ...t.style,\n    },\n  }));\n\n  return {\n    ...state,\n    toasts: mergedToasts,\n  };\n};\n", "import {\n  Renderable,\n  Toast,\n  ToastOptions,\n  ToastType,\n  DefaultToastOptions,\n  ValueOrFunction,\n  resolveValue,\n} from './types';\nimport { genId } from './utils';\nimport { dispatch, ActionType } from './store';\n\ntype Message = ValueOrFunction<Renderable, Toast>;\n\ntype ToastHandler = (message: Message, options?: ToastOptions) => string;\n\nconst createToast = (\n  message: Message,\n  type: ToastType = 'blank',\n  opts?: ToastOptions\n): Toast => ({\n  createdAt: Date.now(),\n  visible: true,\n  dismissed: false,\n  type,\n  ariaProps: {\n    role: 'status',\n    'aria-live': 'polite',\n  },\n  message,\n  pauseDuration: 0,\n  ...opts,\n  id: opts?.id || genId(),\n});\n\nconst createHandler =\n  (type?: ToastType): ToastHandler =>\n  (message, options) => {\n    const toast = createToast(message, type, options);\n    dispatch({ type: ActionType.UPSERT_TOAST, toast });\n    return toast.id;\n  };\n\nconst toast = (message: Message, opts?: ToastOptions) =>\n  createHandler('blank')(message, opts);\n\ntoast.error = createHandler('error');\ntoast.success = createHandler('success');\ntoast.loading = createHandler('loading');\ntoast.custom = createHandler('custom');\n\ntoast.dismiss = (toastId?: string) => {\n  dispatch({\n    type: ActionType.DISMISS_TOAST,\n    toastId,\n  });\n};\n\ntoast.remove = (toastId?: string) =>\n  dispatch({ type: ActionType.REMOVE_TOAST, toastId });\n\ntoast.promise = <T>(\n  promise: Promise<T> | (() => Promise<T>),\n  msgs: {\n    loading: Renderable;\n    success?: ValueOrFunction<Renderable, T>;\n    error?: ValueOrFunction<Renderable, any>;\n  },\n  opts?: DefaultToastOptions\n) => {\n  const id = toast.loading(msgs.loading, { ...opts, ...opts?.loading });\n\n  if (typeof promise === 'function') {\n    promise = promise();\n  }\n\n  promise\n    .then((p) => {\n      const successMessage = msgs.success\n        ? resolveValue(msgs.success, p)\n        : undefined;\n\n      if (successMessage) {\n        toast.success(successMessage, {\n          id,\n          ...opts,\n          ...opts?.success,\n        });\n      } else {\n        toast.dismiss(id);\n      }\n      return p;\n    })\n    .catch((e) => {\n      const errorMessage = msgs.error ? resolveValue(msgs.error, e) : undefined;\n\n      if (errorMessage) {\n        toast.error(errorMessage, {\n          id,\n          ...opts,\n          ...opts?.error,\n        });\n      } else {\n        toast.dismiss(id);\n      }\n    });\n\n  return promise;\n};\n\nexport { toast };\n", "import { useEffect, useCallback } from 'react';\nimport { dispatch, ActionType, useStore } from './store';\nimport { toast } from './toast';\nimport { DefaultToastOptions, Toast, ToastPosition } from './types';\n\nconst updateHeight = (toastId: string, height: number) => {\n  dispatch({\n    type: ActionType.UPDATE_TOAST,\n    toast: { id: toastId, height },\n  });\n};\nconst startPause = () => {\n  dispatch({\n    type: ActionType.START_PAUSE,\n    time: Date.now(),\n  });\n};\n\nconst toastTimeouts = new Map<Toast['id'], ReturnType<typeof setTimeout>>();\n\nexport const REMOVE_DELAY = 1000;\n\nconst addToRemoveQueue = (toastId: string, removeDelay = REMOVE_DELAY) => {\n  if (toastTimeouts.has(toastId)) {\n    return;\n  }\n\n  const timeout = setTimeout(() => {\n    toastTimeouts.delete(toastId);\n    dispatch({\n      type: ActionType.REMOVE_TOAST,\n      toastId: toastId,\n    });\n  }, removeDelay);\n\n  toastTimeouts.set(toastId, timeout);\n};\n\nexport const useToaster = (toastOptions?: DefaultToastOptions) => {\n  const { toasts, pausedAt } = useStore(toastOptions);\n\n  useEffect(() => {\n    if (pausedAt) {\n      return;\n    }\n\n    const now = Date.now();\n    const timeouts = toasts.map((t) => {\n      if (t.duration === Infinity) {\n        return;\n      }\n\n      const durationLeft =\n        (t.duration || 0) + t.pauseDuration - (now - t.createdAt);\n\n      if (durationLeft < 0) {\n        if (t.visible) {\n          toast.dismiss(t.id);\n        }\n        return;\n      }\n      return setTimeout(() => toast.dismiss(t.id), durationLeft);\n    });\n\n    return () => {\n      timeouts.forEach((timeout) => timeout && clearTimeout(timeout));\n    };\n  }, [toasts, pausedAt]);\n\n  const endPause = useCallback(() => {\n    if (pausedAt) {\n      dispatch({ type: ActionType.END_PAUSE, time: Date.now() });\n    }\n  }, [pausedAt]);\n\n  const calculateOffset = useCallback(\n    (\n      toast: Toast,\n      opts?: {\n        reverseOrder?: boolean;\n        gutter?: number;\n        defaultPosition?: ToastPosition;\n      }\n    ) => {\n      const { reverseOrder = false, gutter = 8, defaultPosition } = opts || {};\n\n      const relevantToasts = toasts.filter(\n        (t) =>\n          (t.position || defaultPosition) ===\n            (toast.position || defaultPosition) && t.height\n      );\n      const toastIndex = relevantToasts.findIndex((t) => t.id === toast.id);\n      const toastsBefore = relevantToasts.filter(\n        (toast, i) => i < toastIndex && toast.visible\n      ).length;\n\n      const offset = relevantToasts\n        .filter((t) => t.visible)\n        .slice(...(reverseOrder ? [toastsBefore + 1] : [0, toastsBefore]))\n        .reduce((acc, t) => acc + (t.height || 0) + gutter, 0);\n\n      return offset;\n    },\n    [toasts]\n  );\n\n  useEffect(() => {\n    // Add dismissed toasts to remove queue\n    toasts.forEach((toast) => {\n      if (toast.dismissed) {\n        addToRemoveQueue(toast.id, toast.removeDelay);\n      } else {\n        // If toast becomes visible again, remove it from the queue\n        const timeout = toastTimeouts.get(toast.id);\n        if (timeout) {\n          clearTimeout(timeout);\n          toastTimeouts.delete(toast.id);\n        }\n      }\n    });\n  }, [toasts]);\n\n  return {\n    toasts,\n    handlers: {\n      updateHeight,\n      startPause,\n      endPause,\n      calculateOffset,\n    },\n  };\n};\n", "import * as React from 'react';\nimport { styled, keyframes } from 'goober';\n\nimport { Toast, ToastPosition, resolveValue, Renderable } from '../core/types';\nimport { ToastIcon } from './toast-icon';\nimport { prefersReducedMotion } from '../core/utils';\n\nconst enterAnimation = (factor: number) => `\n0% {transform: translate3d(0,${factor * -200}%,0) scale(.6); opacity:.5;}\n100% {transform: translate3d(0,0,0) scale(1); opacity:1;}\n`;\n\nconst exitAnimation = (factor: number) => `\n0% {transform: translate3d(0,0,-1px) scale(1); opacity:1;}\n100% {transform: translate3d(0,${factor * -150}%,-1px) scale(.6); opacity:0;}\n`;\n\nconst fadeInAnimation = `0%{opacity:0;} 100%{opacity:1;}`;\nconst fadeOutAnimation = `0%{opacity:1;} 100%{opacity:0;}`;\n\nconst ToastBarBase = styled('div')`\n  display: flex;\n  align-items: center;\n  background: #fff;\n  color: #363636;\n  line-height: 1.3;\n  will-change: transform;\n  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);\n  max-width: 350px;\n  pointer-events: auto;\n  padding: 8px 10px;\n  border-radius: 8px;\n`;\n\nconst Message = styled('div')`\n  display: flex;\n  justify-content: center;\n  margin: 4px 10px;\n  color: inherit;\n  flex: 1 1 auto;\n  white-space: pre-line;\n`;\n\ninterface ToastBarProps {\n  toast: Toast;\n  position?: ToastPosition;\n  style?: React.CSSProperties;\n  children?: (components: {\n    icon: Renderable;\n    message: Renderable;\n  }) => Renderable;\n}\n\nconst getAnimationStyle = (\n  position: ToastPosition,\n  visible: boolean\n): React.CSSProperties => {\n  const top = position.includes('top');\n  const factor = top ? 1 : -1;\n\n  const [enter, exit] = prefersReducedMotion()\n    ? [fadeInAnimation, fadeOutAnimation]\n    : [enterAnimation(factor), exitAnimation(factor)];\n\n  return {\n    animation: visible\n      ? `${keyframes(enter)} 0.35s cubic-bezier(.21,1.02,.73,1) forwards`\n      : `${keyframes(exit)} 0.4s forwards cubic-bezier(.06,.71,.55,1)`,\n  };\n};\n\nexport const ToastBar: React.FC<ToastBarProps> = React.memo(\n  ({ toast, position, style, children }) => {\n    const animationStyle: React.CSSProperties = toast.height\n      ? getAnimationStyle(\n          toast.position || position || 'top-center',\n          toast.visible\n        )\n      : { opacity: 0 };\n\n    const icon = <ToastIcon toast={toast} />;\n    const message = (\n      <Message {...toast.ariaProps}>\n        {resolveValue(toast.message, toast)}\n      </Message>\n    );\n\n    return (\n      <ToastBarBase\n        className={toast.className}\n        style={{\n          ...animationStyle,\n          ...style,\n          ...toast.style,\n        }}\n      >\n        {typeof children === 'function' ? (\n          children({\n            icon,\n            message,\n          })\n        ) : (\n          <>\n            {icon}\n            {message}\n          </>\n        )}\n      </ToastBarBase>\n    );\n  }\n);\n", "import * as React from 'react';\nimport { styled, keyframes } from 'goober';\n\nimport { Toast } from '../core/types';\nimport { ErrorIcon, ErrorTheme } from './error';\nimport { LoaderIcon, LoaderTheme } from './loader';\nimport { CheckmarkIcon, CheckmarkTheme } from './checkmark';\n\nconst StatusWrapper = styled('div')`\n  position: absolute;\n`;\n\nconst IndicatorWrapper = styled('div')`\n  position: relative;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  min-width: 20px;\n  min-height: 20px;\n`;\n\nconst enter = keyframes`\nfrom {\n  transform: scale(0.6);\n  opacity: 0.4;\n}\nto {\n  transform: scale(1);\n  opacity: 1;\n}`;\n\nexport const AnimatedIconWrapper = styled('div')`\n  position: relative;\n  transform: scale(0.6);\n  opacity: 0.4;\n  min-width: 20px;\n  animation: ${enter} 0.3s 0.12s cubic-bezier(0.175, 0.885, 0.32, 1.275)\n    forwards;\n`;\n\nexport type IconThemes = Partial<{\n  success: CheckmarkTheme;\n  error: ErrorTheme;\n  loading: LoaderTheme;\n}>;\n\nexport const ToastIcon: React.FC<{\n  toast: Toast;\n}> = ({ toast }) => {\n  const { icon, type, iconTheme } = toast;\n  if (icon !== undefined) {\n    if (typeof icon === 'string') {\n      return <AnimatedIconWrapper>{icon}</AnimatedIconWrapper>;\n    } else {\n      return icon;\n    }\n  }\n\n  if (type === 'blank') {\n    return null;\n  }\n\n  return (\n    <IndicatorWrapper>\n      <LoaderIcon {...iconTheme} />\n      {type !== 'loading' && (\n        <StatusWrapper>\n          {type === 'error' ? (\n            <ErrorIcon {...iconTheme} />\n          ) : (\n            <CheckmarkIcon {...iconTheme} />\n          )}\n        </StatusWrapper>\n      )}\n    </IndicatorWrapper>\n  );\n};\n", "import { styled, keyframes } from 'goober';\n\nconst circleAnimation = keyframes`\nfrom {\n  transform: scale(0) rotate(45deg);\n\topacity: 0;\n}\nto {\n transform: scale(1) rotate(45deg);\n  opacity: 1;\n}`;\n\nconst firstLineAnimation = keyframes`\nfrom {\n  transform: scale(0);\n  opacity: 0;\n}\nto {\n  transform: scale(1);\n  opacity: 1;\n}`;\n\nconst secondLineAnimation = keyframes`\nfrom {\n  transform: scale(0) rotate(90deg);\n\topacity: 0;\n}\nto {\n  transform: scale(1) rotate(90deg);\n\topacity: 1;\n}`;\n\nexport interface ErrorTheme {\n  primary?: string;\n  secondary?: string;\n}\n\nexport const ErrorIcon = styled('div')<ErrorTheme>`\n  width: 20px;\n  opacity: 0;\n  height: 20px;\n  border-radius: 10px;\n  background: ${(p) => p.primary || '#ff4b4b'};\n  position: relative;\n  transform: rotate(45deg);\n\n  animation: ${circleAnimation} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)\n    forwards;\n  animation-delay: 100ms;\n\n  &:after,\n  &:before {\n    content: '';\n    animation: ${firstLineAnimation} 0.15s ease-out forwards;\n    animation-delay: 150ms;\n    position: absolute;\n    border-radius: 3px;\n    opacity: 0;\n    background: ${(p) => p.secondary || '#fff'};\n    bottom: 9px;\n    left: 4px;\n    height: 2px;\n    width: 12px;\n  }\n\n  &:before {\n    animation: ${secondLineAnimation} 0.15s ease-out forwards;\n    animation-delay: 180ms;\n    transform: rotate(90deg);\n  }\n`;\n", "import { styled, keyframes } from 'goober';\n\nconst rotate = keyframes`\n  from {\n    transform: rotate(0deg);\n  }\n  to {\n    transform: rotate(360deg);\n  }\n`;\n\nexport interface LoaderTheme {\n  primary?: string;\n  secondary?: string;\n}\n\nexport const LoaderIcon = styled('div')<LoaderTheme>`\n  width: 12px;\n  height: 12px;\n  box-sizing: border-box;\n  border: 2px solid;\n  border-radius: 100%;\n  border-color: ${(p) => p.secondary || '#e0e0e0'};\n  border-right-color: ${(p) => p.primary || '#616161'};\n  animation: ${rotate} 1s linear infinite;\n`;\n", "import { styled, keyframes } from 'goober';\n\nconst circleAnimation = keyframes`\nfrom {\n  transform: scale(0) rotate(45deg);\n\topacity: 0;\n}\nto {\n  transform: scale(1) rotate(45deg);\n\topacity: 1;\n}`;\n\nconst checkmarkAnimation = keyframes`\n0% {\n\theight: 0;\n\twidth: 0;\n\topacity: 0;\n}\n40% {\n  height: 0;\n\twidth: 6px;\n\topacity: 1;\n}\n100% {\n  opacity: 1;\n  height: 10px;\n}`;\n\nexport interface CheckmarkTheme {\n  primary?: string;\n  secondary?: string;\n}\n\nexport const CheckmarkIcon = styled('div')<CheckmarkTheme>`\n  width: 20px;\n  opacity: 0;\n  height: 20px;\n  border-radius: 10px;\n  background: ${(p) => p.primary || '#61d345'};\n  position: relative;\n  transform: rotate(45deg);\n\n  animation: ${circleAnimation} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)\n    forwards;\n  animation-delay: 100ms;\n  &:after {\n    content: '';\n    box-sizing: border-box;\n    animation: ${checkmarkAnimation} 0.2s ease-out forwards;\n    opacity: 0;\n    animation-delay: 200ms;\n    position: absolute;\n    border-right: 2px solid;\n    border-bottom: 2px solid;\n    border-color: ${(p) => p.secondary || '#fff'};\n    bottom: 6px;\n    left: 6px;\n    height: 10px;\n    width: 6px;\n  }\n`;\n", "import { css, setup } from 'goober';\nimport * as React from 'react';\nimport {\n  resolveValue,\n  ToasterProps,\n  ToastPosition,\n  ToastWrapperProps,\n} from '../core/types';\nimport { useToaster } from '../core/use-toaster';\nimport { prefersReducedMotion } from '../core/utils';\nimport { ToastBar } from './toast-bar';\n\nsetup(React.createElement);\n\nconst ToastWrapper = ({\n  id,\n  className,\n  style,\n  onHeightUpdate,\n  children,\n}: ToastWrapperProps) => {\n  const ref = React.useCallback(\n    (el: HTMLElement | null) => {\n      if (el) {\n        const updateHeight = () => {\n          const height = el.getBoundingClientRect().height;\n          onHeightUpdate(id, height);\n        };\n        updateHeight();\n        new MutationObserver(updateHeight).observe(el, {\n          subtree: true,\n          childList: true,\n          characterData: true,\n        });\n      }\n    },\n    [id, onHeightUpdate]\n  );\n\n  return (\n    <div ref={ref} className={className} style={style}>\n      {children}\n    </div>\n  );\n};\n\nconst getPositionStyle = (\n  position: ToastPosition,\n  offset: number\n): React.CSSProperties => {\n  const top = position.includes('top');\n  const verticalStyle: React.CSSProperties = top ? { top: 0 } : { bottom: 0 };\n  const horizontalStyle: React.CSSProperties = position.includes('center')\n    ? {\n        justifyContent: 'center',\n      }\n    : position.includes('right')\n    ? {\n        justifyContent: 'flex-end',\n      }\n    : {};\n  return {\n    left: 0,\n    right: 0,\n    display: 'flex',\n    position: 'absolute',\n    transition: prefersReducedMotion()\n      ? undefined\n      : `all 230ms cubic-bezier(.21,1.02,.73,1)`,\n    transform: `translateY(${offset * (top ? 1 : -1)}px)`,\n    ...verticalStyle,\n    ...horizontalStyle,\n  };\n};\n\nconst activeClass = css`\n  z-index: 9999;\n  > * {\n    pointer-events: auto;\n  }\n`;\n\nconst DEFAULT_OFFSET = 16;\n\nexport const Toaster: React.FC<ToasterProps> = ({\n  reverseOrder,\n  position = 'top-center',\n  toastOptions,\n  gutter,\n  children,\n  containerStyle,\n  containerClassName,\n}) => {\n  const { toasts, handlers } = useToaster(toastOptions);\n\n  return (\n    <div\n      id=\"_rht_toaster\"\n      style={{\n        position: 'fixed',\n        zIndex: 9999,\n        top: DEFAULT_OFFSET,\n        left: DEFAULT_OFFSET,\n        right: DEFAULT_OFFSET,\n        bottom: DEFAULT_OFFSET,\n        pointerEvents: 'none',\n        ...containerStyle,\n      }}\n      className={containerClassName}\n      onMouseEnter={handlers.startPause}\n      onMouseLeave={handlers.endPause}\n    >\n      {toasts.map((t) => {\n        const toastPosition = t.position || position;\n        const offset = handlers.calculateOffset(t, {\n          reverseOrder,\n          gutter,\n          defaultPosition: position,\n        });\n        const positionStyle = getPositionStyle(toastPosition, offset);\n\n        return (\n          <ToastWrapper\n            id={t.id}\n            key={t.id}\n            onHeightUpdate={handlers.updateHeight}\n            className={t.visible ? activeClass : ''}\n            style={positionStyle}\n          >\n            {t.type === 'custom' ? (\n              resolveValue(t.message, t)\n            ) : children ? (\n              children(t)\n            ) : (\n              <ToastBar toast={t} position={toastPosition} />\n            )}\n          </ToastWrapper>\n        );\n      })}\n    </div>\n  );\n};\n", "import { toast } from './core/toast';\n\nexport * from './headless';\n\nexport { ToastBar } from './components/toast-bar';\nexport { ToastIcon } from './components/toast-icon';\nexport { Toaster } from './components/toaster';\nexport { CheckmarkIcon } from './components/checkmark';\nexport { ErrorIcon } from './components/error';\nexport { LoaderIcon } from './components/loader';\n\nexport { toast };\nexport default toast;\n"], "mappings": ";;;;;AAuBA,IAAMA,CAAA,GACJC,CAAA,IAEA,OAAOA,CAAA,IAAkB;EAEdC,CAAA,GAAeC,CAC1BF,CAAA,EACAG,CAAA,KACYJ,CAAA,CAAWC,CAAa,IAAIA,CAAA,CAAcG,CAAG,IAAIH,CAAA;AC/BxD,IAAMI,CAAA,IAAS,MAAM;IAC1B,IAAIJ,CAAA,GAAQ;IACZ,OAAO,OACG,EAAEA,CAAA,EAAOK,QAAA,CAAS,CAE9B;EAAA,GAAG;EAEUC,CAAA,IAAwB,MAAM;IAEzC,IAAIN,CAAA;IAEJ,OAAO,MAAM;MACX,IAAIA,CAAA,KAAuB,UAAa,OAAOO,MAAA,GAAW,KAAa;QACrE,IAAMJ,CAAA,GAAaK,UAAA,CAAW,kCAAkC;QAChER,CAAA,GAAqB,CAACG,CAAA,IAAcA,CAAA,CAAWM,OAAA;MAAA;MAEjD,OAAOT,CACT;IAAA,CACF;EAAA,GAAG;AClBH,SAASU,SAAA,IAAAC,CAAA,EAAWC,QAAA,IAAAC,CAAA,EAAUC,MAAA,IAAAC,CAAA,QAAc;AAG5C,IAAMC,CAAA,GAAc;AA+Cb,IAAMC,CAAA,GAAUC,CAAClB,CAAA,EAAcG,CAAA,KAA0B;IAC9D,QAAQA,CAAA,CAAOgB,IAAA;MACb,KAAK;QACH,OAAAC,aAAA,CAAAA,aAAA,KACKpB,CAAA;UACHqB,MAAA,EAAQ,CAAClB,CAAA,CAAOmB,KAAA,EAAO,GAAGtB,CAAA,CAAMqB,MAAM,EAAEE,KAAA,CAAM,GAAGP,CAAW;QAC9D;MAEF,KAAK;QACH,OAAAI,aAAA,CAAAA,aAAA,KACKpB,CAAA;UACHqB,MAAA,EAAQrB,CAAA,CAAMqB,MAAA,CAAOG,GAAA,CAAKC,CAAA,IACxBA,CAAA,CAAEC,EAAA,KAAOvB,CAAA,CAAOmB,KAAA,CAAMI,EAAA,GAAAN,aAAA,CAAAA,aAAA,KAAUK,CAAA,GAAMtB,CAAA,CAAOmB,KAAM,IAAIG,CACzD;QACF;MAEF,KAAK;QACH,IAAM;UAAEH,KAAA,EAAAK;QAAM,IAAIxB,CAAA;QAClB,OAAOc,CAAA,CAAQjB,CAAA,EAAO;UACpBmB,IAAA,EAAMnB,CAAA,CAAMqB,MAAA,CAAOO,IAAA,CAAMH,CAAA,IAAMA,CAAA,CAAEC,EAAA,KAAOC,CAAA,CAAMD,EAAE,IAC5C,IACA;UACJJ,KAAA,EAAAK;QACF,CAAC;MAEH,KAAK;QACH,IAAM;UAAEE,OAAA,EAAAC;QAAQ,IAAI3B,CAAA;QAEpB,OAAAiB,aAAA,CAAAA,aAAA,KACKpB,CAAA;UACHqB,MAAA,EAAQrB,CAAA,CAAMqB,MAAA,CAAOG,GAAA,CAAKC,CAAA,IACxBA,CAAA,CAAEC,EAAA,KAAOI,CAAA,IAAWA,CAAA,KAAY,SAAAV,aAAA,CAAAA,aAAA,KAEvBK,CAAA;YACHM,SAAA,EAAW;YACXC,OAAA,EAAS;UACX,KACAP,CACN;QACF;MACF,KAAK;QACH,OAAItB,CAAA,CAAO0B,OAAA,KAAY,SAAAT,aAAA,CAAAA,aAAA,KAEhBpB,CAAA;UACHqB,MAAA,EAAQ;QACV,KAAAD,aAAA,CAAAA,aAAA,KAGGpB,CAAA;UACHqB,MAAA,EAAQrB,CAAA,CAAMqB,MAAA,CAAOY,MAAA,CAAQR,CAAA,IAAMA,CAAA,CAAEC,EAAA,KAAOvB,CAAA,CAAO0B,OAAO;QAC5D;MAEF,KAAK;QACH,OAAAT,aAAA,CAAAA,aAAA,KACKpB,CAAA;UACHkC,QAAA,EAAU/B,CAAA,CAAOgC;QACnB;MAEF,KAAK;QACH,IAAMC,CAAA,GAAOjC,CAAA,CAAOgC,IAAA,IAAQnC,CAAA,CAAMkC,QAAA,IAAY;QAE9C,OAAAd,aAAA,CAAAA,aAAA,KACKpB,CAAA;UACHkC,QAAA,EAAU;UACVb,MAAA,EAAQrB,CAAA,CAAMqB,MAAA,CAAOG,GAAA,CAAKC,CAAA,IAAAL,aAAA,CAAAA,aAAA,KACrBK,CAAA;YACHY,aAAA,EAAeZ,CAAA,CAAEY,aAAA,GAAgBD;UACnC,EAAE;QACJ;IACJ;EACF;EAEME,CAAA,GAA2C,EAAC;EAE9CC,CAAA,GAAqB;IAAElB,MAAA,EAAQ,EAAC;IAAGa,QAAA,EAAU;EAAU;EAE9CM,CAAA,GAAYxC,CAAA,IAAmB;IAC1CuC,CAAA,GAActB,CAAA,CAAQsB,CAAA,EAAavC,CAAM,GACzCsC,CAAA,CAAUG,OAAA,CAAStC,CAAA,IAAa;MAC9BA,CAAA,CAASoC,CAAW,CACtB;IAAA,CAAC,CACH;EAAA;EAEaG,CAAA,GAET;IACFC,KAAA,EAAO;IACPC,KAAA,EAAO;IACPC,OAAA,EAAS;IACTC,OAAA,EAAS;IACTC,MAAA,EAAQ;EACV;EAEaC,CAAA,GAAW,SAAAC,CAAA,EAAmD;IAAA,IAAlDjD,CAAA,GAAAkD,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAoC,CAAC;IAC5D,IAAM,CAAC/C,CAAA,EAAOwB,CAAQ,IAAId,CAAA,CAAgB0B,CAAW;MAC/CT,CAAA,GAAUf,CAAA,CAAOwB,CAAW;IAGlC5B,CAAA,CAAU,OACJmB,CAAA,CAAQuB,OAAA,KAAYd,CAAA,IACtBZ,CAAA,CAASY,CAAW,GAEtBD,CAAA,CAAUgB,IAAA,CAAK3B,CAAQ,GAChB,MAAM;MACX,IAAMF,CAAA,GAAQa,CAAA,CAAUiB,OAAA,CAAQ5B,CAAQ;MACpCF,CAAA,GAAQ,MACVa,CAAA,CAAUkB,MAAA,CAAO/B,CAAA,EAAO,CAAC,CAE7B;IAAA,IACC,EAAE;IAEL,IAAMW,CAAA,GAAejC,CAAA,CAAMkB,MAAA,CAAOG,GAAA,CAAKC,CAAA,IAAG;MAjK5C,IAAAgC,CAAA,EAAAC,CAAA,EAAAC,CAAA;MAiKgD,OAAAvC,aAAA,CAAAA,aAAA,CAAAA,aAAA,CAAAA,aAAA,KACzCpB,CAAA,GACAA,CAAA,CAAayB,CAAA,CAAEN,IAAI,IACnBM,CAAA;QACHmC,WAAA,EACEnC,CAAA,CAAEmC,WAAA,MACFH,CAAA,GAAAzD,CAAA,CAAayB,CAAA,CAAEN,IAAI,MAAnB,gBAAAsC,CAAA,CAAsBG,WAAA,MACtB5D,CAAA,oBAAAA,CAAA,CAAc4D,WAAA;QAChBC,QAAA,EACEpC,CAAA,CAAEoC,QAAA,MACFH,CAAA,GAAA1D,CAAA,CAAayB,CAAA,CAAEN,IAAI,MAAnB,gBAAAuC,CAAA,CAAsBG,QAAA,MACtB7D,CAAA,oBAAAA,CAAA,CAAc6D,QAAA,KACdnB,CAAA,CAAgBjB,CAAA,CAAEN,IAAI;QACxB2C,KAAA,EAAA1C,aAAA,CAAAA,aAAA,CAAAA,aAAA,KACKpB,CAAA,CAAa8D,KAAA,GAChB,CAAGH,CAAA,GAAA3D,CAAA,CAAayB,CAAA,CAAEN,IAAI,MAAnB,gBAAAwC,CAAA,CAAsBG,KAAA,GACtBrC,CAAA,CAAEqC,KACP;MACF;IAAA,CAAE;IAEF,OAAA1C,aAAA,CAAAA,aAAA,KACKjB,CAAA;MACHkB,MAAA,EAAQe;IACV;EACF;ACzKA,IAAM2B,CAAA,GAAc,SAAAC,CAClBhE,CAAA;IAAA,IACAG,CAAA,GAAA+C,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAkB;IAAA,IAClBvB,CAAA,GAAAuB,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;IAAA,OAAAhC,aAAA,CAAAA,aAAA;MAEA6C,SAAA,EAAWC,IAAA,CAAKC,GAAA,CAAI;MACpBnC,OAAA,EAAS;MACTD,SAAA,EAAW;MACXZ,IAAA,EAAAhB,CAAA;MACAiE,SAAA,EAAW;QACTC,IAAA,EAAM;QACN,aAAa;MACf;MACAC,OAAA,EAAAtE,CAAA;MACAqC,aAAA,EAAe;IAAA,GACZV,CAAA;MACHD,EAAA,GAAIC,CAAA,oBAAAA,CAAA,CAAMD,EAAA,KAAMtB,CAAA,CAAM;IACxB;EAAA;EAEMmE,CAAA,GACHvE,CAAA,IACD,CAACG,CAAA,EAASwB,CAAA,KAAY;IACpB,IAAMG,CAAA,GAAQiC,CAAA,CAAY5D,CAAA,EAASH,CAAA,EAAM2B,CAAO;IAChD,OAAAa,CAAA,CAAS;MAAErB,IAAA;MAA+BG,KAAA,EAAAQ;IAAM,CAAC,GAC1CA,CAAA,CAAMJ,EACf;EAAA;EAEI8C,CAAA,GAAQlD,CAACtB,CAAA,EAAkBG,CAAA,KAC/BoE,CAAA,CAAc,OAAO,EAAEvE,CAAA,EAASG,CAAI;AAEtCqE,CAAA,CAAM5B,KAAA,GAAQ2B,CAAA,CAAc,OAAO;AACnCC,CAAA,CAAM3B,OAAA,GAAU0B,CAAA,CAAc,SAAS;AACvCC,CAAA,CAAM1B,OAAA,GAAUyB,CAAA,CAAc,SAAS;AACvCC,CAAA,CAAMzB,MAAA,GAASwB,CAAA,CAAc,QAAQ;AAErCC,CAAA,CAAMC,OAAA,GAAWzE,CAAA,IAAqB;EACpCwC,CAAA,CAAS;IACPrB,IAAA;IACAU,OAAA,EAAA7B;EACF,CAAC,CACH;AAAA;AAEAwE,CAAA,CAAME,MAAA,GAAU1E,CAAA,IACdwC,CAAA,CAAS;EAAErB,IAAA;EAA+BU,OAAA,EAAA7B;AAAQ,CAAC;AAErDwE,CAAA,CAAMG,OAAA,GAAU,CACd3E,CAAA,EACAG,CAAA,EAKAwB,CAAA,KACG;EACH,IAAMG,CAAA,GAAK0C,CAAA,CAAM1B,OAAA,CAAQ3C,CAAA,CAAK2C,OAAA,EAAA1B,aAAA,CAAAA,aAAA,KAAcO,CAAA,GAASA,CAAA,oBAAAA,CAAA,CAAMmB,OAAQ,CAAC;EAEpE,OAAI,OAAO9C,CAAA,IAAY,eACrBA,CAAA,GAAUA,CAAA,CAAQ,IAGpBA,CAAA,CACG4E,IAAA,CAAMxC,CAAA,IAAM;IACX,IAAMX,CAAA,GAAiBtB,CAAA,CAAK0C,OAAA,GACxB5C,CAAA,CAAaE,CAAA,CAAK0C,OAAA,EAAST,CAAC,IAC5B;IAEJ,OAAIX,CAAA,GACF+C,CAAA,CAAM3B,OAAA,CAAQpB,CAAA,EAAAL,aAAA,CAAAA,aAAA;MACZM,EAAA,EAAAI;IAAA,GACGH,CAAA,GACAA,CAAA,oBAAAA,CAAA,CAAMkB,OACX,CAAC,IAED2B,CAAA,CAAMC,OAAA,CAAQ3C,CAAE,GAEXM,CACT;EAAA,CAAC,EACAyC,KAAA,CAAOzC,CAAA,IAAM;IACZ,IAAMX,CAAA,GAAetB,CAAA,CAAKyC,KAAA,GAAQ3C,CAAA,CAAaE,CAAA,CAAKyC,KAAA,EAAOR,CAAC,IAAI;IAE5DX,CAAA,GACF+C,CAAA,CAAM5B,KAAA,CAAMnB,CAAA,EAAAL,aAAA,CAAAA,aAAA;MACVM,EAAA,EAAAI;IAAA,GACGH,CAAA,GACAA,CAAA,oBAAAA,CAAA,CAAMiB,KACX,CAAC,IAED4B,CAAA,CAAMC,OAAA,CAAQ3C,CAAE,CAEpB;EAAA,CAAC,GAEI9B,CACT;AAAA;AC5GA,SAASU,SAAA,IAAAoE,CAAA,EAAWC,WAAA,IAAAC,CAAA,QAAmB;AAKvC,IAAMC,CAAA,GAAeC,CAAClF,CAAA,EAAiBG,CAAA,KAAmB;IACxDqC,CAAA,CAAS;MACPrB,IAAA;MACAG,KAAA,EAAO;QAAEI,EAAA,EAAI1B,CAAA;QAASmF,MAAA,EAAAhF;MAAO;IAC/B,CAAC,CACH;EAAA;EACMiF,CAAA,GAAaC,CAAA,KAAM;IACvB7C,CAAA,CAAS;MACPrB,IAAA;MACAgB,IAAA,EAAM+B,IAAA,CAAKC,GAAA,CAAI;IACjB,CAAC,CACH;EAAA;EAEMmB,CAAA,GAAgB,IAAIC,GAAA;EAEbC,CAAA,GAAe;EAEtBC,EAAA,GAAmB,SAAAC,CAAC1F,CAAA,EAAgD;IAAA,IAA/BG,CAAA,GAAA+C,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAcsC,CAAA;IACvD,IAAIF,CAAA,CAAcK,GAAA,CAAI3F,CAAO,GAC3B;IAGF,IAAM2B,CAAA,GAAUiE,UAAA,CAAW,MAAM;MAC/BN,CAAA,CAAcO,MAAA,CAAO7F,CAAO,GAC5BwC,CAAA,CAAS;QACPrB,IAAA;QACAU,OAAA,EAAS7B;MACX,CAAC,CACH;IAAA,GAAGG,CAAW;IAEdmF,CAAA,CAAcQ,GAAA,CAAI9F,CAAA,EAAS2B,CAAO,CACpC;EAAA;EAEaoE,CAAA,GAAc/F,CAAA,IAAuC;IAChE,IAAM;MAAEqB,MAAA,EAAAlB,CAAA;MAAQ+B,QAAA,EAAAP;IAAS,IAAIqB,CAAA,CAAShD,CAAY;IAElD8E,CAAA,CAAU,MAAM;MACd,IAAInD,CAAA,EACF;MAGF,IAAMF,CAAA,GAAMyC,IAAA,CAAKC,GAAA,CAAI;QACfV,CAAA,GAAWtD,CAAA,CAAOqB,GAAA,CAAKkC,CAAA,IAAM;UACjC,IAAIA,CAAA,CAAEG,QAAA,KAAa,OACjB;UAGF,IAAMF,CAAA,IACHD,CAAA,CAAEG,QAAA,IAAY,KAAKH,CAAA,CAAErB,aAAA,IAAiBZ,CAAA,GAAMiC,CAAA,CAAEO,SAAA;UAEjD,IAAIN,CAAA,GAAe,GAAG;YAChBD,CAAA,CAAE1B,OAAA,IACJwC,CAAA,CAAMC,OAAA,CAAQf,CAAA,CAAEhC,EAAE;YAEpB;UAAA;UAEF,OAAOkE,UAAA,CAAW,MAAMpB,CAAA,CAAMC,OAAA,CAAQf,CAAA,CAAEhC,EAAE,GAAGiC,CAAY,CAC3D;QAAA,CAAC;MAED,OAAO,MAAM;QACXF,CAAA,CAAShB,OAAA,CAASiB,CAAA,IAAYA,CAAA,IAAWsC,YAAA,CAAatC,CAAO,CAAC,CAChE;MAAA,CACF;IAAA,GAAG,CAACvD,CAAA,EAAQwB,CAAQ,CAAC;IAErB,IAAMG,CAAA,GAAWkD,CAAA,CAAY,MAAM;QAC7BrD,CAAA,IACFa,CAAA,CAAS;UAAErB,IAAA;UAA4BgB,IAAA,EAAM+B,IAAA,CAAKC,GAAA,CAAI;QAAE,CAAC,CAE7D;MAAA,GAAG,CAACxC,CAAQ,CAAC;MAEPS,CAAA,GAAkB4C,CAAA,CACtB,CACEvD,CAAA,EACAgC,CAAA,KAKG;QACH,IAAM;YAAEwC,YAAA,EAAAvC,CAAA,GAAe;YAAOwC,MAAA,EAAAvC,CAAA,GAAS;YAAGwC,eAAA,EAAAC;UAAgB,IAAI3C,CAAA,IAAQ,CAAC;UAEjE4C,CAAA,GAAiBlG,CAAA,CAAO8B,MAAA,CAC3BqE,CAAA,KACEA,CAAA,CAAEC,QAAA,IAAYH,CAAA,OACZ3E,CAAA,CAAM8E,QAAA,IAAYH,CAAA,KAAoBE,CAAA,CAAEnB,MAC/C;UACMqB,CAAA,GAAaH,CAAA,CAAeI,SAAA,CAAWH,CAAA,IAAMA,CAAA,CAAE5E,EAAA,KAAOD,CAAA,CAAMC,EAAE;UAC9DgF,CAAA,GAAeL,CAAA,CAAepE,MAAA,CAClC,CAACqE,CAAA,EAAOK,CAAA,KAAMA,CAAA,GAAIH,CAAA,IAAcF,CAAA,CAAMtE,OACxC,EAAEmB,MAAA;QAOF,OALekD,CAAA,CACZpE,MAAA,CAAQqE,CAAA,IAAMA,CAAA,CAAEtE,OAAO,EACvBT,KAAA,CAAM,IAAImC,CAAA,GAAe,CAACgD,CAAA,GAAe,CAAC,IAAI,CAAC,GAAGA,CAAY,CAAE,GAChEE,MAAA,CAAO,CAACN,CAAA,EAAKK,CAAA,KAAML,CAAA,IAAOK,CAAA,CAAExB,MAAA,IAAU,KAAKxB,CAAA,EAAQ,CAAC,CAGzD;MAAA,GACA,CAACxD,CAAM,CACT;IAEA,OAAA2E,CAAA,CAAU,MAAM;MAEd3E,CAAA,CAAOsC,OAAA,CAAShB,CAAA,IAAU;QACxB,IAAIA,CAAA,CAAMM,SAAA,EACR0D,EAAA,CAAiBhE,CAAA,CAAMC,EAAA,EAAID,CAAA,CAAMmC,WAAW,OACvC;UAEL,IAAMH,CAAA,GAAU6B,CAAA,CAAcuB,GAAA,CAAIpF,CAAA,CAAMC,EAAE;UACtC+B,CAAA,KACFuC,YAAA,CAAavC,CAAO,GACpB6B,CAAA,CAAcO,MAAA,CAAOpE,CAAA,CAAMC,EAAE;QAAA;MAGnC,CAAC,CACH;IAAA,GAAG,CAACvB,CAAM,CAAC,GAEJ;MACLkB,MAAA,EAAAlB,CAAA;MACA2G,QAAA,EAAU;QACR5B,YAAA,EAAAD,CAAA;QACAI,UAAA,EAAAD,CAAA;QACA2B,QAAA,EAAAjF,CAAA;QACAkF,eAAA,EAAA5E;MACF;IACF,CACF;EAAA;ACnIA,YAAY6E,CAAA,MAAW;AACvB,SAASC,MAAA,IAAAC,CAAA,EAAQC,SAAA,IAAAC,CAAA,QAAiB;ACDlC,YAAYC,CAAA,MAAW;AACvB,SAASJ,MAAA,IAAAK,CAAA,EAAQH,SAAA,IAAAI,EAAA,QAAiB;ACDlC,SAASN,MAAA,IAAAO,EAAA,EAAQL,SAAA,IAAAM,CAAA,QAAiB;AAElC,IAAMC,EAAA,GAAkBD,CAAA,CAAAE,eAAA,KAAAA,eAAA,GAAAC,sBAAA;EAUlBC,EAAA,GAAqBJ,CAAA,CAAAK,gBAAA,KAAAA,gBAAA,GAAAF,sBAAA;EAUrBG,EAAA,GAAsBN,CAAA,CAAAO,gBAAA,KAAAA,gBAAA,GAAAJ,sBAAA;EAefK,CAAA,GAAYT,EAAA,CAAO,KAAK,EAAAU,gBAAA,KAAAA,gBAAA,GAAAN,sBAAA,qqBAKpB7H,CAAA,IAAMA,CAAA,CAAEoI,OAAA,IAAW,WAIrBT,EAAA,EAOEG,EAAA,EAKE9H,CAAA,IAAMA,CAAA,CAAEqI,SAAA,IAAa,QAQvBL,EAAA,C;AClEjB,SAASd,MAAA,IAAAoB,EAAA,EAAQlB,SAAA,IAAAmB,EAAA,QAAiB;AAElC,IAAMC,EAAA,GAASD,EAAA,CAAAE,gBAAA,KAAAA,gBAAA,GAAAZ,sBAAA;EAcFa,CAAA,GAAaJ,EAAA,CAAO,KAAK,EAAAK,gBAAA,KAAAA,gBAAA,GAAAd,sBAAA,gNAMnB7H,CAAA,IAAMA,CAAA,CAAEqI,SAAA,IAAa,WACfrI,CAAA,IAAMA,CAAA,CAAEoI,OAAA,IAAW,WAC7BI,EAAA,C;ACxBf,SAAStB,MAAA,IAAA0B,EAAA,EAAQxB,SAAA,IAAAyB,CAAA,QAAiB;AAElC,IAAMC,EAAA,GAAkBD,CAAA,CAAAE,gBAAA,KAAAA,gBAAA,GAAAlB,sBAAA;EAUlBmB,EAAA,GAAqBH,CAAA,CAAAI,gBAAA,KAAAA,gBAAA,GAAApB,sBAAA;EAqBdqB,CAAA,GAAgBN,EAAA,CAAO,KAAK,EAAAO,gBAAA,KAAAA,gBAAA,GAAAtB,sBAAA,ylBAKxB7H,CAAA,IAAMA,CAAA,CAAEoI,OAAA,IAAW,WAIrBU,EAAA,EAMEE,EAAA,EAMIhJ,CAAA,IAAMA,CAAA,CAAEqI,SAAA,IAAa,O;AH9C1C,IAAMe,EAAA,GAAgB7B,CAAA,CAAO,KAAK,EAAA8B,gBAAA,KAAAA,gBAAA,GAAAxB,sBAAA;EAI5ByB,EAAA,GAAmB/B,CAAA,CAAO,KAAK,EAAAgC,gBAAA,KAAAA,gBAAA,GAAA1B,sBAAA;EAS/B2B,EAAA,GAAQhC,EAAA,CAAAiC,iBAAA,KAAAA,iBAAA,GAAA5B,sBAAA;EAUD6B,EAAA,GAAsBnC,CAAA,CAAO,KAAK,EAAAoC,iBAAA,KAAAA,iBAAA,GAAA9B,sBAAA,sLAKhC2B,EAAA;EAUFI,CAAA,GAERC,IAAA,IAAe;IAAA,IAAd;MAAEvI,KAAA,EAAAtB;IAAM,IAAA6J,IAAA;IACZ,IAAM;MAAEC,IAAA,EAAA3J,CAAA;MAAMgB,IAAA,EAAAQ,CAAA;MAAMoI,SAAA,EAAAjI;IAAU,IAAI9B,CAAA;IAClC,OAAIG,CAAA,KAAS,SACP,OAAOA,CAAA,IAAS,WACXmH,CAAA,CAAA0C,aAAA,CAACN,EAAA,QAAqBvJ,CAAK,IAE3BA,CAAA,GAIPwB,CAAA,KAAS,UACJ,OAIP2F,CAAA,CAAA0C,aAAA,CAACV,EAAA,QACChC,CAAA,CAAA0C,aAAA,CAACtB,CAAA,EAAAtH,aAAA,KAAeU,CAAA,CAAW,GAC1BH,CAAA,KAAS,aACR2F,CAAA,CAAA0C,aAAA,CAACZ,EAAA,QACEzH,CAAA,KAAS,UACR2F,CAAA,CAAA0C,aAAA,CAAC9B,CAAA,EAAA9G,aAAA,KAAcU,CAAA,CAAW,IAE1BwF,CAAA,CAAA0C,aAAA,CAACd,CAAA,EAAA9H,aAAA,KAAkBU,CAAA,CAAW,CAElC,CAEJ,CAEJ;EAAA;ADrEA,IAAMmI,EAAA,GAAkBjK,CAAA,sCAAAkK,MAAA,CACOlK,CAAA,GAAS;EAIlCmK,EAAA,GAAiBnK,CAAA,oGAAAkK,MAAA,CAEUlK,CAAA,GAAS;EAGpCoK,EAAA,GAAkB;EAClBC,EAAA,GAAmB;EAEnBC,EAAA,GAAenD,CAAA,CAAO,KAAK,EAAAoD,iBAAA,KAAAA,iBAAA,GAAA1C,sBAAA;EAc3B2C,EAAA,GAAUrD,CAAA,CAAO,KAAK,EAAAsD,iBAAA,KAAAA,iBAAA,GAAA5C,sBAAA;EAmBtB6C,EAAA,GAAoBC,CACxB3K,CAAA,EACAG,CAAA,KACwB;IAExB,IAAM2B,CAAA,GADM9B,CAAA,CAAS4K,QAAA,CAAS,KAAK,IACd,IAAI;MAEnB,CAACxI,CAAA,EAAOX,CAAI,IAAInB,CAAA,CAAqB,IACvC,CAAC8J,EAAA,EAAiBC,EAAgB,IAClC,CAACJ,EAAA,CAAenI,CAAM,GAAGqI,EAAA,CAAcrI,CAAM,CAAC;IAElD,OAAO;MACL+I,SAAA,EAAW1K,CAAA,MAAA+J,MAAA,CACJ7C,CAAA,CAAUjF,CAAK,wDAAA8H,MAAA,CACf7C,CAAA,CAAU5F,CAAI;IACvB,CACF;EAAA;EAEaqJ,CAAA,GAA0C7D,CAAA,CAAA8D,IAAA,CACrDC,KAAA,IAA0C;IAAA,IAAzC;MAAE1J,KAAA,EAAAtB,CAAA;MAAOuG,QAAA,EAAApG,CAAA;MAAU2D,KAAA,EAAAnC,CAAA;MAAOsJ,QAAA,EAAAnJ;IAAS,IAAAkJ,KAAA;IAClC,IAAM5I,CAAA,GAAsCpC,CAAA,CAAMmF,MAAA,GAC9CuF,EAAA,CACE1K,CAAA,CAAMuG,QAAA,IAAYpG,CAAA,IAAY,cAC9BH,CAAA,CAAMgC,OACR,IACA;QAAEkJ,OAAA,EAAS;MAAE;MAEXzJ,CAAA,GAAOwF,CAAA,CAAA+C,aAAA,CAACJ,CAAA;QAAUtI,KAAA,EAAOtB;MAAA,CAAO;MAChCyD,CAAA,GACJwD,CAAA,CAAA+C,aAAA,CAACQ,EAAA,EAAApJ,aAAA,KAAYpB,CAAA,CAAMoE,SAAA,GAChBnE,CAAA,CAAaD,CAAA,CAAMsE,OAAA,EAAStE,CAAK,CACpC;IAGF,OACEiH,CAAA,CAAA+C,aAAA,CAACM,EAAA;MACCa,SAAA,EAAWnL,CAAA,CAAMmL,SAAA;MACjBrH,KAAA,EAAA1C,aAAA,CAAAA,aAAA,CAAAA,aAAA,KACKgB,CAAA,GACAT,CAAA,GACA3B,CAAA,CAAM8D,KACX;IAAA,GAEC,OAAOhC,CAAA,IAAa,aACnBA,CAAA,CAAS;MACPgI,IAAA,EAAArI,CAAA;MACA6C,OAAA,EAAAb;IACF,CAAC,IAEDwD,CAAA,CAAA+C,aAAA,CAAA/C,CAAA,CAAAmE,QAAA,QACG3J,CAAA,EACAgC,CACH,CAEJ,CAEJ;EAAA,CACF;AK9GA,SAAS4H,GAAA,IAAAC,EAAA,EAAKC,KAAA,IAAAC,EAAA,QAAa;AAC3B,YAAYC,CAAA,MAAW;AAWvBD,EAAA,CAAYC,CAAA,CAAAzB,aAAa;AAEzB,IAAM0B,EAAA,GAAeC,KAAA,IAMI;IAAA,IANH;MACpBjK,EAAA,EAAA1B,CAAA;MACAmL,SAAA,EAAAhL,CAAA;MACA2D,KAAA,EAAAnC,CAAA;MACAiK,cAAA,EAAA9J,CAAA;MACAmJ,QAAA,EAAA7I;IACF,IAAAuJ,KAAA;IACE,IAAMlK,CAAA,GAAYgK,CAAA,CAAA1G,WAAA,CACftB,CAAA,IAA2B;MAC1B,IAAIA,CAAA,EAAI;QACN,IAAMC,CAAA,GAAewB,CAAA,KAAM;UACzB,IAAMvB,CAAA,GAASF,CAAA,CAAGoI,qBAAA,CAAsB,EAAE1G,MAAA;UAC1CrD,CAAA,CAAe9B,CAAA,EAAI2D,CAAM,CAC3B;QAAA;QACAD,CAAA,CAAa,GACb,IAAIoI,gBAAA,CAAiBpI,CAAY,EAAEqI,OAAA,CAAQtI,CAAA,EAAI;UAC7CuI,OAAA,EAAS;UACTC,SAAA,EAAW;UACXC,aAAA,EAAe;QACjB,CAAC;MAAA;IAEL,GACA,CAAClM,CAAA,EAAI8B,CAAc,CACrB;IAEA,OACE2J,CAAA,CAAAzB,aAAA,CAAC;MAAImC,GAAA,EAAK1K,CAAA;MAAK0J,SAAA,EAAWhL,CAAA;MAAW2D,KAAA,EAAOnC;IAAA,GACzCS,CACH,CAEJ;EAAA;EAEMgK,EAAA,GAAmBC,CACvBrM,CAAA,EACAG,CAAA,KACwB;IACxB,IAAMwB,CAAA,GAAM3B,CAAA,CAAS4K,QAAA,CAAS,KAAK;MAC7B9I,CAAA,GAAqCH,CAAA,GAAM;QAAE2K,GAAA,EAAK;MAAE,IAAI;QAAEC,MAAA,EAAQ;MAAE;MACpEnK,CAAA,GAAuCpC,CAAA,CAAS4K,QAAA,CAAS,QAAQ,IACnE;QACE4B,cAAA,EAAgB;MAClB,IACAxM,CAAA,CAAS4K,QAAA,CAAS,OAAO,IACzB;QACE4B,cAAA,EAAgB;MAClB,IACA,CAAC;IACL,OAAApL,aAAA,CAAAA,aAAA;MACEqL,IAAA,EAAM;MACNC,KAAA,EAAO;MACPC,OAAA,EAAS;MACTpG,QAAA,EAAU;MACVqG,UAAA,EAAYtM,CAAA,CAAqB,IAC7B,SACA;MACJuM,SAAA,gBAAA3C,MAAA,CAAyB/J,CAAA,IAAUwB,CAAA,GAAM,IAAI;IAAA,GAC1CG,CAAA,GACAM,CACL;EACF;EAEM0K,EAAA,GAAcxB,EAAA,CAAAyB,iBAAA,KAAAA,iBAAA,GAAAlF,sBAAA;EAOdmF,CAAA,GAAiB;EAEVC,EAAA,GAAkCC,KAAA,IAQzC;IAAA,IAR0C;MAC9CjH,YAAA,EAAAjG,CAAA;MACAuG,QAAA,EAAApG,CAAA,GAAW;MACXgN,YAAA,EAAAxL,CAAA;MACAuE,MAAA,EAAApE,CAAA;MACAmJ,QAAA,EAAA7I,CAAA;MACAgL,cAAA,EAAA3L,CAAA;MACA4L,kBAAA,EAAA5J;IACF,IAAAyJ,KAAA;IACE,IAAM;MAAE7L,MAAA,EAAAqC,CAAA;MAAQoD,QAAA,EAAAnD;IAAS,IAAIoC,CAAA,CAAWpE,CAAY;IAEpD,OACE8J,CAAA,CAAAzB,aAAA,CAAC;MACCtI,EAAA,EAAG;MACHoC,KAAA,EAAA1C,aAAA;QACEmF,QAAA,EAAU;QACV+G,MAAA,EAAQ;QACRhB,GAAA,EAAKU,CAAA;QACLP,IAAA,EAAMO,CAAA;QACNN,KAAA,EAAOM,CAAA;QACPT,MAAA,EAAQS,CAAA;QACRO,aAAA,EAAe;MAAA,GACZ9L,CACL;MACA0J,SAAA,EAAW1H,CAAA;MACX+J,YAAA,EAAc7J,CAAA,CAAS0B,UAAA;MACvBoI,YAAA,EAAc9J,CAAA,CAASoD;IAAA,GAEtBrD,CAAA,CAAOlC,GAAA,CAAK4E,CAAA,IAAM;MACjB,IAAMC,CAAA,GAAgBD,CAAA,CAAEG,QAAA,IAAYpG,CAAA;QAC9BqG,CAAA,GAAS7C,CAAA,CAASqD,eAAA,CAAgBZ,CAAA,EAAG;UACzCH,YAAA,EAAAjG,CAAA;UACAkG,MAAA,EAAApE,CAAA;UACAqE,eAAA,EAAiBhG;QACnB,CAAC;QACKuG,CAAA,GAAgB0F,EAAA,CAAiB/F,CAAA,EAAeG,CAAM;MAE5D,OACEiF,CAAA,CAAAzB,aAAA,CAAC0B,EAAA;QACChK,EAAA,EAAI0E,CAAA,CAAE1E,EAAA;QACNgM,GAAA,EAAKtH,CAAA,CAAE1E,EAAA;QACPkK,cAAA,EAAgBjI,CAAA,CAASuB,YAAA;QACzBiG,SAAA,EAAW/E,CAAA,CAAEpE,OAAA,GAAU8K,EAAA,GAAc;QACrChJ,KAAA,EAAO4C;MAAA,GAENN,CAAA,CAAEjF,IAAA,KAAS,WACVlB,CAAA,CAAamG,CAAA,CAAE9B,OAAA,EAAS8B,CAAC,IACvBhE,CAAA,GACFA,CAAA,CAASgE,CAAC,IAEVqF,CAAA,CAAAzB,aAAA,CAACc,CAAA;QAASxJ,KAAA,EAAO8E,CAAA;QAAGG,QAAA,EAAUF;MAAA,CAAe,CAEjD,CAEJ;IAAA,CAAC,CACH,CAEJ;EAAA;ACjIA,IAAOsH,EAAA,GAAQnJ,CAAA;AAAA,SAAA0E,CAAA,IAAA0E,aAAA,EAAA1F,CAAA,IAAA2F,SAAA,EAAAnF,CAAA,IAAAoF,UAAA,EAAAhD,CAAA,IAAAiD,QAAA,EAAAnE,CAAA,IAAAoE,SAAA,EAAAf,EAAA,IAAAgB,OAAA,EAAAN,EAAA,IAAAO,OAAA,EAAAjO,CAAA,IAAAC,YAAA,EAAAsE,CAAA,IAAAlD,KAAA,EAAAyE,CAAA,IAAAoI,UAAA,EAAAnL,CAAA,IAAAoL,eAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}