{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst Construction = createLucideIcon(\"Construction\", [[\"rect\", {\n  x: \"2\",\n  y: \"6\",\n  width: \"20\",\n  height: \"8\",\n  rx: \"1\",\n  key: \"1estib\"\n}], [\"path\", {\n  d: \"M17 14v7\",\n  key: \"7m2elx\"\n}], [\"path\", {\n  d: \"M7 14v7\",\n  key: \"1cm7wv\"\n}], [\"path\", {\n  d: \"M17 3v3\",\n  key: \"1v4jwn\"\n}], [\"path\", {\n  d: \"M7 3v3\",\n  key: \"7o6guu\"\n}], [\"path\", {\n  d: \"M10 14 2.3 6.3\",\n  key: \"1023jk\"\n}], [\"path\", {\n  d: \"m14 6 7.7 7.7\",\n  key: \"1s8pl2\"\n}], [\"path\", {\n  d: \"m8 6 8 8\",\n  key: \"hl96qh\"\n}]]);\nexport { Construction as default };", "map": {"version": 3, "names": ["Construction", "createLucideIcon", "x", "y", "width", "height", "rx", "key", "d"], "sources": ["D:\\repos-personal\\repos\\openapi-to-mcp\\ui\\node_modules\\lucide-react\\src\\icons\\construction.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Construction\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB4PSIyIiB5PSI2IiB3aWR0aD0iMjAiIGhlaWdodD0iOCIgcng9IjEiIC8+CiAgPHBhdGggZD0iTTE3IDE0djciIC8+CiAgPHBhdGggZD0iTTcgMTR2NyIgLz4KICA8cGF0aCBkPSJNMTcgM3YzIiAvPgogIDxwYXRoIGQ9Ik03IDN2MyIgLz4KICA8cGF0aCBkPSJNMTAgMTQgMi4zIDYuMyIgLz4KICA8cGF0aCBkPSJtMTQgNiA3LjcgNy43IiAvPgogIDxwYXRoIGQ9Im04IDYgOCA4IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/construction\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Construction = createLucideIcon('Construction', [\n  [\n    'rect',\n    { x: '2', y: '6', width: '20', height: '8', rx: '1', key: '1estib' },\n  ],\n  ['path', { d: 'M17 14v7', key: '7m2elx' }],\n  ['path', { d: 'M7 14v7', key: '1cm7wv' }],\n  ['path', { d: 'M17 3v3', key: '1v4jwn' }],\n  ['path', { d: 'M7 3v3', key: '7o6guu' }],\n  ['path', { d: 'M10 14 2.3 6.3', key: '1023jk' }],\n  ['path', { d: 'm14 6 7.7 7.7', key: '1s8pl2' }],\n  ['path', { d: 'm8 6 8 8', key: 'hl96qh' }],\n]);\n\nexport default Construction;\n"], "mappings": ";;;;;AAaM,MAAAA,YAAA,GAAeC,gBAAA,CAAiB,cAAgB,GACpD,CACE,QACA;EAAEC,CAAA,EAAG,GAAK;EAAAC,CAAA,EAAG,GAAK;EAAAC,KAAA,EAAO,IAAM;EAAAC,MAAA,EAAQ,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,GAAA,EAAK;AAAS,EACrE,EACA,CAAC,MAAQ;EAAEC,CAAA,EAAG,UAAY;EAAAD,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAEC,CAAA,EAAG,SAAW;EAAAD,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAEC,CAAA,EAAG,SAAW;EAAAD,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAEC,CAAA,EAAG,QAAU;EAAAD,GAAA,EAAK;AAAA,CAAU,GACvC,CAAC,MAAQ;EAAEC,CAAA,EAAG,gBAAkB;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC/C,CAAC,MAAQ;EAAEC,CAAA,EAAG,eAAiB;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC9C,CAAC,MAAQ;EAAEC,CAAA,EAAG,UAAY;EAAAD,GAAA,EAAK;AAAA,CAAU,EAC1C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}