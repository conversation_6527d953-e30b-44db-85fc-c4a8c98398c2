{"ast": null, "code": "import _objectSpread from\"D:/repos-personal/repos/openapi-to-mcp/ui/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import _objectWithoutProperties from\"D:/repos-personal/repos/openapi-to-mcp/ui/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\";const _excluded=[\"className\",\"variant\",\"size\",\"loading\",\"icon\",\"children\",\"disabled\",\"onClick\",\"type\"];/**\r\n * Reusable Button component\r\n */import React from'react';import{cn}from'../../utils';export const Button=_ref=>{let{className,variant='primary',size='md',loading=false,icon,children,disabled,onClick,type='button'}=_ref,props=_objectWithoutProperties(_ref,_excluded);const baseClasses='inline-flex items-center justify-center rounded-md font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none';const variants={primary:'bg-indigo-600 text-white hover:bg-indigo-700 focus:ring-indigo-500',secondary:'bg-gray-100 text-gray-900 hover:bg-gray-200 focus:ring-gray-500',outline:'border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-indigo-500',ghost:'text-gray-700 hover:bg-gray-100 focus:ring-indigo-500',destructive:'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500'};const sizes={sm:'h-8 px-3 text-sm',md:'h-10 px-4 text-sm',lg:'h-12 px-6 text-base'};const buttonClasses=cn(baseClasses,variants[variant],sizes[size],className);return/*#__PURE__*/React.createElement('button',_objectSpread({className:buttonClasses,disabled:disabled||loading,onClick,type},props),loading&&/*#__PURE__*/React.createElement('svg',{className:'animate-spin -ml-1 mr-2 h-4 w-4',xmlns:'http://www.w3.org/2000/svg',fill:'none',viewBox:'0 0 24 24'},/*#__PURE__*/React.createElement('circle',{className:'opacity-25',cx:'12',cy:'12',r:'10',stroke:'currentColor',strokeWidth:'4'}),/*#__PURE__*/React.createElement('path',{className:'opacity-75',fill:'currentColor',d:'M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z'})),!loading&&icon&&/*#__PURE__*/React.createElement('span',{className:'mr-2'},icon),children);};", "map": {"version": 3, "names": ["React", "cn", "<PERSON><PERSON>", "_ref", "className", "variant", "size", "loading", "icon", "children", "disabled", "onClick", "type", "props", "_objectWithoutProperties", "_excluded", "baseClasses", "variants", "primary", "secondary", "outline", "ghost", "destructive", "sizes", "sm", "md", "lg", "buttonClasses", "createElement", "_objectSpread", "xmlns", "fill", "viewBox", "cx", "cy", "r", "stroke", "strokeWidth", "d"], "sources": ["D:/repos-personal/repos/openapi-to-mcp/ui/src/components/ui/Button.tsx"], "sourcesContent": ["/**\r\n * Reusable Button component\r\n */\r\n\r\nimport React from 'react';\r\nimport { cn } from '../../utils';\r\n\r\nexport interface ButtonProps {\r\n  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'destructive';\r\n  size?: 'sm' | 'md' | 'lg';\r\n  loading?: boolean;\r\n  icon?: any;\r\n  children?: any;\r\n  className?: string;\r\n  onClick?: () => void | Promise<void>;\r\n  disabled?: boolean;\r\n  type?: 'button' | 'submit' | 'reset';\r\n}\r\n\r\nexport const Button: React.FC<ButtonProps> = ({ \r\n  className, \r\n  variant = 'primary', \r\n  size = 'md', \r\n  loading = false, \r\n  icon, \r\n  children, \r\n  disabled,\r\n  onClick,\r\n  type = 'button',\r\n  ...props \r\n}) => {\r\n  const baseClasses = 'inline-flex items-center justify-center rounded-md font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none';\r\n  \r\n  const variants = {\r\n    primary: 'bg-indigo-600 text-white hover:bg-indigo-700 focus:ring-indigo-500',\r\n    secondary: 'bg-gray-100 text-gray-900 hover:bg-gray-200 focus:ring-gray-500',\r\n    outline: 'border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-indigo-500',\r\n    ghost: 'text-gray-700 hover:bg-gray-100 focus:ring-indigo-500',\r\n    destructive: 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500',\r\n  };\r\n\r\n  const sizes = {\r\n    sm: 'h-8 px-3 text-sm',\r\n    md: 'h-10 px-4 text-sm',\r\n    lg: 'h-12 px-6 text-base',\r\n  };\r\n\r\n  const buttonClasses = cn(\r\n    baseClasses,\r\n    variants[variant],\r\n    sizes[size],\r\n    className\r\n  );\r\n\r\n  return React.createElement('button', {\r\n    className: buttonClasses,\r\n    disabled: disabled || loading,\r\n    onClick,\r\n    type,\r\n    ...props\r\n  }, \r\n    loading && React.createElement('svg', {\r\n      className: 'animate-spin -ml-1 mr-2 h-4 w-4',\r\n      xmlns: 'http://www.w3.org/2000/svg',\r\n      fill: 'none',\r\n      viewBox: '0 0 24 24'\r\n    },\r\n      React.createElement('circle', {\r\n        className: 'opacity-25',\r\n        cx: '12',\r\n        cy: '12',\r\n        r: '10',\r\n        stroke: 'currentColor',\r\n        strokeWidth: '4'\r\n      }),\r\n      React.createElement('path', {\r\n        className: 'opacity-75',\r\n        fill: 'currentColor',\r\n        d: 'M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z'\r\n      })\r\n    ),\r\n    !loading && icon && React.createElement('span', { className: 'mr-2' }, icon),\r\n    children\r\n  );\r\n};\r\n"], "mappings": "wXAAA;AACA;AACA,GAEA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,EAAE,KAAQ,aAAa,CAchC,MAAO,MAAM,CAAAC,MAA6B,CAAGC,IAAA,EAWvC,IAXwC,CAC5CC,SAAS,CACTC,OAAO,CAAG,SAAS,CACnBC,IAAI,CAAG,IAAI,CACXC,OAAO,CAAG,KAAK,CACfC,IAAI,CACJC,QAAQ,CACRC,QAAQ,CACRC,OAAO,CACPC,IAAI,CAAG,QAET,CAAC,CAAAT,IAAA,CADIU,KAAK,CAAAC,wBAAA,CAAAX,IAAA,CAAAY,SAAA,EAER,KAAM,CAAAC,WAAW,CAAG,uOAAuO,CAE3P,KAAM,CAAAC,QAAQ,CAAG,CACfC,OAAO,CAAE,oEAAoE,CAC7EC,SAAS,CAAE,iEAAiE,CAC5EC,OAAO,CAAE,sFAAsF,CAC/FC,KAAK,CAAE,uDAAuD,CAC9DC,WAAW,CAAE,2DACf,CAAC,CAED,KAAM,CAAAC,KAAK,CAAG,CACZC,EAAE,CAAE,kBAAkB,CACtBC,EAAE,CAAE,mBAAmB,CACvBC,EAAE,CAAE,qBACN,CAAC,CAED,KAAM,CAAAC,aAAa,CAAG1B,EAAE,CACtBe,WAAW,CACXC,QAAQ,CAACZ,OAAO,CAAC,CACjBkB,KAAK,CAACjB,IAAI,CAAC,CACXF,SACF,CAAC,CAED,mBAAOJ,KAAK,CAAC4B,aAAa,CAAC,QAAQ,CAAAC,aAAA,EACjCzB,SAAS,CAAEuB,aAAa,CACxBjB,QAAQ,CAAEA,QAAQ,EAAIH,OAAO,CAC7BI,OAAO,CACPC,IAAI,EACDC,KAAK,EAERN,OAAO,eAAIP,KAAK,CAAC4B,aAAa,CAAC,KAAK,CAAE,CACpCxB,SAAS,CAAE,iCAAiC,CAC5C0B,KAAK,CAAE,4BAA4B,CACnCC,IAAI,CAAE,MAAM,CACZC,OAAO,CAAE,WACX,CAAC,cACChC,KAAK,CAAC4B,aAAa,CAAC,QAAQ,CAAE,CAC5BxB,SAAS,CAAE,YAAY,CACvB6B,EAAE,CAAE,IAAI,CACRC,EAAE,CAAE,IAAI,CACRC,CAAC,CAAE,IAAI,CACPC,MAAM,CAAE,cAAc,CACtBC,WAAW,CAAE,GACf,CAAC,CAAC,cACFrC,KAAK,CAAC4B,aAAa,CAAC,MAAM,CAAE,CAC1BxB,SAAS,CAAE,YAAY,CACvB2B,IAAI,CAAE,cAAc,CACpBO,CAAC,CAAE,iHACL,CAAC,CACH,CAAC,CACD,CAAC/B,OAAO,EAAIC,IAAI,eAAIR,KAAK,CAAC4B,aAAa,CAAC,MAAM,CAAE,CAAExB,SAAS,CAAE,MAAO,CAAC,CAAEI,IAAI,CAAC,CAC5EC,QACF,CAAC,CACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}