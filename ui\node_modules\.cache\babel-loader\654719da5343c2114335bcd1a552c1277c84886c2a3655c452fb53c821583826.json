{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst AlignStartHorizontal = createLucideIcon(\"AlignStartHorizontal\", [[\"rect\", {\n  width: \"6\",\n  height: \"16\",\n  x: \"4\",\n  y: \"6\",\n  rx: \"2\",\n  key: \"1n4dg1\"\n}], [\"rect\", {\n  width: \"6\",\n  height: \"9\",\n  x: \"14\",\n  y: \"6\",\n  rx: \"2\",\n  key: \"17khns\"\n}], [\"path\", {\n  d: \"M22 2H2\",\n  key: \"fhrpnj\"\n}]]);\nexport { AlignStartHorizontal as default };", "map": {"version": 3, "names": ["AlignStartHorizontal", "createLucideIcon", "width", "height", "x", "y", "rx", "key", "d"], "sources": ["D:\\repos-personal\\repos\\openapi-to-mcp\\ui\\node_modules\\lucide-react\\src\\icons\\align-start-horizontal.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name AlignStartHorizontal\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iNiIgaGVpZ2h0PSIxNiIgeD0iNCIgeT0iNiIgcng9IjIiIC8+CiAgPHJlY3Qgd2lkdGg9IjYiIGhlaWdodD0iOSIgeD0iMTQiIHk9IjYiIHJ4PSIyIiAvPgogIDxwYXRoIGQ9Ik0yMiAySDIiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/align-start-horizontal\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst AlignStartHorizontal = createLucideIcon('AlignStartHorizontal', [\n  [\n    'rect',\n    { width: '6', height: '16', x: '4', y: '6', rx: '2', key: '1n4dg1' },\n  ],\n  [\n    'rect',\n    { width: '6', height: '9', x: '14', y: '6', rx: '2', key: '17khns' },\n  ],\n  ['path', { d: 'M22 2H2', key: 'fhrpnj' }],\n]);\n\nexport default AlignStartHorizontal;\n"], "mappings": ";;;;;AAaM,MAAAA,oBAAA,GAAuBC,gBAAA,CAAiB,sBAAwB,GACpE,CACE,QACA;EAAEC,KAAA,EAAO,GAAK;EAAAC,MAAA,EAAQ,IAAM;EAAAC,CAAA,EAAG,GAAK;EAAAC,CAAA,EAAG,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,GAAA,EAAK;AAAS,EACrE,EACA,CACE,QACA;EAAEL,KAAA,EAAO,GAAK;EAAAC,MAAA,EAAQ,GAAK;EAAAC,CAAA,EAAG,IAAM;EAAAC,CAAA,EAAG,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,GAAA,EAAK;AAAS,EACrE,EACA,CAAC,MAAQ;EAAEC,CAAA,EAAG,SAAW;EAAAD,GAAA,EAAK;AAAA,CAAU,EACzC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}