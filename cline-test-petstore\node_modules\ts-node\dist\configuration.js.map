{"version": 3, "file": "configuration.js", "sourceRoot": "", "sources": ["../src/configuration.ts"], "names": [], "mappings": ";;;AAAA,+BAA8C;AAE9C,mCAOiB;AAEjB,iDAAmD;AACnD,2CAAmE;AACnE,iCAKgB;AAEhB;;GAEG;AACH,MAAM,wBAAwB,GAAG;IAC/B,SAAS,EAAE,IAAI;IACf,eAAe,EAAE,KAAK;IACtB,aAAa,EAAE,IAAI;IACnB,WAAW,EAAE,KAAK;IAClB,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,UAAU;CACnB,CAAC;AAEF;;GAEG;AACH,SAAS,SAAS,CAAC,EAAY,EAAE,MAA6B;IAC5D,sDAAsD;IACtD,OAAO,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC;IAC1B,OAAO,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC;IAC9B,OAAO,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC;IAChC,OAAO,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC;IACrC,OAAO,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC;IACrC,OAAO,MAAM,CAAC,OAAO,CAAC,mBAAmB,CAAC;IAE1C,iDAAiD;IACjD,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,KAAK,SAAS,EAAE;QACvC,MAAM,CAAC,OAAO,CAAC,MAAM,GAAG,EAAE,CAAC,YAAY,CAAC,GAAG,CAAC;KAC7C;IAED,qGAAqG;IACrG,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,KAAK,SAAS,EAAE;QACvC,MAAM,CAAC,OAAO,CAAC,MAAM,GAAG,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC;KAChD;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,gBAAgB;AAChB,SAAgB,iBAAiB,CAAC,UAAyB;;IACzD,MAAM,GAAG,GAAG,IAAA,cAAO,EACjB,MAAA,MAAA,MAAA,UAAU,CAAC,GAAG,mCAAI,UAAU,CAAC,GAAG,mCAAI,gBAAQ,CAAC,GAAG,mCAAI,OAAO,CAAC,GAAG,EAAE,CAClE,CAAC;IACF,MAAM,YAAY,GAAG,MAAA,UAAU,CAAC,QAAQ,mCAAI,gBAAQ,CAAC,QAAQ,CAAC;IAE9D,mDAAmD;IACnD,IAAI,sBAAsB,GAAG,IAAA,qDAA8C,EACzE,SAAS,EACT,UAAU,CAAC,gBAAgB,EAC3B,UAAU,CAAC,OAAO,EAClB,GAAG,CACJ,CAAC;IACF,IAAI,EAAE,QAAQ,EAAE,EAAE,EAAE,GAAG,sBAAsB,CAC3C,YAAY,EACZ,sBAAsB,CACvB,CAAC;IAEF,sEAAsE;IACtE,MAAM,EAAE,cAAc,EAAE,MAAM,EAAE,yBAAyB,EAAE,eAAe,EAAE,GAC1E,UAAU,CAAC,GAAG,EAAE,EAAE,EAAE,UAAU,CAAC,CAAC;IAElC,MAAM,OAAO,GAAG,IAAA,aAAM,EACpB,EAAE,EACF,gBAAQ,EACR,yBAAyB,IAAI,EAAE,EAC/B,EAAE,eAAe,EAAE,EACnB,UAAU,CACX,CAAC;IACF,OAAO,CAAC,OAAO,GAAG;QAChB,GAAG,CAAC,yBAAyB,CAAC,OAAO,IAAI,EAAE,CAAC;QAC5C,GAAG,CAAC,UAAU,CAAC,OAAO,IAAI,EAAE,CAAC;KAC9B,CAAC;IAEF,kDAAkD;IAClD,6FAA6F;IAC7F,0EAA0E;IAC1E,IAAI,cAAc,EAAE;QAClB,sBAAsB,GAAG,IAAA,qDAA8C,EACrE,cAAc,EACd,UAAU,CAAC,gBAAgB,EAC3B,UAAU,CAAC,OAAO,EAClB,GAAG,CACJ,CAAC;QACF,CAAC,EAAE,QAAQ,EAAE,GAAG,eAAe,CAC7B,OAAO,CAAC,QAAQ,EAChB,MAAA,eAAe,CAAC,QAAQ,mCAAI,sBAAsB,CACnD,CAAC,CAAC;KACJ;IAED,OAAO;QACL,OAAO;QACP,MAAM;QACN,sBAAsB;QACtB,eAAe;QACf,cAAc;QACd,GAAG;QACH,QAAQ;KACT,CAAC;AACJ,CAAC;AA3DD,8CA2DC;AAED;;;;;;;;GAQG;AACH,SAAgB,UAAU,CACxB,GAAW,EACX,EAAY,EACZ,aAA4B;;IAiB5B,uDAAuD;IACvD,MAAM,WAAW,GAIZ,EAAE,CAAC;IACR,IAAI,MAAM,GAAQ,EAAE,eAAe,EAAE,EAAE,EAAE,CAAC;IAC1C,IAAI,QAAQ,GAAG,GAAG,CAAC;IACnB,IAAI,cAAc,GAAuB,SAAS,CAAC;IACnD,MAAM,gBAAgB,GAAG,IAAA,cAAO,EAAC,GAAG,EAAE,MAAA,aAAa,CAAC,gBAAgB,mCAAI,GAAG,CAAC,CAAC;IAE7E,MAAM,EACJ,UAAU,GAAG,EAAE,CAAC,GAAG,CAAC,UAAU,EAC9B,QAAQ,GAAG,EAAE,CAAC,GAAG,CAAC,QAAQ,EAC1B,WAAW,GAAG,gBAAQ,CAAC,WAAW,EAClC,OAAO,GAAG,gBAAQ,CAAC,OAAO,EAC1B,OAAO,GAAG,gBAAQ,CAAC,OAAO,GAC3B,GAAG,aAAa,CAAC;IAElB,6CAA6C;IAC7C,IAAI,CAAC,WAAW,EAAE;QAChB,IAAI,OAAO,EAAE;YACX,MAAM,QAAQ,GAAG,IAAA,cAAO,EAAC,GAAG,EAAE,OAAO,CAAC,CAAC;YACvC,MAAM,MAAM,GAAG,IAAA,WAAI,EAAC,QAAQ,EAAE,eAAe,CAAC,CAAC;YAC/C,cAAc,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC;SACzD;aAAM;YACL,cAAc,GAAG,EAAE,CAAC,cAAc,CAAC,gBAAgB,EAAE,UAAU,CAAC,CAAC;SAClE;QAED,IAAI,cAAc,EAAE;YAClB,IAAI,uBAAuB,GAAG,cAAc,CAAC;YAC7C,MAAM,WAAW,GAAG,IAAA,gCAAiB,EAAC,EAAE,CAAC,CAAC;YAC1C,MAAM,MAAM,GAA0B,EAAE,CAAC;YAEzC,4BAA4B;YAC5B,OAAO,IAAI,EAAE;gBACX,MAAM,MAAM,GAAG,EAAE,CAAC,cAAc,CAAC,uBAAuB,EAAE,QAAQ,CAAC,CAAC;gBAEpE,sBAAsB;gBACtB,IAAI,MAAM,CAAC,KAAK,EAAE;oBAChB,OAAO;wBACL,cAAc;wBACd,MAAM,EAAE,EAAE,MAAM,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,SAAS,EAAE,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE;wBAC9D,yBAAyB,EAAE,EAAE;wBAC7B,eAAe,EAAE,EAAE;qBACpB,CAAC;iBACH;gBAED,MAAM,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC;gBACxB,MAAM,EAAE,GAAG,IAAA,cAAO,EAAC,uBAAuB,CAAC,CAAC;gBAC5C,WAAW,CAAC,IAAI,CAAC;oBACf,MAAM,EAAE,CAAC;oBACT,QAAQ,EAAE,EAAE;oBACZ,UAAU,EAAE,uBAAuB;iBACpC,CAAC,CAAC;gBAEH,IAAI,CAAC,CAAC,OAAO,IAAI,IAAI;oBAAE,MAAM;gBAC7B,MAAM,0BAA0B,GAAG,WAAW,CAAC,oBAAoB,CACjE,CAAC,CAAC,OAAO,EACT;oBACE,UAAU;oBACV,aAAa,EAAE,EAAE,CAAC,GAAG,CAAC,aAAa;oBACnC,QAAQ;oBACR,yBAAyB,EAAE,EAAE,CAAC,GAAG,CAAC,yBAAyB;oBAC3D,KAAK,EAAE,OAAO;iBACf,EACD,EAAE,EACF,MAAM,EACL,EAA4B,CAAC,wBAAwB,CACvD,CAAC;gBACF,IAAI,MAAM,CAAC,MAAM,EAAE;oBACjB,OAAO;wBACL,cAAc;wBACd,MAAM,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE;wBAC9C,yBAAyB,EAAE,EAAE;wBAC7B,eAAe,EAAE,EAAE;qBACpB,CAAC;iBACH;gBACD,IAAI,0BAA0B,IAAI,IAAI;oBAAE,MAAM;gBAC9C,uBAAuB,GAAG,0BAA0B,CAAC;aACtD;YAED,CAAC,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;SACzC;KACF;IAED,gEAAgE;IAChE,MAAM,yBAAyB,GAAoB,EAAE,CAAC;IACtD,MAAM,eAAe,GAAoB,EAAE,CAAC;IAC5C,KAAK,IAAI,CAAC,GAAG,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;QAChD,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;QACxD,MAAM,OAAO,GAAG,qCAAqC,CACnD,MAAM,CAAC,SAAS,CAAC,CAClB,CAAC,UAAU,CAAC;QAEb,4FAA4F;QAC5F,IAAI,OAAO,CAAC,OAAO,EAAE;YACnB,wEAAwE;YACxE,MAAM,wBAAwB,GAAG,IAAA,sCAA+B,EAC9D,IAAA,cAAO,EAAC,UAAU,CAAC,CACpB,CAAC;YACF,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,IAAY,EAAE,EAAE,CACrD,wBAAwB,CAAC,IAAI,EAAE,KAAK,CAAC,CACtC,CAAC;SACH;QACD,IAAI,OAAO,CAAC,QAAQ,EAAE;YACpB,OAAO,CAAC,QAAQ,GAAG,IAAA,cAAO,EAAC,QAAQ,EAAE,OAAO,CAAC,QAAS,CAAC,CAAC;SACzD;QAED,6DAA6D;QAC7D,IAAI,OAAO,CAAC,WAAW,EAAE;YACvB,eAAe,CAAC,WAAW,GAAG,QAAQ,CAAC;SACxC;QACD,IAAI,OAAO,CAAC,UAAU,IAAI,IAAI,EAAE;YAC9B,eAAe,CAAC,UAAU,GAAG,QAAQ,CAAC;SACvC;QACD,IAAI,OAAO,CAAC,QAAQ,IAAI,IAAI,EAAE;YAC5B,eAAe,CAAC,QAAQ,GAAG,QAAQ,CAAC;SACrC;QACD,IAAI,OAAO,CAAC,GAAG,IAAI,IAAI,EAAE;YACvB,eAAe,CAAC,GAAG,GAAG,QAAQ,CAAC;SAChC;QAED,IAAA,aAAM,EAAC,yBAAyB,EAAE,OAAO,CAAC,CAAC;KAC5C;IAED,gCAAgC;IAChC,MAAM,KAAK,GACT,MAAA,MAAA,aAAa,CAAC,KAAK,mCAAI,yBAAyB,CAAC,KAAK,mCAAI,gBAAQ,CAAC,KAAK,CAAC;IAE3E,6FAA6F;IAC7F,MAAM,0BAA0B,GAAG,cAAc,IAAI,IAAI,CAAC;IAC1D,MAAM,oCAAoC,GAAG,0BAA0B;QACrE,CAAC,CAAC,SAAS;QACX,CAAC,CAAC;YACE,GAAG,IAAA,gDAAoC,EAAC,EAAE,CAAC,CAAC,eAAe;YAC3D,KAAK,EAAE,CAAC,MAAM,CAAC;SAChB,CAAC;IAEN,yCAAyC;IACzC,MAAM,CAAC,eAAe,GAAG,MAAM,CAAC,MAAM,CACpC,EAAE;IACF,qDAAqD;IACrD,oCAAoC;IACpC,kCAAkC;IAClC,MAAM,CAAC,eAAe;IACtB,eAAe;IACf,gBAAQ,CAAC,eAAe;IACxB,6CAA6C;IAC7C,yBAAyB,CAAC,eAAe;IACzC,0BAA0B;IAC1B,aAAa,CAAC,eAAe;IAC7B,mDAAmD;IACnD,wBAAwB,CACzB,CAAC;IAEF,MAAM,WAAW,GAAG,SAAS,CAC3B,EAAE,EACF,EAAE,CAAC,0BAA0B,CAC3B,MAAM,EACN;QACE,UAAU;QACV,QAAQ;QACR,uDAAuD;QACvD,8DAA8D;QAC9D,aAAa,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE;QACtD,yBAAyB,EAAE,EAAE,CAAC,GAAG,CAAC,yBAAyB;KAC5D,EACD,QAAQ,EACR,SAAS,EACT,cAAc,CACf,CACF,CAAC;IAEF,OAAO;QACL,cAAc;QACd,MAAM,EAAE,WAAW;QACnB,yBAAyB;QACzB,eAAe;KAChB,CAAC;AACJ,CAAC;AAxMD,gCAwMC;AAED;;;;GAIG;AACH,SAAgB,sBAAsB,CACpC,IAAwB,EACxB,cAAsB;IAEtB,MAAM,EAAE,QAAQ,EAAE,GAAG,eAAe,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC;IAC3D,MAAM,EAAE,GAAG,YAAY,CAAC,QAAQ,CAAC,CAAC;IAClC,OAAO,EAAE,QAAQ,EAAE,EAAE,EAAE,CAAC;AAC1B,CAAC;AAPD,wDAOC;AAED,SAAS,eAAe,CAAC,IAAwB,EAAE,cAAsB;IACvE,MAAM,yBAAyB,GAC7B,IAAA,sCAA+B,EAAC,cAAc,CAAC,CAAC;IAClD,MAAM,QAAQ,GAAG,yBAAyB,CAAC,IAAI,IAAI,YAAY,EAAE,IAAI,CAAC,CAAC;IACvE,OAAO,EAAE,QAAQ,EAAE,CAAC;AACtB,CAAC;AAED,gBAAgB;AAChB,SAAgB,YAAY,CAAC,QAAgB;IAC3C,OAAO,IAAA,uCAAgC,EAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;AAC7D,CAAC;AAFD,oCAEC;AAED;;;GAGG;AACH,SAAS,qCAAqC,CAAC,UAAe;IAI5D,IAAI,UAAU,IAAI,IAAI;QAAE,OAAO,EAAE,UAAU,EAAE,EAAE,EAAE,YAAY,EAAE,EAAE,EAAE,CAAC;IACpE,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,eAAe,EACf,IAAI,EACJ,KAAK,EACL,MAAM,EACN,iBAAiB,EACjB,QAAQ,EACR,YAAY,EACZ,MAAM,EACN,OAAO,EACP,UAAU,EACV,aAAa,EACb,SAAS,EACT,UAAU,EACV,KAAK,EACL,QAAQ,EACR,WAAW,EACX,qBAAqB,EACrB,GAAG,EACH,oBAAoB,EACpB,GAAG,EACH,+BAA+B,EAC/B,8BAA8B,EAC9B,GAAG,YAAY,EAChB,GAAG,UAA6B,CAAC;IAClC,MAAM,uBAAuB,GAAG;QAC9B,QAAQ;QACR,YAAY;QACZ,eAAe;QACf,IAAI;QACJ,qBAAqB;QACrB,KAAK;QACL,MAAM;QACN,iBAAiB;QACjB,QAAQ;QACR,YAAY;QACZ,MAAM;QACN,OAAO;QACP,UAAU;QACV,aAAa;QACb,SAAS;QACT,UAAU;QACV,KAAK;QACL,QAAQ;QACR,WAAW;QACX,GAAG;QACH,oBAAoB;QACpB,GAAG;QACH,+BAA+B;QAC/B,8BAA8B;KAC/B,CAAC;IACF,yFAAyF;IACzF,MAAM,oBAAoB,GACxB,IAAmD,CAAC;IACtD,MAAM,iBAAiB,GACrB,IAAoC,CAAC;IACvC,OAAO,EAAE,UAAU,EAAE,uBAAuB,EAAE,YAAY,EAAE,CAAC;AAC/D,CAAC;AAED,gBAAgB;AACH,QAAA,0BAA0B,GAAG,MAAM,EAAE,CAAC;AAEnD;;;;GAIG;AACH,SAAgB,mBAAmB,CACjC,MAA6B,EAC7B,QAAgB,EAChB,MAA4B,EAC5B,QAA8B,EAC9B,QAA8B;IAE9B,MAAM,EAAE,SAAS,GAAG,KAAK,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC;IAC7C,IAAI,OAAO,GACT,MAAM,CAAC,OAAO,CAAC,OAAQ,CAAC;IAC1B,IAAI,OAAO,IAAI,IAAI,EAAE;QACnB,IAAI,SAAS;YAAE,OAAO,GAAG,QAAQ,CAAC;QAClC,mFAAmF;;YAC9E,OAAO,GAAG,kCAA0B,CAAC;KAC3C;IACD,MAAM,EAAE,MAAM,GAAG,OAAO,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC;IAC5C,kEAAkE;IAClE,oEAAoE;IACpE,MAAM,OAAO,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;IACvC,MAAM,KAAK,GAAG,MAAM,aAAN,MAAM,cAAN,MAAM,GAAI,EAAE,CAAC;IAC3B,uEAAuE;IACvE,gIAAgI;IAChI,MAAM,OAAO,GAAG,QAAQ,aAAR,QAAQ,cAAR,QAAQ,GAAI,CAAC,MAAM,CAAC,CAAC,CAAC,0FAA0F;IAEhI,uBAAuB;IAEvB,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,SAAS,EAAE,CAAC;AACjE,CAAC;AA3BD,kDA2BC", "sourcesContent": ["import { resolve, dirname, join } from 'path';\nimport type * as _ts from 'typescript';\nimport {\n  CreateOptions,\n  DEFAULTS,\n  OptionBasePaths,\n  RegisterOptions,\n  TSCommon,\n  TsConfigOptions,\n} from './index';\nimport type { TSInternal } from './ts-compiler-types';\nimport { createTsInternals } from './ts-internals';\nimport { getDefaultTsconfigJsonForNodeVersion } from './tsconfigs';\nimport {\n  assign,\n  attemptRequireWithV8CompileCache,\n  createProjectLocalResolveHelper,\n  getBasePathForProjectLocalDependencyResolution,\n} from './util';\n\n/**\n * TypeScript compiler option values required by `ts-node` which cannot be overridden.\n */\nconst TS_NODE_COMPILER_OPTIONS = {\n  sourceMap: true,\n  inlineSourceMap: false,\n  inlineSources: true,\n  declaration: false,\n  noEmit: false,\n  outDir: '.ts-node',\n};\n\n/*\n * Do post-processing on config options to support `ts-node`.\n */\nfunction fixConfig(ts: TSCommon, config: _ts.ParsedCommandLine) {\n  // Delete options that *should not* be passed through.\n  delete config.options.out;\n  delete config.options.outFile;\n  delete config.options.composite;\n  delete config.options.declarationDir;\n  delete config.options.declarationMap;\n  delete config.options.emitDeclarationOnly;\n\n  // Target ES5 output by default (instead of ES3).\n  if (config.options.target === undefined) {\n    config.options.target = ts.ScriptTarget.ES5;\n  }\n\n  // Target CommonJS modules by default (instead of magically switching to ES6 when the target is ES6).\n  if (config.options.module === undefined) {\n    config.options.module = ts.ModuleKind.CommonJS;\n  }\n\n  return config;\n}\n\n/** @internal */\nexport function findAndReadConfig(rawOptions: CreateOptions) {\n  const cwd = resolve(\n    rawOptions.cwd ?? rawOptions.dir ?? DEFAULTS.cwd ?? process.cwd()\n  );\n  const compilerName = rawOptions.compiler ?? DEFAULTS.compiler;\n\n  // Compute minimum options to read the config file.\n  let projectLocalResolveDir = getBasePathForProjectLocalDependencyResolution(\n    undefined,\n    rawOptions.projectSearchDir,\n    rawOptions.project,\n    cwd\n  );\n  let { compiler, ts } = resolveAndLoadCompiler(\n    compilerName,\n    projectLocalResolveDir\n  );\n\n  // Read config file and merge new options between env and CLI options.\n  const { configFilePath, config, tsNodeOptionsFromTsconfig, optionBasePaths } =\n    readConfig(cwd, ts, rawOptions);\n\n  const options = assign<RegisterOptions>(\n    {},\n    DEFAULTS,\n    tsNodeOptionsFromTsconfig || {},\n    { optionBasePaths },\n    rawOptions\n  );\n  options.require = [\n    ...(tsNodeOptionsFromTsconfig.require || []),\n    ...(rawOptions.require || []),\n  ];\n\n  // Re-resolve the compiler in case it has changed.\n  // Compiler is loaded relative to tsconfig.json, so tsconfig discovery may cause us to load a\n  // different compiler than we did above, even if the name has not changed.\n  if (configFilePath) {\n    projectLocalResolveDir = getBasePathForProjectLocalDependencyResolution(\n      configFilePath,\n      rawOptions.projectSearchDir,\n      rawOptions.project,\n      cwd\n    );\n    ({ compiler } = resolveCompiler(\n      options.compiler,\n      optionBasePaths.compiler ?? projectLocalResolveDir\n    ));\n  }\n\n  return {\n    options,\n    config,\n    projectLocalResolveDir,\n    optionBasePaths,\n    configFilePath,\n    cwd,\n    compiler,\n  };\n}\n\n/**\n * Load TypeScript configuration. Returns the parsed TypeScript config and\n * any `ts-node` options specified in the config file.\n *\n * Even when a tsconfig.json is not loaded, this function still handles merging\n * compilerOptions from various sources: API, environment variables, etc.\n *\n * @internal\n */\nexport function readConfig(\n  cwd: string,\n  ts: TSCommon,\n  rawApiOptions: CreateOptions\n): {\n  /**\n   * Path of tsconfig file if one was loaded\n   */\n  configFilePath: string | undefined;\n  /**\n   * Parsed TypeScript configuration with compilerOptions merged from all other sources (env vars, etc)\n   */\n  config: _ts.ParsedCommandLine;\n  /**\n   * ts-node options pulled from `tsconfig.json`, NOT merged with any other sources.  Merging must happen outside\n   * this function.\n   */\n  tsNodeOptionsFromTsconfig: TsConfigOptions;\n  optionBasePaths: OptionBasePaths;\n} {\n  // Ordered [a, b, c] where config a extends b extends c\n  const configChain: Array<{\n    config: any;\n    basePath: string;\n    configPath: string;\n  }> = [];\n  let config: any = { compilerOptions: {} };\n  let basePath = cwd;\n  let configFilePath: string | undefined = undefined;\n  const projectSearchDir = resolve(cwd, rawApiOptions.projectSearchDir ?? cwd);\n\n  const {\n    fileExists = ts.sys.fileExists,\n    readFile = ts.sys.readFile,\n    skipProject = DEFAULTS.skipProject,\n    project = DEFAULTS.project,\n    tsTrace = DEFAULTS.tsTrace,\n  } = rawApiOptions;\n\n  // Read project configuration when available.\n  if (!skipProject) {\n    if (project) {\n      const resolved = resolve(cwd, project);\n      const nested = join(resolved, 'tsconfig.json');\n      configFilePath = fileExists(nested) ? nested : resolved;\n    } else {\n      configFilePath = ts.findConfigFile(projectSearchDir, fileExists);\n    }\n\n    if (configFilePath) {\n      let pathToNextConfigInChain = configFilePath;\n      const tsInternals = createTsInternals(ts);\n      const errors: Array<_ts.Diagnostic> = [];\n\n      // Follow chain of \"extends\"\n      while (true) {\n        const result = ts.readConfigFile(pathToNextConfigInChain, readFile);\n\n        // Return diagnostics.\n        if (result.error) {\n          return {\n            configFilePath,\n            config: { errors: [result.error], fileNames: [], options: {} },\n            tsNodeOptionsFromTsconfig: {},\n            optionBasePaths: {},\n          };\n        }\n\n        const c = result.config;\n        const bp = dirname(pathToNextConfigInChain);\n        configChain.push({\n          config: c,\n          basePath: bp,\n          configPath: pathToNextConfigInChain,\n        });\n\n        if (c.extends == null) break;\n        const resolvedExtendedConfigPath = tsInternals.getExtendsConfigPath(\n          c.extends,\n          {\n            fileExists,\n            readDirectory: ts.sys.readDirectory,\n            readFile,\n            useCaseSensitiveFileNames: ts.sys.useCaseSensitiveFileNames,\n            trace: tsTrace,\n          },\n          bp,\n          errors,\n          (ts as unknown as TSInternal).createCompilerDiagnostic\n        );\n        if (errors.length) {\n          return {\n            configFilePath,\n            config: { errors, fileNames: [], options: {} },\n            tsNodeOptionsFromTsconfig: {},\n            optionBasePaths: {},\n          };\n        }\n        if (resolvedExtendedConfigPath == null) break;\n        pathToNextConfigInChain = resolvedExtendedConfigPath;\n      }\n\n      ({ config, basePath } = configChain[0]);\n    }\n  }\n\n  // Merge and fix ts-node options that come from tsconfig.json(s)\n  const tsNodeOptionsFromTsconfig: TsConfigOptions = {};\n  const optionBasePaths: OptionBasePaths = {};\n  for (let i = configChain.length - 1; i >= 0; i--) {\n    const { config, basePath, configPath } = configChain[i];\n    const options = filterRecognizedTsConfigTsNodeOptions(\n      config['ts-node']\n    ).recognized;\n\n    // Some options are relative to the config file, so must be converted to absolute paths here\n    if (options.require) {\n      // Modules are found relative to the tsconfig file, not the `dir` option\n      const tsconfigRelativeResolver = createProjectLocalResolveHelper(\n        dirname(configPath)\n      );\n      options.require = options.require.map((path: string) =>\n        tsconfigRelativeResolver(path, false)\n      );\n    }\n    if (options.scopeDir) {\n      options.scopeDir = resolve(basePath, options.scopeDir!);\n    }\n\n    // Downstream code uses the basePath; we do not do that here.\n    if (options.moduleTypes) {\n      optionBasePaths.moduleTypes = basePath;\n    }\n    if (options.transpiler != null) {\n      optionBasePaths.transpiler = basePath;\n    }\n    if (options.compiler != null) {\n      optionBasePaths.compiler = basePath;\n    }\n    if (options.swc != null) {\n      optionBasePaths.swc = basePath;\n    }\n\n    assign(tsNodeOptionsFromTsconfig, options);\n  }\n\n  // Remove resolution of \"files\".\n  const files =\n    rawApiOptions.files ?? tsNodeOptionsFromTsconfig.files ?? DEFAULTS.files;\n\n  // Only if a config file is *not* loaded, load an implicit configuration from @tsconfig/bases\n  const skipDefaultCompilerOptions = configFilePath != null;\n  const defaultCompilerOptionsForNodeVersion = skipDefaultCompilerOptions\n    ? undefined\n    : {\n        ...getDefaultTsconfigJsonForNodeVersion(ts).compilerOptions,\n        types: ['node'],\n      };\n\n  // Merge compilerOptions from all sources\n  config.compilerOptions = Object.assign(\n    {},\n    // automatically-applied options from @tsconfig/bases\n    defaultCompilerOptionsForNodeVersion,\n    // tsconfig.json \"compilerOptions\"\n    config.compilerOptions,\n    // from env var\n    DEFAULTS.compilerOptions,\n    // tsconfig.json \"ts-node\": \"compilerOptions\"\n    tsNodeOptionsFromTsconfig.compilerOptions,\n    // passed programmatically\n    rawApiOptions.compilerOptions,\n    // overrides required by ts-node, cannot be changed\n    TS_NODE_COMPILER_OPTIONS\n  );\n\n  const fixedConfig = fixConfig(\n    ts,\n    ts.parseJsonConfigFileContent(\n      config,\n      {\n        fileExists,\n        readFile,\n        // Only used for globbing \"files\", \"include\", \"exclude\"\n        // When `files` option disabled, we want to avoid the fs calls\n        readDirectory: files ? ts.sys.readDirectory : () => [],\n        useCaseSensitiveFileNames: ts.sys.useCaseSensitiveFileNames,\n      },\n      basePath,\n      undefined,\n      configFilePath\n    )\n  );\n\n  return {\n    configFilePath,\n    config: fixedConfig,\n    tsNodeOptionsFromTsconfig,\n    optionBasePaths,\n  };\n}\n\n/**\n * Load the typescript compiler. It is required to load the tsconfig but might\n * be changed by the tsconfig, so we have to do this twice.\n * @internal\n */\nexport function resolveAndLoadCompiler(\n  name: string | undefined,\n  relativeToPath: string\n) {\n  const { compiler } = resolveCompiler(name, relativeToPath);\n  const ts = loadCompiler(compiler);\n  return { compiler, ts };\n}\n\nfunction resolveCompiler(name: string | undefined, relativeToPath: string) {\n  const projectLocalResolveHelper =\n    createProjectLocalResolveHelper(relativeToPath);\n  const compiler = projectLocalResolveHelper(name || 'typescript', true);\n  return { compiler };\n}\n\n/** @internal */\nexport function loadCompiler(compiler: string): TSCommon {\n  return attemptRequireWithV8CompileCache(require, compiler);\n}\n\n/**\n * Given the raw \"ts-node\" sub-object from a tsconfig, return an object with only the properties\n * recognized by \"ts-node\"\n */\nfunction filterRecognizedTsConfigTsNodeOptions(jsonObject: any): {\n  recognized: TsConfigOptions;\n  unrecognized: any;\n} {\n  if (jsonObject == null) return { recognized: {}, unrecognized: {} };\n  const {\n    compiler,\n    compilerHost,\n    compilerOptions,\n    emit,\n    files,\n    ignore,\n    ignoreDiagnostics,\n    logError,\n    preferTsExts,\n    pretty,\n    require,\n    skipIgnore,\n    transpileOnly,\n    typeCheck,\n    transpiler,\n    scope,\n    scopeDir,\n    moduleTypes,\n    experimentalReplAwait,\n    swc,\n    experimentalResolver,\n    esm,\n    experimentalSpecifierResolution,\n    experimentalTsImportSpecifiers,\n    ...unrecognized\n  } = jsonObject as TsConfigOptions;\n  const filteredTsConfigOptions = {\n    compiler,\n    compilerHost,\n    compilerOptions,\n    emit,\n    experimentalReplAwait,\n    files,\n    ignore,\n    ignoreDiagnostics,\n    logError,\n    preferTsExts,\n    pretty,\n    require,\n    skipIgnore,\n    transpileOnly,\n    typeCheck,\n    transpiler,\n    scope,\n    scopeDir,\n    moduleTypes,\n    swc,\n    experimentalResolver,\n    esm,\n    experimentalSpecifierResolution,\n    experimentalTsImportSpecifiers,\n  };\n  // Use the typechecker to make sure this implementation has the correct set of properties\n  const catchExtraneousProps: keyof TsConfigOptions =\n    null as any as keyof typeof filteredTsConfigOptions;\n  const catchMissingProps: keyof typeof filteredTsConfigOptions =\n    null as any as keyof TsConfigOptions;\n  return { recognized: filteredTsConfigOptions, unrecognized };\n}\n\n/** @internal */\nexport const ComputeAsCommonRootOfFiles = Symbol();\n\n/**\n * Some TS compiler options have defaults which are not provided by TS's config parsing functions.\n * This function centralizes the logic for computing those defaults.\n * @internal\n */\nexport function getTsConfigDefaults(\n  config: _ts.ParsedCommandLine,\n  basePath: string,\n  _files: string[] | undefined,\n  _include: string[] | undefined,\n  _exclude: string[] | undefined\n) {\n  const { composite = false } = config.options;\n  let rootDir: string | typeof ComputeAsCommonRootOfFiles =\n    config.options.rootDir!;\n  if (rootDir == null) {\n    if (composite) rootDir = basePath;\n    // Return this symbol to avoid computing from `files`, which would require fs calls\n    else rootDir = ComputeAsCommonRootOfFiles;\n  }\n  const { outDir = rootDir } = config.options;\n  // Docs are wrong: https://www.typescriptlang.org/tsconfig#include\n  // Docs say **, but it's actually **/*; compiler throws error for **\n  const include = _files ? [] : ['**/*'];\n  const files = _files ?? [];\n  // Docs are misleading: https://www.typescriptlang.org/tsconfig#exclude\n  // Docs say it excludes node_modules, bower_components, jspm_packages, but actually those are excluded via behavior of \"include\"\n  const exclude = _exclude ?? [outDir]; // TODO technically, outDir is absolute path, but exclude should be relative glob pattern?\n\n  // TODO compute baseUrl\n\n  return { rootDir, outDir, include, files, exclude, composite };\n}\n"]}