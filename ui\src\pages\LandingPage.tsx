/**
 * Landing Page component - Professional homepage
 */

import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { 
  <PERSON>ap, 
  ArrowR<PERSON>, 
  CheckCircle, 
  Star,
  Users,
  Globe,
  Shield,
  Rocket,
  Code,
  Bot
} from 'lucide-react';

export const LandingPage: React.FC = () => {
  const features = [
    {
      icon: Zap,
      title: 'Instant Conversion',
      description: 'Convert any OpenAPI specification to MCP server in seconds'
    },
    {
      icon: Code,
      title: 'Production Ready',
      description: 'Generated servers are TypeScript-based and production-ready'
    },
    {
      icon: Bot,
      title: 'AI Compatible',
      description: 'Works with Cline, Cursor, Windsurf, and other MCP clients'
    },
    {
      icon: Shield,
      title: 'Secure & Reliable',
      description: 'Built with security best practices and error handling'
    },
    {
      icon: Globe,
      title: 'Universal Support',
      description: 'Supports any REST API with OpenAPI 3.0+ specification'
    },
    {
      icon: Rocket,
      title: 'Deploy Anywhere',
      description: 'Deploy to Railway, Vercel, or any Node.js hosting platform'
    }
  ];

  const stats = [
    { label: 'APIs Converted', value: '1,000+' },
    { label: 'Happy Developers', value: '500+' },
    { label: 'MCP Servers Generated', value: '2,500+' },
    { label: 'Uptime', value: '99.9%' }
  ];

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-blue-50 via-white to-purple-50 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-5xl md:text-6xl font-bold text-gray-900 mb-6">
              Transform APIs into
              <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                {' '}MCP Servers
              </span>
            </h1>
            <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
              Convert any OpenAPI specification into a fully functional MCP (Model Context Protocol) server. 
              Enable AI assistants to interact with your APIs seamlessly.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
              <Link
                to="/convert"
                className="inline-flex items-center space-x-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-4 rounded-lg text-lg font-medium hover:from-blue-700 hover:to-purple-700 transition-all shadow-lg"
              >
                <Zap className="w-5 h-5" />
                <span>Start Converting</span>
                <ArrowRight className="w-5 h-5" />
              </Link>
              <Link
                to="/playground"
                className="inline-flex items-center space-x-2 bg-white text-gray-700 px-8 py-4 rounded-lg text-lg font-medium hover:bg-gray-50 transition-all border border-gray-300"
              >
                <span>View Examples</span>
              </Link>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto">
              {stats.map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="text-3xl font-bold text-gray-900 mb-1">{stat.value}</div>
                  <div className="text-gray-600">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              Why Choose MCPForge?
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              The most powerful and easy-to-use API to MCP converter in the market
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <div key={index} className="bg-white rounded-xl p-8 shadow-sm border border-gray-200 hover:shadow-lg transition-shadow">
                <div className="w-12 h-12 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center mb-6">
                  <feature.icon className="w-6 h-6 text-white" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">{feature.title}</h3>
                <p className="text-gray-600">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* How It Works */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              How It Works
            </h2>
            <p className="text-xl text-gray-600">
              Three simple steps to get your MCP server running
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <span className="text-2xl font-bold text-blue-600">1</span>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">Provide OpenAPI Spec</h3>
              <p className="text-gray-600">Upload your OpenAPI specification file or provide a URL to your API documentation</p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <span className="text-2xl font-bold text-purple-600">2</span>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">Generate MCP Server</h3>
              <p className="text-gray-600">Our AI-powered converter analyzes your API and generates a complete MCP server</p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <span className="text-2xl font-bold text-green-600">3</span>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">Deploy & Use</h3>
              <p className="text-gray-600">Download your server, deploy it, and connect it to your favorite AI assistant</p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-blue-600 to-purple-600">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-4xl font-bold text-white mb-6">
            Ready to Transform Your APIs?
          </h2>
          <p className="text-xl text-blue-100 mb-8">
            Join thousands of developers who are already using MCPify to power their AI assistants
          </p>
          <Link
            to="/convert"
            className="inline-flex items-center space-x-2 bg-white text-blue-600 px-8 py-4 rounded-lg text-lg font-medium hover:bg-gray-100 transition-all shadow-lg"
          >
            <Zap className="w-5 h-5" />
            <span>Get Started Free</span>
            <ArrowRight className="w-5 h-5" />
          </Link>
        </div>
      </section>
    </div>
  );
};
