
import dotenv from 'dotenv';
dotenv.config();

import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import axios from 'axios';
import { router as apiRoutes } from './routes';

const app = express();
const PORT = process.env.PORT || 8000;
const BASE_URL = process.env.BASE_URL || 'http://localhost:8000';

// Security middleware
app.use(helmet());
app.use(cors());

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Health check
app.get('/health', (req: express.Request, res: express.Response) => {
  res.json({
    status: 'healthy',
    name: 'instant-mcp',
    version: '1.0.0',
    timestamp: new Date().toISOString(),
    baseUrl: BASE_URL
  });
});

// MCP tool endpoints
app.use('/tools', apiRoutes);

// MCP protocol handler
app.post('/mcp', async (req: express.Request, res: express.Response) => {
  try {
    const { tool, parameters } = req.body;
    
    // Route to appropriate tool handler
    const response = await fetch(`http://localhost:${PORT}/tools/${tool}`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(parameters)
    });
    
    const result = await response.json();
    
    res.json({
      success: true,
      result
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : String(error)
    });
  }
});

// LLM Chat endpoint using LiteLLM Gateway and API key
app.post('/chat', async (req: express.Request, res: express.Response) => {
  const { message } = req.body;
  if (!message) {
    return res.status(400).json({ error: 'No message provided' });
  }

  try {
    const headers: Record<string, string> = {};
    if (process.env.LITELLM_API_KEY) {
      headers['Authorization'] = `Bearer ${process.env.LITELLM_API_KEY}`;
    }

    const llmUrl = process.env.LITELLM_URL || 'http://localhost:4000/chat/completions';
    const llmModel = process.env.LITELLM_MODEL || 'gpt-3.5-turbo';

    console.log('[MCP] LLM Chat request:', {
      url: llmUrl,
      model: llmModel,
      apiKeyPresent: !!process.env.LITELLM_API_KEY,
      headers,
      message
    });

    const llmRes = await axios.post(
      llmUrl,
      {
        model: llmModel,
        messages: [
          { role: 'system', content: 'You are an API assistant. Help the user interact with the API using natural language.' },
          { role: 'user', content: message }
        ]
      },
      { headers }
    );
    const llmMessage = llmRes.data?.choices?.[0]?.message?.content || llmRes.data?.choices?.[0]?.text || JSON.stringify(llmRes.data);

    res.json({ response: llmMessage });
  } catch (err: any) {
    console.error('[MCP] LLM call failed:', err && (err.response?.data || err.message || err.toString()), err);
    res.status(500).json({ 
      error: 'LLM call failed', 
      details: err && (err.response?.data || err.message || err.toString())
    });
  }
});

// Error handling
app.use((error: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  console.error('Server error:', error);
  res.status(500).json({
    error: 'Internal server error',
    message: error instanceof Error ? error.message : String(error)
  });
});

// 404 handler
app.use('*', (req: express.Request, res: express.Response) => {
  res.status(404).json({
    error: 'Not found',
    message: `Route ${req.originalUrl} not found`
  });
});

// Start server
const server = app.listen(PORT, () => {
  console.log(`🚀 instant-mcp MCP Server running on port ${PORT}`);
  console.log(`📖 Health check: http://localhost:${PORT}/health`);
  console.log(`🔧 Base URL: ${BASE_URL}`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully');
  server.close(() => {
    console.log('Server closed');
    process.exit(0);
  });
});

export default app;
