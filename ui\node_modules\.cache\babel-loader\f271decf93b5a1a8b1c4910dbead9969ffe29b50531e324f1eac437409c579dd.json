{"ast": null, "code": "/**\r\n * Header component - Professional production-ready header\r\n */import React,{useState}from'react';import{Link,useLocation}from'react-router-dom';import{Zap,Menu,X,User,Settings,LogOut,Crown,Sparkles}from'lucide-react';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";export const Header=()=>{const[isMenuOpen,setIsMenuOpen]=useState(false);const[isUserMenuOpen,setIsUserMenuOpen]=useState(false);const location=useLocation();const navigation=[{name:'Convert',href:'/convert',current:location.pathname==='/convert'},{name:'Chat',href:'/chat',current:location.pathname==='/chat',comingSoon:true},{name:'Tools',href:'/tools',current:location.pathname==='/tools',comingSoon:true},{name:'Playground',href:'/playground',current:location.pathname==='/playground',comingSoon:true}];const userNavigation=[{name:'Your Profile',href:'/profile',icon:User},{name:'Settings',href:'/settings',icon:Settings},{name:'Upgrade to Pro',href:'/pricing',icon:Crown,highlight:true},{name:'Sign out',href:'/logout',icon:LogOut}];return/*#__PURE__*/_jsx(\"header\",{className:\"bg-white shadow-lg border-b border-gray-200 sticky top-0 z-50\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between items-center h-16\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex items-center\",children:/*#__PURE__*/_jsxs(Link,{to:\"/\",className:\"flex items-center space-x-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex items-center justify-center w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg\",children:/*#__PURE__*/_jsx(Zap,{className:\"w-5 h-5 text-white\"})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-xl font-bold text-gray-900\",children:\"MCPify\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-xs text-gray-500 -mt-1\",children:\"API to MCP Converter\"})]})]})}),/*#__PURE__*/_jsx(\"nav\",{className:\"hidden md:flex space-x-1\",children:navigation.map(item=>/*#__PURE__*/_jsx(\"div\",{className:\"relative\",children:item.comingSoon?/*#__PURE__*/_jsxs(\"div\",{className:\"relative group\",children:[/*#__PURE__*/_jsxs(\"span\",{className:\"text-gray-400 px-3 py-2 text-sm font-medium cursor-not-allowed flex items-center space-x-1\",children:[/*#__PURE__*/_jsx(\"span\",{children:item.name}),/*#__PURE__*/_jsx(Sparkles,{className:\"w-3 h-3\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"absolute top-full left-1/2 transform -translate-x-1/2 mt-1 px-2 py-1 bg-gray-800 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap\",children:\"Coming Soon\"})]}):/*#__PURE__*/_jsx(Link,{to:item.href,className:\"px-3 py-2 text-sm font-medium rounded-md transition-colors \".concat(item.current?'bg-blue-100 text-blue-700':'text-gray-600 hover:text-gray-900 hover:bg-gray-100'),children:item.name})},item.name))}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-4\",children:[/*#__PURE__*/_jsxs(Link,{to:\"/pricing\",className:\"hidden md:flex items-center space-x-1 bg-gradient-to-r from-blue-600 to-purple-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:from-blue-700 hover:to-purple-700 transition-all\",children:[/*#__PURE__*/_jsx(Crown,{className:\"w-4 h-4\"}),/*#__PURE__*/_jsx(\"span\",{children:\"Upgrade\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"relative\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>setIsUserMenuOpen(!isUserMenuOpen),className:\"flex items-center space-x-2 text-gray-600 hover:text-gray-900 p-2 rounded-lg hover:bg-gray-100 transition-colors\",children:/*#__PURE__*/_jsx(\"div\",{className:\"w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center\",children:/*#__PURE__*/_jsx(User,{className:\"w-4 h-4 text-white\"})})}),isUserMenuOpen&&/*#__PURE__*/_jsx(\"div\",{className:\"absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-50\",children:userNavigation.map(item=>/*#__PURE__*/_jsxs(Link,{to:item.href,className:\"flex items-center space-x-2 px-4 py-2 text-sm hover:bg-gray-100 transition-colors \".concat(item.highlight?'text-blue-600 font-medium':'text-gray-700'),onClick:()=>setIsUserMenuOpen(false),children:[/*#__PURE__*/_jsx(item.icon,{className:\"w-4 h-4\"}),/*#__PURE__*/_jsx(\"span\",{children:item.name})]},item.name))})]}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>setIsMenuOpen(!isMenuOpen),className:\"md:hidden p-2 rounded-lg text-gray-600 hover:text-gray-900 hover:bg-gray-100\",children:isMenuOpen?/*#__PURE__*/_jsx(X,{className:\"w-5 h-5\"}):/*#__PURE__*/_jsx(Menu,{className:\"w-5 h-5\"})})]})]}),isMenuOpen&&/*#__PURE__*/_jsx(\"div\",{className:\"md:hidden border-t border-gray-200 py-4\",children:/*#__PURE__*/_jsx(\"div\",{className:\"space-y-1\",children:navigation.map(item=>/*#__PURE__*/_jsx(\"div\",{children:item.comingSoon?/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between px-3 py-2 text-gray-400\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-base font-medium\",children:item.name}),/*#__PURE__*/_jsx(\"span\",{className:\"text-xs bg-gray-100 px-2 py-1 rounded-full\",children:\"Coming Soon\"})]}):/*#__PURE__*/_jsx(Link,{to:item.href,className:\"block px-3 py-2 text-base font-medium rounded-md \".concat(item.current?'bg-blue-100 text-blue-700':'text-gray-600 hover:text-gray-900 hover:bg-gray-100'),onClick:()=>setIsMenuOpen(false),children:item.name})},item.name))})})]})});};", "map": {"version": 3, "names": ["React", "useState", "Link", "useLocation", "Zap", "<PERSON><PERSON>", "X", "User", "Settings", "LogOut", "Crown", "<PERSON><PERSON><PERSON>", "jsx", "_jsx", "jsxs", "_jsxs", "Header", "isMenuOpen", "setIsMenuOpen", "isUserMenuOpen", "setIsUserMenuOpen", "location", "navigation", "name", "href", "current", "pathname", "comingSoon", "userNavigation", "icon", "highlight", "className", "children", "to", "map", "item", "concat", "onClick"], "sources": ["D:/repos-personal/repos/openapi-to-mcp/ui/src/components/layout/Header.tsx"], "sourcesContent": ["/**\r\n * Header component - Professional production-ready header\r\n */\r\n\r\nimport React, { useState } from 'react';\r\nimport { Link, useLocation } from 'react-router-dom';\r\nimport {\r\n  Zap,\r\n  Menu,\r\n  X,\r\n  User,\r\n  Settings,\r\n  LogOut,\r\n  Crown,\r\n  Sparkles\r\n} from 'lucide-react';\r\n\r\nexport const Header: React.FC = () => {\r\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\r\n  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false);\r\n  const location = useLocation();\r\n\r\n  const navigation = [\r\n    { name: 'Convert', href: '/convert', current: location.pathname === '/convert' },\r\n    { name: 'Chat', href: '/chat', current: location.pathname === '/chat', comingSoon: true },\r\n    { name: 'Tools', href: '/tools', current: location.pathname === '/tools', comingSoon: true },\r\n    { name: 'Playground', href: '/playground', current: location.pathname === '/playground', comingSoon: true },\r\n  ];\r\n\r\n  const userNavigation = [\r\n    { name: 'Your Profile', href: '/profile', icon: User },\r\n    { name: 'Settings', href: '/settings', icon: Settings },\r\n    { name: 'Upgrade to Pro', href: '/pricing', icon: Crown, highlight: true },\r\n    { name: 'Sign out', href: '/logout', icon: LogOut },\r\n  ];\r\n\r\n  return (\r\n    <header className=\"bg-white shadow-lg border-b border-gray-200 sticky top-0 z-50\">\r\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n        <div className=\"flex justify-between items-center h-16\">\r\n          {/* Logo */}\r\n          <div className=\"flex items-center\">\r\n            <Link to=\"/\" className=\"flex items-center space-x-2\">\r\n              <div className=\"flex items-center justify-center w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg\">\r\n                <Zap className=\"w-5 h-5 text-white\" />\r\n              </div>\r\n              <div className=\"flex flex-col\">\r\n                <span className=\"text-xl font-bold text-gray-900\">MCPify</span>\r\n                <span className=\"text-xs text-gray-500 -mt-1\">API to MCP Converter</span>\r\n              </div>\r\n            </Link>\r\n          </div>\r\n\r\n          {/* Desktop Navigation */}\r\n          <nav className=\"hidden md:flex space-x-1\">\r\n            {navigation.map((item) => (\r\n              <div key={item.name} className=\"relative\">\r\n                {item.comingSoon ? (\r\n                  <div className=\"relative group\">\r\n                    <span className=\"text-gray-400 px-3 py-2 text-sm font-medium cursor-not-allowed flex items-center space-x-1\">\r\n                      <span>{item.name}</span>\r\n                      <Sparkles className=\"w-3 h-3\" />\r\n                    </span>\r\n                    <div className=\"absolute top-full left-1/2 transform -translate-x-1/2 mt-1 px-2 py-1 bg-gray-800 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap\">\r\n                      Coming Soon\r\n                    </div>\r\n                  </div>\r\n                ) : (\r\n                  <Link\r\n                    to={item.href}\r\n                    className={`px-3 py-2 text-sm font-medium rounded-md transition-colors ${\r\n                      item.current\r\n                        ? 'bg-blue-100 text-blue-700'\r\n                        : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'\r\n                    }`}\r\n                  >\r\n                    {item.name}\r\n                  </Link>\r\n                )}\r\n              </div>\r\n            ))}\r\n          </nav>\r\n\r\n          {/* Right side */}\r\n          <div className=\"flex items-center space-x-4\">\r\n            {/* Upgrade Button */}\r\n            <Link\r\n              to=\"/pricing\"\r\n              className=\"hidden md:flex items-center space-x-1 bg-gradient-to-r from-blue-600 to-purple-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:from-blue-700 hover:to-purple-700 transition-all\"\r\n            >\r\n              <Crown className=\"w-4 h-4\" />\r\n              <span>Upgrade</span>\r\n            </Link>\r\n\r\n            {/* User Menu */}\r\n            <div className=\"relative\">\r\n              <button\r\n                onClick={() => setIsUserMenuOpen(!isUserMenuOpen)}\r\n                className=\"flex items-center space-x-2 text-gray-600 hover:text-gray-900 p-2 rounded-lg hover:bg-gray-100 transition-colors\"\r\n              >\r\n                <div className=\"w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center\">\r\n                  <User className=\"w-4 h-4 text-white\" />\r\n                </div>\r\n              </button>\r\n\r\n              {/* User Dropdown */}\r\n              {isUserMenuOpen && (\r\n                <div className=\"absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-50\">\r\n                  {userNavigation.map((item) => (\r\n                    <Link\r\n                      key={item.name}\r\n                      to={item.href}\r\n                      className={`flex items-center space-x-2 px-4 py-2 text-sm hover:bg-gray-100 transition-colors ${\r\n                        item.highlight ? 'text-blue-600 font-medium' : 'text-gray-700'\r\n                      }`}\r\n                      onClick={() => setIsUserMenuOpen(false)}\r\n                    >\r\n                      <item.icon className=\"w-4 h-4\" />\r\n                      <span>{item.name}</span>\r\n                    </Link>\r\n                  ))}\r\n                </div>\r\n              )}\r\n            </div>\r\n\r\n            {/* Mobile menu button */}\r\n            <button\r\n              onClick={() => setIsMenuOpen(!isMenuOpen)}\r\n              className=\"md:hidden p-2 rounded-lg text-gray-600 hover:text-gray-900 hover:bg-gray-100\"\r\n            >\r\n              {isMenuOpen ? <X className=\"w-5 h-5\" /> : <Menu className=\"w-5 h-5\" />}\r\n            </button>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Mobile Navigation */}\r\n        {isMenuOpen && (\r\n          <div className=\"md:hidden border-t border-gray-200 py-4\">\r\n            <div className=\"space-y-1\">\r\n              {navigation.map((item) => (\r\n                <div key={item.name}>\r\n                  {item.comingSoon ? (\r\n                    <div className=\"flex items-center justify-between px-3 py-2 text-gray-400\">\r\n                      <span className=\"text-base font-medium\">{item.name}</span>\r\n                      <span className=\"text-xs bg-gray-100 px-2 py-1 rounded-full\">Coming Soon</span>\r\n                    </div>\r\n                  ) : (\r\n                    <Link\r\n                      to={item.href}\r\n                      className={`block px-3 py-2 text-base font-medium rounded-md ${\r\n                        item.current\r\n                          ? 'bg-blue-100 text-blue-700'\r\n                          : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'\r\n                      }`}\r\n                      onClick={() => setIsMenuOpen(false)}\r\n                    >\r\n                      {item.name}\r\n                    </Link>\r\n                  )}\r\n                </div>\r\n              ))}\r\n            </div>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </header>\r\n  );\r\n};\r\n"], "mappings": "AAAA;AACA;AACA,GAEA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OAASC,IAAI,CAAEC,WAAW,KAAQ,kBAAkB,CACpD,OACEC,GAAG,CACHC,IAAI,CACJC,CAAC,CACDC,IAAI,CACJC,QAAQ,CACRC,MAAM,CACNC,KAAK,CACLC,QAAQ,KACH,cAAc,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEtB,MAAO,MAAM,CAAAC,MAAgB,CAAGA,CAAA,GAAM,CACpC,KAAM,CAACC,UAAU,CAAEC,aAAa,CAAC,CAAGjB,QAAQ,CAAC,KAAK,CAAC,CACnD,KAAM,CAACkB,cAAc,CAAEC,iBAAiB,CAAC,CAAGnB,QAAQ,CAAC,KAAK,CAAC,CAC3D,KAAM,CAAAoB,QAAQ,CAAGlB,WAAW,CAAC,CAAC,CAE9B,KAAM,CAAAmB,UAAU,CAAG,CACjB,CAAEC,IAAI,CAAE,SAAS,CAAEC,IAAI,CAAE,UAAU,CAAEC,OAAO,CAAEJ,QAAQ,CAACK,QAAQ,GAAK,UAAW,CAAC,CAChF,CAAEH,IAAI,CAAE,MAAM,CAAEC,IAAI,CAAE,OAAO,CAAEC,OAAO,CAAEJ,QAAQ,CAACK,QAAQ,GAAK,OAAO,CAAEC,UAAU,CAAE,IAAK,CAAC,CACzF,CAAEJ,IAAI,CAAE,OAAO,CAAEC,IAAI,CAAE,QAAQ,CAAEC,OAAO,CAAEJ,QAAQ,CAACK,QAAQ,GAAK,QAAQ,CAAEC,UAAU,CAAE,IAAK,CAAC,CAC5F,CAAEJ,IAAI,CAAE,YAAY,CAAEC,IAAI,CAAE,aAAa,CAAEC,OAAO,CAAEJ,QAAQ,CAACK,QAAQ,GAAK,aAAa,CAAEC,UAAU,CAAE,IAAK,CAAC,CAC5G,CAED,KAAM,CAAAC,cAAc,CAAG,CACrB,CAAEL,IAAI,CAAE,cAAc,CAAEC,IAAI,CAAE,UAAU,CAAEK,IAAI,CAAEtB,IAAK,CAAC,CACtD,CAAEgB,IAAI,CAAE,UAAU,CAAEC,IAAI,CAAE,WAAW,CAAEK,IAAI,CAAErB,QAAS,CAAC,CACvD,CAAEe,IAAI,CAAE,gBAAgB,CAAEC,IAAI,CAAE,UAAU,CAAEK,IAAI,CAAEnB,KAAK,CAAEoB,SAAS,CAAE,IAAK,CAAC,CAC1E,CAAEP,IAAI,CAAE,UAAU,CAAEC,IAAI,CAAE,SAAS,CAAEK,IAAI,CAAEpB,MAAO,CAAC,CACpD,CAED,mBACEI,IAAA,WAAQkB,SAAS,CAAC,+DAA+D,CAAAC,QAAA,cAC/EjB,KAAA,QAAKgB,SAAS,CAAC,wCAAwC,CAAAC,QAAA,eACrDjB,KAAA,QAAKgB,SAAS,CAAC,wCAAwC,CAAAC,QAAA,eAErDnB,IAAA,QAAKkB,SAAS,CAAC,mBAAmB,CAAAC,QAAA,cAChCjB,KAAA,CAACb,IAAI,EAAC+B,EAAE,CAAC,GAAG,CAACF,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAClDnB,IAAA,QAAKkB,SAAS,CAAC,kGAAkG,CAAAC,QAAA,cAC/GnB,IAAA,CAACT,GAAG,EAAC2B,SAAS,CAAC,oBAAoB,CAAE,CAAC,CACnC,CAAC,cACNhB,KAAA,QAAKgB,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BnB,IAAA,SAAMkB,SAAS,CAAC,iCAAiC,CAAAC,QAAA,CAAC,QAAM,CAAM,CAAC,cAC/DnB,IAAA,SAAMkB,SAAS,CAAC,6BAA6B,CAAAC,QAAA,CAAC,sBAAoB,CAAM,CAAC,EACtE,CAAC,EACF,CAAC,CACJ,CAAC,cAGNnB,IAAA,QAAKkB,SAAS,CAAC,0BAA0B,CAAAC,QAAA,CACtCV,UAAU,CAACY,GAAG,CAAEC,IAAI,eACnBtB,IAAA,QAAqBkB,SAAS,CAAC,UAAU,CAAAC,QAAA,CACtCG,IAAI,CAACR,UAAU,cACdZ,KAAA,QAAKgB,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7BjB,KAAA,SAAMgB,SAAS,CAAC,4FAA4F,CAAAC,QAAA,eAC1GnB,IAAA,SAAAmB,QAAA,CAAOG,IAAI,CAACZ,IAAI,CAAO,CAAC,cACxBV,IAAA,CAACF,QAAQ,EAACoB,SAAS,CAAC,SAAS,CAAE,CAAC,EAC5B,CAAC,cACPlB,IAAA,QAAKkB,SAAS,CAAC,oLAAoL,CAAAC,QAAA,CAAC,aAEpM,CAAK,CAAC,EACH,CAAC,cAENnB,IAAA,CAACX,IAAI,EACH+B,EAAE,CAAEE,IAAI,CAACX,IAAK,CACdO,SAAS,+DAAAK,MAAA,CACPD,IAAI,CAACV,OAAO,CACR,2BAA2B,CAC3B,qDAAqD,CACxD,CAAAO,QAAA,CAEFG,IAAI,CAACZ,IAAI,CACN,CACP,EAtBOY,IAAI,CAACZ,IAuBV,CACN,CAAC,CACC,CAAC,cAGNR,KAAA,QAAKgB,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAE1CjB,KAAA,CAACb,IAAI,EACH+B,EAAE,CAAC,UAAU,CACbF,SAAS,CAAC,+LAA+L,CAAAC,QAAA,eAEzMnB,IAAA,CAACH,KAAK,EAACqB,SAAS,CAAC,SAAS,CAAE,CAAC,cAC7BlB,IAAA,SAAAmB,QAAA,CAAM,SAAO,CAAM,CAAC,EAChB,CAAC,cAGPjB,KAAA,QAAKgB,SAAS,CAAC,UAAU,CAAAC,QAAA,eACvBnB,IAAA,WACEwB,OAAO,CAAEA,CAAA,GAAMjB,iBAAiB,CAAC,CAACD,cAAc,CAAE,CAClDY,SAAS,CAAC,kHAAkH,CAAAC,QAAA,cAE5HnB,IAAA,QAAKkB,SAAS,CAAC,oGAAoG,CAAAC,QAAA,cACjHnB,IAAA,CAACN,IAAI,EAACwB,SAAS,CAAC,oBAAoB,CAAE,CAAC,CACpC,CAAC,CACA,CAAC,CAGRZ,cAAc,eACbN,IAAA,QAAKkB,SAAS,CAAC,2FAA2F,CAAAC,QAAA,CACvGJ,cAAc,CAACM,GAAG,CAAEC,IAAI,eACvBpB,KAAA,CAACb,IAAI,EAEH+B,EAAE,CAAEE,IAAI,CAACX,IAAK,CACdO,SAAS,sFAAAK,MAAA,CACPD,IAAI,CAACL,SAAS,CAAG,2BAA2B,CAAG,eAAe,CAC7D,CACHO,OAAO,CAAEA,CAAA,GAAMjB,iBAAiB,CAAC,KAAK,CAAE,CAAAY,QAAA,eAExCnB,IAAA,CAACsB,IAAI,CAACN,IAAI,EAACE,SAAS,CAAC,SAAS,CAAE,CAAC,cACjClB,IAAA,SAAAmB,QAAA,CAAOG,IAAI,CAACZ,IAAI,CAAO,CAAC,GARnBY,IAAI,CAACZ,IASN,CACP,CAAC,CACC,CACN,EACE,CAAC,cAGNV,IAAA,WACEwB,OAAO,CAAEA,CAAA,GAAMnB,aAAa,CAAC,CAACD,UAAU,CAAE,CAC1Cc,SAAS,CAAC,8EAA8E,CAAAC,QAAA,CAEvFf,UAAU,cAAGJ,IAAA,CAACP,CAAC,EAACyB,SAAS,CAAC,SAAS,CAAE,CAAC,cAAGlB,IAAA,CAACR,IAAI,EAAC0B,SAAS,CAAC,SAAS,CAAE,CAAC,CAChE,CAAC,EACN,CAAC,EACH,CAAC,CAGLd,UAAU,eACTJ,IAAA,QAAKkB,SAAS,CAAC,yCAAyC,CAAAC,QAAA,cACtDnB,IAAA,QAAKkB,SAAS,CAAC,WAAW,CAAAC,QAAA,CACvBV,UAAU,CAACY,GAAG,CAAEC,IAAI,eACnBtB,IAAA,QAAAmB,QAAA,CACGG,IAAI,CAACR,UAAU,cACdZ,KAAA,QAAKgB,SAAS,CAAC,2DAA2D,CAAAC,QAAA,eACxEnB,IAAA,SAAMkB,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAEG,IAAI,CAACZ,IAAI,CAAO,CAAC,cAC1DV,IAAA,SAAMkB,SAAS,CAAC,4CAA4C,CAAAC,QAAA,CAAC,aAAW,CAAM,CAAC,EAC5E,CAAC,cAENnB,IAAA,CAACX,IAAI,EACH+B,EAAE,CAAEE,IAAI,CAACX,IAAK,CACdO,SAAS,qDAAAK,MAAA,CACPD,IAAI,CAACV,OAAO,CACR,2BAA2B,CAC3B,qDAAqD,CACxD,CACHY,OAAO,CAAEA,CAAA,GAAMnB,aAAa,CAAC,KAAK,CAAE,CAAAc,QAAA,CAEnCG,IAAI,CAACZ,IAAI,CACN,CACP,EAlBOY,IAAI,CAACZ,IAmBV,CACN,CAAC,CACC,CAAC,CACH,CACN,EACE,CAAC,CACA,CAAC,CAEb,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}