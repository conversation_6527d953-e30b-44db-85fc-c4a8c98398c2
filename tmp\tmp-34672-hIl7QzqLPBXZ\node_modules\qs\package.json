{"name": "qs", "description": "A querystring parser that supports nesting and arrays, with a depth limit", "homepage": "https://github.com/ljharb/qs", "version": "6.13.0", "repository": {"type": "git", "url": "https://github.com/ljharb/qs.git"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "main": "lib/index.js", "sideEffects": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes"}], "keywords": ["querystring", "qs", "query", "url", "parse", "stringify"], "engines": {"node": ">=0.6"}, "dependencies": {"side-channel": "^1.0.6"}, "devDependencies": {"@browserify/envify": "^6.0.0", "@browserify/uglifyify": "^6.0.0", "@ljharb/eslint-config": "^21.1.1", "browserify": "^16.5.2", "bundle-collapser": "^1.4.0", "common-shakeify": "~1.0.0", "eclint": "^2.8.1", "es-value-fixtures": "^1.4.2", "eslint": "=8.8.0", "evalmd": "^0.0.19", "for-each": "^0.3.3", "glob": "=10.3.7", "has-override-mistake": "^1.0.1", "has-property-descriptors": "^1.0.2", "has-symbols": "^1.0.3", "iconv-lite": "^0.5.1", "in-publish": "^2.0.1", "jackspeak": "=2.1.1", "mkdirp": "^0.5.5", "mock-property": "^1.0.3", "module-deps": "^6.2.3", "npmignore": "^0.3.1", "nyc": "^10.3.2", "object-inspect": "^1.13.2", "qs-iconv": "^1.0.4", "safe-publish-latest": "^2.0.0", "safer-buffer": "^2.1.2", "tape": "^5.8.1", "unassertify": "^3.0.1"}, "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated && npm run dist", "prepublishOnly": "safe-publish-latest", "prepublish": "not-in-publish || npm run prepublishOnly", "pretest": "npm run --silent readme && npm run --silent lint", "test": "npm run tests-only", "tests-only": "nyc tape 'test/**/*.js'", "posttest": "npx npm@'>=10.2' audit --production", "readme": "evalmd README.md", "postlint": "eclint check $(git ls-files | xargs find 2> /dev/null | grep -vE 'node_modules|\\.git' | grep -v dist/)", "lint": "eslint --ext=js,mjs .", "dist": "mkdirp dist && browserify --standalone Qs -g unassertify -g @browserify/envify -g [@browserify/uglifyify --mangle.keep_fnames --compress.keep_fnames --format.indent_level=1 --compress.arrows=false --compress.passes=4 --compress.typeofs=false] -p common-shakeify -p bundle-collapser/plugin lib/index.js > dist/qs.js"}, "license": "BSD-3-<PERSON><PERSON>", "publishConfig": {"ignore": ["!dist/*", "bower.json", "component.json", ".github/workflows", "logos", "tea.yaml"]}}