{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst CheckCircle = createLucideIcon(\"CheckCircle\", [[\"path\", {\n  d: \"M22 11.08V12a10 10 0 1 1-5.93-9.14\",\n  key: \"g774vq\"\n}], [\"polyline\", {\n  points: \"22 4 12 14.01 9 11.01\",\n  key: \"6xbx8j\"\n}]]);\nexport { CheckCircle as default };", "map": {"version": 3, "names": ["CheckCircle", "createLucideIcon", "d", "key", "points"], "sources": ["D:\\repos-personal\\repos\\openapi-to-mcp\\ui\\node_modules\\lucide-react\\src\\icons\\check-circle.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name CheckCircle\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjIgMTEuMDhWMTJhMTAgMTAgMCAxIDEtNS45My05LjE0IiAvPgogIDxwb2x5bGluZSBwb2ludHM9IjIyIDQgMTIgMTQuMDEgOSAxMS4wMSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/check-circle\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst CheckCircle = createLucideIcon('CheckCircle', [\n  ['path', { d: 'M22 11.08V12a10 10 0 1 1-5.93-9.14', key: 'g774vq' }],\n  ['polyline', { points: '22 4 12 14.01 9 11.01', key: '6xbx8j' }],\n]);\n\nexport default CheckCircle;\n"], "mappings": ";;;;;AAaM,MAAAA,WAAA,GAAcC,gBAAA,CAAiB,aAAe,GAClD,CAAC,MAAQ;EAAEC,CAAA,EAAG,oCAAsC;EAAAC,GAAA,EAAK;AAAA,CAAU,GACnE,CAAC,UAAY;EAAEC,MAAA,EAAQ,uBAAyB;EAAAD,GAAA,EAAK;AAAA,CAAU,EAChE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}