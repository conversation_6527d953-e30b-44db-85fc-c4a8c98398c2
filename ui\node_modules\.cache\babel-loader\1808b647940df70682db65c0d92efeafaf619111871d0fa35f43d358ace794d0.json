{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst ListStart = createLucideIcon(\"ListStart\", [[\"path\", {\n  d: \"M16 12H3\",\n  key: \"1a2rj7\"\n}], [\"path\", {\n  d: \"M16 18H3\",\n  key: \"12xzn7\"\n}], [\"path\", {\n  d: \"M10 6H3\",\n  key: \"lf8lx7\"\n}], [\"path\", {\n  d: \"M21 18V8a2 2 0 0 0-2-2h-5\",\n  key: \"1hghli\"\n}], [\"path\", {\n  d: \"m16 8-2-2 2-2\",\n  key: \"160uvd\"\n}]]);\nexport { ListStart as default };", "map": {"version": 3, "names": ["ListStart", "createLucideIcon", "d", "key"], "sources": ["D:\\repos-personal\\repos\\openapi-to-mcp\\ui\\node_modules\\lucide-react\\src\\icons\\list-start.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name ListStart\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTYgMTJIMyIgLz4KICA8cGF0aCBkPSJNMTYgMThIMyIgLz4KICA8cGF0aCBkPSJNMTAgNkgzIiAvPgogIDxwYXRoIGQ9Ik0yMSAxOFY4YTIgMiAwIDAgMC0yLTJoLTUiIC8+CiAgPHBhdGggZD0ibTE2IDgtMi0yIDItMiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/list-start\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ListStart = createLucideIcon('ListStart', [\n  ['path', { d: 'M16 12H3', key: '1a2rj7' }],\n  ['path', { d: 'M16 18H3', key: '12xzn7' }],\n  ['path', { d: 'M10 6H3', key: 'lf8lx7' }],\n  ['path', { d: 'M21 18V8a2 2 0 0 0-2-2h-5', key: '1hghli' }],\n  ['path', { d: 'm16 8-2-2 2-2', key: '160uvd' }],\n]);\n\nexport default ListStart;\n"], "mappings": ";;;;;AAaM,MAAAA,SAAA,GAAYC,gBAAA,CAAiB,WAAa,GAC9C,CAAC,MAAQ;EAAEC,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,2BAA6B;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1D,CAAC,MAAQ;EAAED,CAAA,EAAG,eAAiB;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC/C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}