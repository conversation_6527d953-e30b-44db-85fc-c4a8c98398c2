{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst BarChart3 = createLucideIcon(\"BarChart3\", [[\"path\", {\n  d: \"M3 3v18h18\",\n  key: \"1s2lah\"\n}], [\"path\", {\n  d: \"M18 17V9\",\n  key: \"2bz60n\"\n}], [\"path\", {\n  d: \"M13 17V5\",\n  key: \"1frdt8\"\n}], [\"path\", {\n  d: \"M8 17v-3\",\n  key: \"17ska0\"\n}]]);\nexport { BarChart3 as default };", "map": {"version": 3, "names": ["BarChart3", "createLucideIcon", "d", "key"], "sources": ["D:\\repos-personal\\repos\\openapi-to-mcp\\ui\\node_modules\\lucide-react\\src\\icons\\bar-chart-3.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name BarChart3\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMyAzdjE4aDE4IiAvPgogIDxwYXRoIGQ9Ik0xOCAxN1Y5IiAvPgogIDxwYXRoIGQ9Ik0xMyAxN1Y1IiAvPgogIDxwYXRoIGQ9Ik04IDE3di0zIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/bar-chart-3\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst BarChart3 = createLucideIcon('BarChart3', [\n  ['path', { d: 'M3 3v18h18', key: '1s2lah' }],\n  ['path', { d: 'M18 17V9', key: '2bz60n' }],\n  ['path', { d: 'M13 17V5', key: '1frdt8' }],\n  ['path', { d: 'M8 17v-3', key: '17ska0' }],\n]);\n\nexport default BarChart3;\n"], "mappings": ";;;;;AAaM,MAAAA,SAAA,GAAYC,gBAAA,CAAiB,WAAa,GAC9C,CAAC,MAAQ;EAAEC,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC1C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}