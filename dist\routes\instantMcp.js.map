{"version": 3, "file": "instantMcp.js", "sourceRoot": "", "sources": ["../../src/routes/instantMcp.ts"], "names": [], "mappings": ";;;;;AAAA,sDAA8B;AAC9B,sDAAsD;AACtD,aAAa;AACb,4DAAoC;AACpC,+BAAoC;AACpC,8EAAsD;AACtD,0EAAiD;AACjD,6DAA0D;AAC1D,uEAAoE;AAEpE,MAAM,MAAM,GAAG,iBAAO,CAAC,MAAM,EAAE,CAAC;AAEhC,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;IAClC,MAAM,EAAE,UAAU,EAAE,GAAG,GAAG,CAAC,IAAI,CAAC;IAChC,IAAI,CAAC,UAAU;QAAE,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,oBAAoB,EAAE,CAAC,CAAC;IAE9E,IAAI,CAAC;QACH,MAAM,QAAQ,GAAG,GAAG,IAAA,SAAM,GAAE,IAAI,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC;QAE7C,8BAA8B;QAC9B,MAAM,UAAU,GAAG,MAAM,KAAK,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;QAC/D,MAAM,MAAM,GAAG,IAAI,uBAAY,EAAE,CAAC;QAClC,MAAM,MAAM,GAAG,UAAU,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,UAAU,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QAC3E,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,eAAe,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;QAEhE,8BAA8B;QAC9B,MAAM,IAAI,GAAG,MAAM,oBAAU,CAAC,cAAc,EAAE,CAAC;QAC/C,MAAM,MAAM,GAAG;YACb,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,OAAO,CAAC,gBAAgB,EAAE,GAAG,CAAC,CAAC,WAAW,EAAE,IAAI,aAAa;YAC5F,OAAO,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,IAAI,OAAO;YAC7C,IAAI;YACJ,OAAO,EAAE,MAAM,CAAC,OAAO,IAAI,oBAAoB,IAAI,EAAE;YACrD,WAAW,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,WAAW,IAAI,oBAAoB;YAClE,OAAO,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,EAAE,IAAI,IAAI,KAAK;YACjD,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,EAAE,IAAI,IAAI,gBAAgB;YAC3D,SAAS,EAAE,oBAAoB,QAAQ,EAAE;SAC1C,CAAC;QAEF,wBAAwB;QACxB,MAAM,iBAAiB,GAAG,IAAI,2CAAoB,EAAE,CAAC;QACrD,MAAM,QAAQ,GAAG,iBAAiB,CAAC,gBAAgB,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QAEpE,wBAAwB;QACxB,MAAM,SAAS,GAAG,IAAI,yBAAe,EAAE,CAAC;QACxC,MAAM,KAAK,GAAG,MAAM,SAAS,CAAC,cAAc,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QAE7D,6BAA6B;QAC7B,MAAM,eAAe,GAAG,IAAI,iCAAe,EAAE,CAAC;QAC9C,MAAM,UAAU,GAAG,MAAM,eAAe,CAAC,YAAY,CAAC,QAAQ,EAAE;YAC9D,QAAQ;YACR,KAAK;YACL,MAAM;SACP,CAAC,CAAC;QAEH,+BAA+B;QAC/B,MAAM,UAAU,GAAG,MAAM,eAAe,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;QAEnE,GAAG,CAAC,IAAI,CAAC;YACP,OAAO,EAAE,IAAI;YACb,QAAQ;YACR,UAAU,EAAE,QAAQ,EAAE,6BAA6B;YACnD,WAAW,EAAE,iBAAiB,QAAQ,EAAE;YACxC,UAAU;YACV,OAAO,EAAE,wEAAwE;YACjF,MAAM,EAAE;gBACN,IAAI,EAAE,MAAM,CAAC,IAAI;gBACjB,OAAO,EAAE,MAAM,CAAC,OAAO;gBACvB,IAAI,EAAE,MAAM,CAAC,IAAI;gBACjB,OAAO,EAAE,MAAM,CAAC,OAAO;aACxB;YACD,KAAK,EAAE;gBACL,SAAS,EAAE,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC,CAAC,MAAM;gBACtD,KAAK,EAAE,QAAQ,CAAC,KAAK,CAAC,MAAM;aAC7B;SACF,CAAC,CAAC;IACL,CAAC;IAAC,OAAO,GAAQ,EAAE,CAAC;QAClB,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,GAAG,CAAC,OAAO,IAAI,GAAG,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;IACjE,CAAC;AACH,CAAC,CAAC,CAAC;AAEH,kBAAe,MAAM,CAAC"}