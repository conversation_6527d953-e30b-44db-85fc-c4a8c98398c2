{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst ChevronUpSquare = createLucideIcon(\"ChevronUpSquare\", [[\"rect\", {\n  width: \"18\",\n  height: \"18\",\n  x: \"3\",\n  y: \"3\",\n  rx: \"2\",\n  key: \"afitv7\"\n}], [\"path\", {\n  d: \"m8 14 4-4 4 4\",\n  key: \"fy2ptz\"\n}]]);\nexport { ChevronUpSquare as default };", "map": {"version": 3, "names": ["ChevronUpSquare", "createLucideIcon", "width", "height", "x", "y", "rx", "key", "d"], "sources": ["D:\\repos-personal\\repos\\openapi-to-mcp\\ui\\node_modules\\lucide-react\\src\\icons\\chevron-up-square.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name ChevronUpSquare\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMTgiIGhlaWdodD0iMTgiIHg9IjMiIHk9IjMiIHJ4PSIyIiAvPgogIDxwYXRoIGQ9Im04IDE0IDQtNCA0IDQiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/chevron-up-square\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ChevronUpSquare = createLucideIcon('ChevronUpSquare', [\n  [\n    'rect',\n    { width: '18', height: '18', x: '3', y: '3', rx: '2', key: 'afitv7' },\n  ],\n  ['path', { d: 'm8 14 4-4 4 4', key: 'fy2ptz' }],\n]);\n\nexport default ChevronUpSquare;\n"], "mappings": ";;;;;AAaM,MAAAA,eAAA,GAAkBC,gBAAA,CAAiB,iBAAmB,GAC1D,CACE,QACA;EAAEC,KAAA,EAAO,IAAM;EAAAC,MAAA,EAAQ,IAAM;EAAAC,CAAA,EAAG,GAAK;EAAAC,CAAA,EAAG,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,GAAA,EAAK;AAAS,EACtE,EACA,CAAC,MAAQ;EAAEC,CAAA,EAAG,eAAiB;EAAAD,GAAA,EAAK;AAAA,CAAU,EAC/C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}