"use strict";
/**
 * MCP Server Manager - Handles multiple running MCP servers
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.ServerManager = void 0;
const fs = __importStar(require("fs-extra"));
const path = __importStar(require("path"));
const child_process_1 = require("child_process");
const events_1 = require("events");
class ServerManager extends events_1.EventEmitter {
    constructor() {
        super();
        this.servers = new Map();
        this.processes = new Map();
        this.basePort = 8000;
        this.maxServers = 10;
        this.serversDir = path.join(process.cwd(), 'generated-servers');
        this.ensureServersDirectory();
        this.startHealthCheckInterval();
    }
    /**
     * Create and start a new MCP server
     */
    async createServer(name, openApiUrl, baseUrl, config) {
        const id = this.generateServerId(name);
        const port = this.getNextAvailablePort();
        if (this.servers.size >= this.maxServers) {
            throw new Error(`Maximum number of servers (${this.maxServers}) reached`);
        }
        const serverInfo = {
            id,
            name,
            port,
            status: 'starting',
            baseUrl,
            openApiUrl,
            createdAt: new Date()
        };
        this.servers.set(id, serverInfo);
        try {
            // Generate server files
            await this.generateServerFiles(id, name, openApiUrl, baseUrl, config);
            // Start the server process
            await this.startServerProcess(id);
            this.emit('serverCreated', serverInfo);
            return serverInfo;
        }
        catch (error) {
            serverInfo.status = 'error';
            serverInfo.errorMessage = error instanceof Error ? error.message : String(error);
            this.emit('serverError', serverInfo, error);
            throw error;
        }
    }
    /**
     * Stop a running MCP server
     */
    async stopServer(id) {
        const server = this.servers.get(id);
        if (!server) {
            throw new Error(`Server ${id} not found`);
        }
        const process = this.processes.get(id);
        if (process) {
            process.kill('SIGTERM');
            this.processes.delete(id);
        }
        server.status = 'stopped';
        this.emit('serverStopped', server);
    }
    /**
     * Restart a MCP server
     */
    async restartServer(id) {
        await this.stopServer(id);
        await new Promise(resolve => setTimeout(resolve, 2000)); // Wait 2 seconds
        await this.startServerProcess(id);
    }
    /**
     * Get all servers
     */
    getAllServers() {
        return Array.from(this.servers.values());
    }
    /**
     * Get server by ID
     */
    getServer(id) {
        return this.servers.get(id);
    }
    /**
     * Delete a server completely
     */
    async deleteServer(id) {
        await this.stopServer(id);
        // Remove server files
        const serverDir = path.join(this.serversDir, id);
        if (await fs.pathExists(serverDir)) {
            await fs.remove(serverDir);
        }
        this.servers.delete(id);
        this.emit('serverDeleted', id);
    }
    /**
     * Generate server files using the OpenAPI generator
     */
    async generateServerFiles(id, name, openApiUrl, baseUrl, config) {
        const serverDir = path.join(this.serversDir, id);
        await fs.ensureDir(serverDir);
        // Import the generator (assuming it's available)
        const { OpenAPIParser } = await Promise.resolve().then(() => __importStar(require('../core/openapiParser')));
        const { ServerGenerator } = await Promise.resolve().then(() => __importStar(require('../core/serverGenerator')));
        const parser = new OpenAPIParser();
        const generator = new ServerGenerator();
        // Parse OpenAPI spec
        const parsed = await parser.parseFromURL(openApiUrl);
        // Generate server files
        const serverConfig = {
            name: name,
            version: '1.0.0',
            description: `Generated MCP server for ${name}`,
            port: this.getNextAvailablePort(),
            baseUrl: baseUrl,
            ...config
        };
        const files = await generator.generateServer(parsed, serverConfig);
        // Write files to disk
        for (const [filePath, content] of Object.entries(files)) {
            const fullPath = path.join(serverDir, filePath);
            await fs.ensureDir(path.dirname(fullPath));
            await fs.writeFile(fullPath, content);
        }
        // Create Dockerfile for the server
        const dockerfile = this.generateDockerfile();
        await fs.writeFile(path.join(serverDir, 'Dockerfile'), dockerfile);
    }
    /**
     * Start server process
     */
    async startServerProcess(id) {
        const server = this.servers.get(id);
        if (!server)
            throw new Error(`Server ${id} not found`);
        const serverDir = path.join(this.serversDir, id);
        // Build the server first
        const buildProcess = (0, child_process_1.spawn)('npm', ['run', 'build'], {
            cwd: serverDir,
            stdio: 'pipe'
        });
        await new Promise((resolve, reject) => {
            buildProcess.on('close', (code) => {
                if (code === 0)
                    resolve(void 0);
                else
                    reject(new Error(`Build failed with code ${code}`));
            });
        });
        // Start the server
        const serverProcess = (0, child_process_1.spawn)('npm', ['start'], {
            cwd: serverDir,
            stdio: 'pipe',
            env: {
                ...process.env,
                PORT: server.port.toString(),
                BASE_URL: server.baseUrl,
                NODE_ENV: 'production'
            }
        });
        serverProcess.stdout?.on('data', (data) => {
            console.log(`[${id}] ${data.toString()}`);
        });
        serverProcess.stderr?.on('data', (data) => {
            console.error(`[${id}] ${data.toString()}`);
        });
        serverProcess.on('close', (code) => {
            console.log(`[${id}] Process exited with code ${code}`);
            server.status = code === 0 ? 'stopped' : 'error';
            this.processes.delete(id);
            this.emit('serverStopped', server);
        });
        serverProcess.on('error', (error) => {
            console.error(`[${id}] Process error:`, error);
            server.status = 'error';
            server.errorMessage = error.message;
            this.emit('serverError', server, error);
        });
        this.processes.set(id, serverProcess);
        server.pid = serverProcess.pid;
        server.status = 'running';
        // Wait a bit for the server to start
        await new Promise(resolve => setTimeout(resolve, 3000));
    }
    /**
     * Generate unique server ID
     */
    generateServerId(name) {
        const sanitized = name.toLowerCase().replace(/[^a-z0-9]/g, '-');
        const timestamp = Date.now();
        return `${sanitized}-${timestamp}`;
    }
    /**
     * Get next available port
     */
    getNextAvailablePort() {
        const usedPorts = Array.from(this.servers.values()).map(s => s.port);
        let port = this.basePort;
        while (usedPorts.includes(port)) {
            port++;
        }
        return port;
    }
    /**
     * Ensure servers directory exists
     */
    async ensureServersDirectory() {
        await fs.ensureDir(this.serversDir);
    }
    /**
     * Start health check interval
     */
    startHealthCheckInterval() {
        setInterval(async () => {
            for (const [id, server] of this.servers) {
                if (server.status === 'running') {
                    try {
                        const response = await fetch(`http://localhost:${server.port}/health`);
                        if (response.ok) {
                            server.lastHealthCheck = new Date();
                        }
                        else {
                            server.status = 'error';
                            server.errorMessage = `Health check failed: ${response.status}`;
                        }
                    }
                    catch (error) {
                        server.status = 'error';
                        server.errorMessage = `Health check failed: ${error}`;
                    }
                }
            }
        }, 30000); // Check every 30 seconds
    }
    /**
     * Generate Dockerfile for individual MCP server
     */
    generateDockerfile() {
        return `FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

EXPOSE 8000

CMD ["npm", "start"]`;
    }
}
exports.ServerManager = ServerManager;
exports.default = ServerManager;
//# sourceMappingURL=serverManager.js.map