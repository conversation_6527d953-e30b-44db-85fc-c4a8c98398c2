{"version": 3, "file": "ts-internals.js", "sourceRoot": "", "sources": ["../src/ts-internals.ts"], "names": [], "mappings": ";;;AAAA,+BAA2C;AAC3C,iCAAsE;AAItE,gBAAgB;AACH,QAAA,iBAAiB,GAAG,IAAA,mBAAY,EAAC,yBAAyB,CAAC,CAAC;AACzE;;;;;;;;GAQG;AACH,SAAS,yBAAyB,CAAC,GAAa;IAC9C,MAAM,EAAE,GAAG,GAA4B,CAAC;IACxC;;;OAGG;IACH,SAAS,oBAAoB,CAC3B,cAAsB,EACtB,IAAyB,EACzB,QAAgB,EAChB,MAAgC,EAChC,gBAGmB;QAEnB,cAAc,GAAG,IAAA,uBAAgB,EAAC,cAAc,CAAC,CAAC;QAClD,IACE,gBAAgB,CAAC,cAAc,CAAC;YAChC,UAAU,CAAC,cAAc,EAAE,IAAI,CAAC;YAChC,UAAU,CAAC,cAAc,EAAE,KAAK,CAAC,EACjC;YACA,IAAI,kBAAkB,GAAG,yBAAyB,CAChD,cAAc,EACd,QAAQ,CACT,CAAC;YACF,IACE,CAAC,IAAI,CAAC,UAAU,CAAC,kBAAkB,CAAC;gBACpC,CAAC,QAAQ,CAAC,kBAAkB,EAAE,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,EAChD;gBACA,kBAAkB,GAAG,GAAG,kBAAkB,OAAO,CAAC;gBAClD,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,kBAAkB,CAAC,EAAE;oBACxC,MAAM,CAAC,IAAI,CACT,gBAAgB,CAAC,EAAE,CAAC,WAAW,CAAC,gBAAgB,EAAE,cAAc,CAAC,CAClE,CAAC;oBACF,OAAO,SAAS,CAAC;iBAClB;aACF;YACD,OAAO,kBAAkB,CAAC;SAC3B;QACD,qEAAqE;QACrE,MAAM,UAAU,GAAG,IAAA,mBAAY,EAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QACrD,MAAM,QAAQ,GAAG,EAAE,CAAC,sBAAsB,CACxC,cAAc,EACd,YAAY,CAAC,QAAQ,EAAE,eAAe,CAAC,EACvC,EAAE,gBAAgB,EAAE,EAAE,CAAC,oBAAoB,CAAC,MAAM,EAAE,EACpD,IAAI;QACJ,SAAS,CAAC,SAAS;QACnB,eAAe,CAAC,SAAS;QACzB,8BAA8B,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI;QAC5D,kBAAkB,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CACjD,CAAC;QACF,IAAI,QAAQ,CAAC,cAAc,EAAE;YAC3B,OAAO,QAAQ,CAAC,cAAc,CAAC,gBAAgB,CAAC;SACjD;QACD,MAAM,CAAC,IAAI,CACT,gBAAgB,CAAC,EAAE,CAAC,WAAW,CAAC,gBAAgB,EAAE,cAAc,CAAC,CAClE,CAAC;QACF,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,OAAO,EAAE,oBAAoB,EAAE,CAAC;AAClC,CAAC;AAED,oFAAoF;AACpF,SAAS,gBAAgB,CAAC,IAAY;IACpC,OAAO,IAAA,iBAAU,EAAC,IAAI,CAAC,CAAC;AAC1B,CAAC;AACD,SAAS,YAAY,CAAC,IAAY,EAAE,GAAG,KAA6B;IAClE,OAAO,IAAA,uBAAgB,EACrB,IAAA,cAAO,EAAC,IAAI,EAAE,GAAI,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAc,CAAC,CAC7D,CAAC;AACJ,CAAC;AACD,SAAS,yBAAyB,CAChC,QAAgB,EAChB,gBAAoC;IAEpC,OAAO,IAAA,uBAAgB,EACrB,gBAAgB,IAAI,IAAI;QACtB,CAAC,CAAC,IAAA,cAAO,EAAC,gBAAiB,EAAE,QAAQ,CAAC;QACtC,CAAC,CAAC,IAAA,cAAO,EAAC,QAAQ,CAAC,CACtB,CAAC;AACJ,CAAC;AAED,SAAS,UAAU,CAAC,GAAW,EAAE,MAAc;IAC7C,OAAO,GAAG,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC;AAC1C,CAAC;AAED,SAAS,QAAQ,CAAC,GAAW,EAAE,MAAc;IAC3C,MAAM,WAAW,GAAG,GAAG,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;IAC/C,OAAO,WAAW,IAAI,CAAC,IAAI,GAAG,CAAC,OAAO,CAAC,MAAM,EAAE,WAAW,CAAC,KAAK,WAAW,CAAC;AAC9E,CAAC;AACD,6FAA6F;AAC7F,8FAA8F;AAC9F,SAAS;AACT,MAAM,wBAAwB,GAAG,YAAY,CAAC;AAE9C;;;GAGG;AACH,SAAgB,kBAAkB,CAAC,IAAY,EAAE,QAAgB;IAC/D,MAAM,OAAO,GAAG,IAAI,IAAI,qBAAqB,CAAC,IAAI,EAAE,QAAQ,EAAE,cAAc,CAAC,CAAC;IAC9E,OAAO,OAAO,IAAI,KAAK,OAAO,IAAI,OAAO,EAAE,CAAC;AAC9C,CAAC;AAHD,gDAGC;AACD,SAAS,qBAAqB,CAC5B,IAAY,EACZ,QAAgB,EAChB,EACE,2BAA2B,EAC3B,2BAA2B,EAC3B,wBAAwB,GACR;IAElB,IAAI,UAAU,GAAG,EAAE,CAAC;IACpB,IAAI,mBAAmB,GAAG,KAAK,CAAC;IAChC,MAAM,UAAU,GAAG,2BAA2B,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;IAC/D,MAAM,aAAa,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC;IAEvC,6EAA6E;IAC7E,mDAAmD;IACnD,UAAU,CAAC,CAAC,CAAC,GAAG,gCAAgC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;IAEhE,IAAI,cAAc,CAAC,aAAa,CAAC,EAAE;QACjC,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;KAC5B;IAED,IAAI,aAAa,GAAG,CAAC,CAAC;IACtB,KAAK,IAAI,SAAS,IAAI,UAAU,EAAE;QAChC,IAAI,SAAS,KAAK,IAAI,EAAE;YACtB,UAAU,IAAI,2BAA2B,CAAC;SAC3C;aAAM;YACL,IAAI,mBAAmB,EAAE;gBACvB,UAAU,IAAI,kBAAkB,CAAC;aAClC;YACD,UAAU,IAAI,SAAS,CAAC,OAAO,CAC7B,wBAAwB,EACxB,wBAAwB,CACzB,CAAC;SACH;QAED,mBAAmB,GAAG,IAAI,CAAC;KAC5B;IAED,OAAO,aAAa,GAAG,CAAC,EAAE;QACxB,UAAU,IAAI,IAAI,CAAC;QACnB,aAAa,EAAE,CAAC;KACjB;IAED,OAAO,UAAU,CAAC;AACpB,CAAC;AAMD,MAAM,kBAAkB,GAAoB;IAC1C,2BAA2B,EAAE,OAAO;IACpC;;;OAGG;IACH,2BAA2B,EAAE,iBAAiB;IAC9C,wBAAwB,EAAE,CAAC,KAAK,EAAE,EAAE,CAClC,wBAAwB,CACtB,KAAK,EACL,kBAAkB,CAAC,2BAA2B,CAC/C;CACJ,CAAC;AACF,MAAM,cAAc,GAAoB;IACtC,2BAA2B,EAAE,OAAO;IACpC,2BAA2B,EAAE,SAAS;IACtC,wBAAwB,EAAE,CAAC,KAAK,EAAE,EAAE,CAClC,wBAAwB,CAAC,KAAK,EAAE,cAAc,CAAC,2BAA2B,CAAC;CAC9E,CAAC;AACF,SAAS,2BAA2B,CAClC,IAAY,EACZ,gBAAoC;IAEpC,OAAO,oBAAoB,CAAC,iBAAiB,CAAC,IAAI,EAAE,gBAAgB,CAAC,CAAC,CAAC;AACzE,CAAC;AACD,SAAS,iBAAiB,CAAC,IAAY,EAAE,gBAAgB,GAAG,EAAE;IAC5D,IAAI,GAAG,YAAY,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAC;IAC5C,OAAO,cAAc,CAAC,IAAI,EAAE,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC;AACnD,CAAC;AACD,SAAS,oBAAoB,CAAC,UAA6B;IACzD,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC;QAAE,OAAO,EAAE,CAAC;IACjC,MAAM,OAAO,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;IAChC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QAC1C,MAAM,SAAS,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;QAChC,IAAI,CAAC,SAAS;YAAE,SAAS;QACzB,IAAI,SAAS,KAAK,GAAG;YAAE,SAAS;QAChC,IAAI,SAAS,KAAK,IAAI,EAAE;YACtB,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;gBACtB,IAAI,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,IAAI,EAAE;oBACxC,OAAO,CAAC,GAAG,EAAE,CAAC;oBACd,SAAS;iBACV;aACF;iBAAM,IAAI,OAAO,CAAC,CAAC,CAAC;gBAAE,SAAS;SACjC;QACD,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;KACzB;IACD,OAAO,OAAO,CAAC;AACjB,CAAC;AACD,SAAS,aAAa,CAAC,IAAY;IACjC,MAAM,UAAU,GAAG,oBAAoB,CAAC,IAAI,CAAC,CAAC;IAC9C,OAAO,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC;AACnD,CAAC;AACD,SAAS,oBAAoB,CAAC,IAAY;IACxC,IAAI,CAAC,IAAI;QAAE,OAAO,CAAC,CAAC;IACpB,MAAM,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;IAE/B,eAAe;IACf,IAAI,GAAG,kCAAyB,IAAI,GAAG,sCAA6B,EAAE;QACpE,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,KAAK,GAAG;YAAE,OAAO,CAAC,CAAC,CAAC,qCAAqC;QAE/E,MAAM,EAAE,GAAG,IAAI,CAAC,OAAO,CACrB,GAAG,kCAAyB,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,qBAAqB,EACzE,CAAC,CACF,CAAC;QACF,IAAI,EAAE,GAAG,CAAC;YAAE,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,gCAAgC;QAEhE,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC,kCAAkC;KAClD;IAED,MAAM;IACN,IAAI,iBAAiB,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,kCAAyB,EAAE;QACzE,MAAM,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;QAC/B,IAAI,GAAG,kCAAyB,IAAI,GAAG,sCAA6B;YAClE,OAAO,CAAC,CAAC,CAAC,sBAAsB;QAClC,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,CAAC,CAAC,CAAC,4BAA4B;KAC9D;IAED,MAAM;IACN,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC;IACnD,IAAI,SAAS,KAAK,CAAC,CAAC,EAAE;QACpB,MAAM,cAAc,GAAG,SAAS,GAAG,kBAAkB,CAAC,MAAM,CAAC;QAC7D,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,kBAAkB,EAAE,cAAc,CAAC,CAAC;QACtE,IAAI,YAAY,KAAK,CAAC,CAAC,EAAE;YACvB,0DAA0D;YAC1D,sEAAsE;YACtE,6EAA6E;YAC7E,qFAAqF;YACrF,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC;YACxC,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,cAAc,EAAE,YAAY,CAAC,CAAC;YAC3D,IACE,MAAM,KAAK,MAAM;gBACjB,CAAC,SAAS,KAAK,EAAE,IAAI,SAAS,KAAK,WAAW,CAAC;gBAC/C,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC,EACpD;gBACA,MAAM,kBAAkB,GAAG,4BAA4B,CACrD,IAAI,EACJ,YAAY,GAAG,CAAC,CACjB,CAAC;gBACF,IAAI,kBAAkB,KAAK,CAAC,CAAC,EAAE;oBAC7B,IAAI,IAAI,CAAC,UAAU,CAAC,kBAAkB,CAAC,kCAAyB,EAAE;wBAChE,wFAAwF;wBACxF,OAAO,CAAC,CAAC,kBAAkB,GAAG,CAAC,CAAC,CAAC;qBAClC;oBACD,IAAI,kBAAkB,KAAK,IAAI,CAAC,MAAM,EAAE;wBACtC,oFAAoF;wBACpF,2CAA2C;wBAC3C,OAAO,CAAC,kBAAkB,CAAC;qBAC5B;iBACF;aACF;YACD,OAAO,CAAC,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC,CAAC,0CAA0C;SACvE;QACD,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,wCAAwC;KAC9D;IAED,WAAW;IACX,OAAO,CAAC,CAAC;AACX,CAAC;AACD,SAAS,gCAAgC,CAAC,IAAY;IACpD,IAAI,CAAC,6BAA6B,CAAC,IAAI,CAAC,EAAE;QACxC,OAAO,IAAI,GAAG,kBAAkB,CAAC;KAClC;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AACD,SAAS,6BAA6B,CAAC,IAAY;IACjD,OAAO,CACL,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,uBAAuB,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAC7E,CAAC;AACJ,CAAC;AACD,SAAS,uBAAuB,CAAC,QAAgB;IAC/C,OAAO,CACL,QAAQ,kCAAyB,IAAI,QAAQ,sCAA6B,CAC3E,CAAC;AACJ,CAAC;AACD,SAAS,gCAAgC,CAAC,IAAY;IACpD,IAAI,6BAA6B,CAAC,IAAI,CAAC,EAAE;QACvC,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;KACxC;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AACD,MAAM,kBAAkB,GAAG,GAAG,CAAC;AAC/B,MAAM,qBAAqB,GAAG,IAAI,CAAC;AACnC,MAAM,kBAAkB,GAAG,KAAK,CAAC;AACjC,SAAS,iBAAiB,CAAC,QAAgB;IACzC,OAAO,CACL,CAAC,QAAQ,6BAAoB,IAAI,QAAQ,8BAAoB,CAAC;QAC9D,CAAC,QAAQ,6BAAoB,IAAI,QAAQ,6BAAoB,CAAC,CAC/D,CAAC;AACJ,CAAC;AACD,SAAS,4BAA4B,CAAC,GAAW,EAAE,KAAa;IAC9D,MAAM,GAAG,GAAG,GAAG,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;IAClC,IAAI,GAAG,kCAAyB;QAAE,OAAO,KAAK,GAAG,CAAC,CAAC;IACnD,IACE,GAAG,oCAA2B;QAC9B,GAAG,CAAC,UAAU,CAAC,KAAK,GAAG,CAAC,CAAC,+BAAsB,EAC/C;QACA,MAAM,GAAG,GAAG,GAAG,CAAC,UAAU,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;QACtC,IAAI,GAAG,8BAAqB,IAAI,GAAG,8BAAqB;YAAE,OAAO,KAAK,GAAG,CAAC,CAAC;KAC5E;IACD,OAAO,CAAC,CAAC,CAAC;AACZ,CAAC;AAMD,SAAS,IAAI,CACX,KAA+B,EAC/B,SAAiC;IAEjC,IAAI,KAAK,EAAE;QACT,IAAI,SAAS,EAAE;YACb,KAAK,MAAM,CAAC,IAAI,KAAK,EAAE;gBACrB,IAAI,SAAS,CAAC,CAAC,CAAC,EAAE;oBAChB,OAAO,IAAI,CAAC;iBACb;aACF;SACF;aAAM;YACL,OAAO,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;SACzB;KACF;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAeD,SAAS,cAAc,CAAC,IAAY,EAAE,UAAkB;IACtD,MAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;IAC3C,MAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC;IAClE,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC;QAAE,IAAI,CAAC,GAAG,EAAE,CAAC;IACtD,OAAO,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC;AACzB,CAAC;AACD,SAAS,eAAe,CAAI,KAAmB;IAC7C,OAAO,KAAK,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AAClE,CAAC;AACD,SAAS,IAAI,CAAI,KAAmB;IAClC,oCAAoC;IACpC,OAAO,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AACjC,CAAC;AACD,SAAS,wBAAwB,CAC/B,KAAa,EACb,2BAAmC;IAEnC,OAAO,KAAK,KAAK,GAAG;QAClB,CAAC,CAAC,2BAA2B;QAC7B,CAAC,CAAC,KAAK,KAAK,GAAG;YACf,CAAC,CAAC,MAAM;YACR,CAAC,CAAC,IAAI,GAAG,KAAK,CAAC;AACnB,CAAC;AACD;;;GAGG;AACH,SAAS,cAAc,CAAC,iBAAyB;IAC/C,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;AAC1C,CAAC;AAED,MAAM,mBAAmB,GAAG,CAAC,CAAC;AAC9B,MAAM,sBAAsB,GAAG,CAAC,CAAC;AACjC,MAAM,sBAAsB,GAAG,EAAE,CAAC;AAClC,MAAM,oBAAoB,GAAG,GAAG,CAAC;AACjC,MAAM,sBAAsB,GAAG,GAAG,CAAC;AACnC,wHAAwH;AACxH,SAAgB,0BAA0B,CAAC,eAAoC;IAC7E,OAAO,eAAe,CAAC,uBAAuB,KAAK,SAAS;QAC1D,CAAC,CAAC,mBAAmB,CAAC,eAAe,CAAC,IAAI,sBAAsB;QAChE,CAAC,CAAC,eAAe,CAAC,uBAAuB,CAAC;AAC9C,CAAC;AAJD,gEAIC;AAED,wHAAwH;AACxH,SAAgB,mBAAmB,CAAC,eAGnC;;IACC,OAAO,CACL,MAAA,eAAe,CAAC,MAAM,mCACtB,CAAC,CAAC,eAAe,CAAC,MAAM,KAAK,oBAAoB,IAAI,sBAAsB,CAAC;QAC1E,CAAC,eAAe,CAAC,MAAM,KAAK,sBAAsB,IAAI,sBAAsB,CAAC;QAC7E,mBAAmB,CAAC,CACvB,CAAC;AACJ,CAAC;AAVD,kDAUC", "sourcesContent": ["import { isAbsolute, resolve } from 'path';\nimport { cachedLookup, normalizeSlashes, versionGteLt } from './util';\nimport type * as _ts from 'typescript';\nimport type { TSCommon, TSInternal } from './ts-compiler-types';\n\n/** @internal */\nexport const createTsInternals = cachedLookup(createTsInternalsUncached);\n/**\n * Given a reference to the TS compiler, return some TS internal functions that we\n * could not or did not want to grab off the `ts` object.\n * These have been copy-pasted from TS's source and tweaked as necessary.\n *\n * NOTE: This factory returns *only* functions which need a reference to the TS\n * compiler.  Other functions do not need a reference to the TS compiler so are\n * exported directly from this file.\n */\nfunction createTsInternalsUncached(_ts: TSCommon) {\n  const ts = _ts as TSCommon & TSInternal;\n  /**\n   * Copied from:\n   * https://github.com/microsoft/TypeScript/blob/v4.3.2/src/compiler/commandLineParser.ts#L2821-L2846\n   */\n  function getExtendsConfigPath(\n    extendedConfig: string,\n    host: _ts.ParseConfigHost,\n    basePath: string,\n    errors: _ts.Push<_ts.Diagnostic>,\n    createDiagnostic: (\n      message: _ts.DiagnosticMessage,\n      arg1?: string\n    ) => _ts.Diagnostic\n  ) {\n    extendedConfig = normalizeSlashes(extendedConfig);\n    if (\n      isRootedDiskPath(extendedConfig) ||\n      startsWith(extendedConfig, './') ||\n      startsWith(extendedConfig, '../')\n    ) {\n      let extendedConfigPath = getNormalizedAbsolutePath(\n        extendedConfig,\n        basePath\n      );\n      if (\n        !host.fileExists(extendedConfigPath) &&\n        !endsWith(extendedConfigPath, ts.Extension.Json)\n      ) {\n        extendedConfigPath = `${extendedConfigPath}.json`;\n        if (!host.fileExists(extendedConfigPath)) {\n          errors.push(\n            createDiagnostic(ts.Diagnostics.File_0_not_found, extendedConfig)\n          );\n          return undefined;\n        }\n      }\n      return extendedConfigPath;\n    }\n    // If the path isn't a rooted or relative path, resolve like a module\n    const tsGte5_3_0 = versionGteLt(ts.version, '5.3.0');\n    const resolved = ts.nodeModuleNameResolver(\n      extendedConfig,\n      combinePaths(basePath, 'tsconfig.json'),\n      { moduleResolution: ts.ModuleResolutionKind.NodeJs },\n      host,\n      /*cache*/ undefined,\n      /*projectRefs*/ undefined,\n      /*conditionsOrIsConfigLookup*/ tsGte5_3_0 ? undefined : true,\n      /*isConfigLookup*/ tsGte5_3_0 ? true : undefined\n    );\n    if (resolved.resolvedModule) {\n      return resolved.resolvedModule.resolvedFileName;\n    }\n    errors.push(\n      createDiagnostic(ts.Diagnostics.File_0_not_found, extendedConfig)\n    );\n    return undefined;\n  }\n\n  return { getExtendsConfigPath };\n}\n\n// These functions have alternative implementation to avoid copying too much from TS\nfunction isRootedDiskPath(path: string) {\n  return isAbsolute(path);\n}\nfunction combinePaths(path: string, ...paths: (string | undefined)[]): string {\n  return normalizeSlashes(\n    resolve(path, ...(paths.filter((path) => path) as string[]))\n  );\n}\nfunction getNormalizedAbsolutePath(\n  fileName: string,\n  currentDirectory: string | undefined\n) {\n  return normalizeSlashes(\n    currentDirectory != null\n      ? resolve(currentDirectory!, fileName)\n      : resolve(fileName)\n  );\n}\n\nfunction startsWith(str: string, prefix: string): boolean {\n  return str.lastIndexOf(prefix, 0) === 0;\n}\n\nfunction endsWith(str: string, suffix: string): boolean {\n  const expectedPos = str.length - suffix.length;\n  return expectedPos >= 0 && str.indexOf(suffix, expectedPos) === expectedPos;\n}\n// Reserved characters, forces escaping of any non-word (or digit), non-whitespace character.\n// It may be inefficient (we could just match (/[-[\\]{}()*+?.,\\\\^$|#\\s]/g), but this is future\n// proof.\nconst reservedCharacterPattern = /[^\\w\\s\\/]/g;\n\n/**\n * @internal\n * See also: getRegularExpressionForWildcard, which seems to do almost the same thing\n */\nexport function getPatternFromSpec(spec: string, basePath: string) {\n  const pattern = spec && getSubPatternFromSpec(spec, basePath, excludeMatcher);\n  return pattern && `^(${pattern})${'($|/)'}`;\n}\nfunction getSubPatternFromSpec(\n  spec: string,\n  basePath: string,\n  {\n    singleAsteriskRegexFragment,\n    doubleAsteriskRegexFragment,\n    replaceWildcardCharacter,\n  }: WildcardMatcher\n): string {\n  let subpattern = '';\n  let hasWrittenComponent = false;\n  const components = getNormalizedPathComponents(spec, basePath);\n  const lastComponent = last(components);\n\n  // getNormalizedPathComponents includes the separator for the root component.\n  // We need to remove to create our regex correctly.\n  components[0] = removeTrailingDirectorySeparator(components[0]);\n\n  if (isImplicitGlob(lastComponent)) {\n    components.push('**', '*');\n  }\n\n  let optionalCount = 0;\n  for (let component of components) {\n    if (component === '**') {\n      subpattern += doubleAsteriskRegexFragment;\n    } else {\n      if (hasWrittenComponent) {\n        subpattern += directorySeparator;\n      }\n      subpattern += component.replace(\n        reservedCharacterPattern,\n        replaceWildcardCharacter\n      );\n    }\n\n    hasWrittenComponent = true;\n  }\n\n  while (optionalCount > 0) {\n    subpattern += ')?';\n    optionalCount--;\n  }\n\n  return subpattern;\n}\ninterface WildcardMatcher {\n  singleAsteriskRegexFragment: string;\n  doubleAsteriskRegexFragment: string;\n  replaceWildcardCharacter: (match: string) => string;\n}\nconst directoriesMatcher: WildcardMatcher = {\n  singleAsteriskRegexFragment: '[^/]*',\n  /**\n   * Regex for the ** wildcard. Matches any num of subdirectories. When used for including\n   * files or directories, does not match subdirectories that start with a . character\n   */\n  doubleAsteriskRegexFragment: `(/[^/.][^/]*)*?`,\n  replaceWildcardCharacter: (match) =>\n    replaceWildcardCharacter(\n      match,\n      directoriesMatcher.singleAsteriskRegexFragment\n    ),\n};\nconst excludeMatcher: WildcardMatcher = {\n  singleAsteriskRegexFragment: '[^/]*',\n  doubleAsteriskRegexFragment: '(/.+?)?',\n  replaceWildcardCharacter: (match) =>\n    replaceWildcardCharacter(match, excludeMatcher.singleAsteriskRegexFragment),\n};\nfunction getNormalizedPathComponents(\n  path: string,\n  currentDirectory: string | undefined\n) {\n  return reducePathComponents(getPathComponents(path, currentDirectory));\n}\nfunction getPathComponents(path: string, currentDirectory = '') {\n  path = combinePaths(currentDirectory, path);\n  return pathComponents(path, getRootLength(path));\n}\nfunction reducePathComponents(components: readonly string[]) {\n  if (!some(components)) return [];\n  const reduced = [components[0]];\n  for (let i = 1; i < components.length; i++) {\n    const component = components[i];\n    if (!component) continue;\n    if (component === '.') continue;\n    if (component === '..') {\n      if (reduced.length > 1) {\n        if (reduced[reduced.length - 1] !== '..') {\n          reduced.pop();\n          continue;\n        }\n      } else if (reduced[0]) continue;\n    }\n    reduced.push(component);\n  }\n  return reduced;\n}\nfunction getRootLength(path: string) {\n  const rootLength = getEncodedRootLength(path);\n  return rootLength < 0 ? ~rootLength : rootLength;\n}\nfunction getEncodedRootLength(path: string): number {\n  if (!path) return 0;\n  const ch0 = path.charCodeAt(0);\n\n  // POSIX or UNC\n  if (ch0 === CharacterCodes.slash || ch0 === CharacterCodes.backslash) {\n    if (path.charCodeAt(1) !== ch0) return 1; // POSIX: \"/\" (or non-normalized \"\\\")\n\n    const p1 = path.indexOf(\n      ch0 === CharacterCodes.slash ? directorySeparator : altDirectorySeparator,\n      2\n    );\n    if (p1 < 0) return path.length; // UNC: \"//server\" or \"\\\\server\"\n\n    return p1 + 1; // UNC: \"//server/\" or \"\\\\server\\\"\n  }\n\n  // DOS\n  if (isVolumeCharacter(ch0) && path.charCodeAt(1) === CharacterCodes.colon) {\n    const ch2 = path.charCodeAt(2);\n    if (ch2 === CharacterCodes.slash || ch2 === CharacterCodes.backslash)\n      return 3; // DOS: \"c:/\" or \"c:\\\"\n    if (path.length === 2) return 2; // DOS: \"c:\" (but not \"c:d\")\n  }\n\n  // URL\n  const schemeEnd = path.indexOf(urlSchemeSeparator);\n  if (schemeEnd !== -1) {\n    const authorityStart = schemeEnd + urlSchemeSeparator.length;\n    const authorityEnd = path.indexOf(directorySeparator, authorityStart);\n    if (authorityEnd !== -1) {\n      // URL: \"file:///\", \"file://server/\", \"file://server/path\"\n      // For local \"file\" URLs, include the leading DOS volume (if present).\n      // Per https://www.ietf.org/rfc/rfc1738.txt, a host of \"\" or \"localhost\" is a\n      // special case interpreted as \"the machine from which the URL is being interpreted\".\n      const scheme = path.slice(0, schemeEnd);\n      const authority = path.slice(authorityStart, authorityEnd);\n      if (\n        scheme === 'file' &&\n        (authority === '' || authority === 'localhost') &&\n        isVolumeCharacter(path.charCodeAt(authorityEnd + 1))\n      ) {\n        const volumeSeparatorEnd = getFileUrlVolumeSeparatorEnd(\n          path,\n          authorityEnd + 2\n        );\n        if (volumeSeparatorEnd !== -1) {\n          if (path.charCodeAt(volumeSeparatorEnd) === CharacterCodes.slash) {\n            // URL: \"file:///c:/\", \"file://localhost/c:/\", \"file:///c%3a/\", \"file://localhost/c%3a/\"\n            return ~(volumeSeparatorEnd + 1);\n          }\n          if (volumeSeparatorEnd === path.length) {\n            // URL: \"file:///c:\", \"file://localhost/c:\", \"file:///c$3a\", \"file://localhost/c%3a\"\n            // but not \"file:///c:d\" or \"file:///c%3ad\"\n            return ~volumeSeparatorEnd;\n          }\n        }\n      }\n      return ~(authorityEnd + 1); // URL: \"file://server/\", \"http://server/\"\n    }\n    return ~path.length; // URL: \"file://server\", \"http://server\"\n  }\n\n  // relative\n  return 0;\n}\nfunction ensureTrailingDirectorySeparator(path: string) {\n  if (!hasTrailingDirectorySeparator(path)) {\n    return path + directorySeparator;\n  }\n\n  return path;\n}\nfunction hasTrailingDirectorySeparator(path: string) {\n  return (\n    path.length > 0 && isAnyDirectorySeparator(path.charCodeAt(path.length - 1))\n  );\n}\nfunction isAnyDirectorySeparator(charCode: number): boolean {\n  return (\n    charCode === CharacterCodes.slash || charCode === CharacterCodes.backslash\n  );\n}\nfunction removeTrailingDirectorySeparator(path: string) {\n  if (hasTrailingDirectorySeparator(path)) {\n    return path.substr(0, path.length - 1);\n  }\n\n  return path;\n}\nconst directorySeparator = '/';\nconst altDirectorySeparator = '\\\\';\nconst urlSchemeSeparator = '://';\nfunction isVolumeCharacter(charCode: number) {\n  return (\n    (charCode >= CharacterCodes.a && charCode <= CharacterCodes.z) ||\n    (charCode >= CharacterCodes.A && charCode <= CharacterCodes.Z)\n  );\n}\nfunction getFileUrlVolumeSeparatorEnd(url: string, start: number) {\n  const ch0 = url.charCodeAt(start);\n  if (ch0 === CharacterCodes.colon) return start + 1;\n  if (\n    ch0 === CharacterCodes.percent &&\n    url.charCodeAt(start + 1) === CharacterCodes._3\n  ) {\n    const ch2 = url.charCodeAt(start + 2);\n    if (ch2 === CharacterCodes.a || ch2 === CharacterCodes.A) return start + 3;\n  }\n  return -1;\n}\nfunction some<T>(array: readonly T[] | undefined): array is readonly T[];\nfunction some<T>(\n  array: readonly T[] | undefined,\n  predicate: (value: T) => boolean\n): boolean;\nfunction some<T>(\n  array: readonly T[] | undefined,\n  predicate?: (value: T) => boolean\n): boolean {\n  if (array) {\n    if (predicate) {\n      for (const v of array) {\n        if (predicate(v)) {\n          return true;\n        }\n      }\n    } else {\n      return array.length > 0;\n    }\n  }\n  return false;\n}\n/* @internal */\nconst enum CharacterCodes {\n  _3 = 0x33,\n  a = 0x61,\n  z = 0x7a,\n  A = 0x41,\n  Z = 0x5a,\n  asterisk = 0x2a, // *\n  backslash = 0x5c, // \\\n  colon = 0x3a, // :\n  percent = 0x25, // %\n  question = 0x3f, // ?\n  slash = 0x2f, // /\n}\nfunction pathComponents(path: string, rootLength: number) {\n  const root = path.substring(0, rootLength);\n  const rest = path.substring(rootLength).split(directorySeparator);\n  if (rest.length && !lastOrUndefined(rest)) rest.pop();\n  return [root, ...rest];\n}\nfunction lastOrUndefined<T>(array: readonly T[]): T | undefined {\n  return array.length === 0 ? undefined : array[array.length - 1];\n}\nfunction last<T>(array: readonly T[]): T {\n  // Debug.assert(array.length !== 0);\n  return array[array.length - 1];\n}\nfunction replaceWildcardCharacter(\n  match: string,\n  singleAsteriskRegexFragment: string\n) {\n  return match === '*'\n    ? singleAsteriskRegexFragment\n    : match === '?'\n    ? '[^/]'\n    : '\\\\' + match;\n}\n/**\n * An \"includes\" path \"foo\" is implicitly a glob \"foo/** /*\" (without the space) if its last component has no extension,\n * and does not contain any glob characters itself.\n */\nfunction isImplicitGlob(lastPathComponent: string): boolean {\n  return !/[.*?]/.test(lastPathComponent);\n}\n\nconst ts_ScriptTarget_ES5 = 1;\nconst ts_ScriptTarget_ES2022 = 9;\nconst ts_ScriptTarget_ESNext = 99;\nconst ts_ModuleKind_Node16 = 100;\nconst ts_ModuleKind_NodeNext = 199;\n// https://github.com/microsoft/TypeScript/blob/fc418a2e611c88cf9afa0115ff73490b2397d311/src/compiler/utilities.ts#L8761\nexport function getUseDefineForClassFields(compilerOptions: _ts.CompilerOptions): boolean {\n  return compilerOptions.useDefineForClassFields === undefined\n    ? getEmitScriptTarget(compilerOptions) >= ts_ScriptTarget_ES2022\n    : compilerOptions.useDefineForClassFields;\n}\n\n// https://github.com/microsoft/TypeScript/blob/fc418a2e611c88cf9afa0115ff73490b2397d311/src/compiler/utilities.ts#L8556\nexport function getEmitScriptTarget(compilerOptions: {\n  module?: _ts.CompilerOptions['module'];\n  target?: _ts.CompilerOptions['target'];\n}): _ts.ScriptTarget {\n  return (\n    compilerOptions.target ??\n    ((compilerOptions.module === ts_ModuleKind_Node16 && ts_ScriptTarget_ES2022) ||\n      (compilerOptions.module === ts_ModuleKind_NodeNext && ts_ScriptTarget_ESNext) ||\n      ts_ScriptTarget_ES5)\n  );\n}\n"]}