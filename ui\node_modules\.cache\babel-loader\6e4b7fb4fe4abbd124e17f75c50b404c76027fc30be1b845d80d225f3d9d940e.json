{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst MoveHorizontal = createLucideIcon(\"MoveHorizontal\", [[\"polyline\", {\n  points: \"18 8 22 12 18 16\",\n  key: \"1hqrds\"\n}], [\"polyline\", {\n  points: \"6 8 2 12 6 16\",\n  key: \"f0ernq\"\n}], [\"line\", {\n  x1: \"2\",\n  x2: \"22\",\n  y1: \"12\",\n  y2: \"12\",\n  key: \"1dnqot\"\n}]]);\nexport { MoveHorizontal as default };", "map": {"version": 3, "names": ["MoveHorizontal", "createLucideIcon", "points", "key", "x1", "x2", "y1", "y2"], "sources": ["D:\\repos-personal\\repos\\openapi-to-mcp\\ui\\node_modules\\lucide-react\\src\\icons\\move-horizontal.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name MoveHorizontal\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cG9seWxpbmUgcG9pbnRzPSIxOCA4IDIyIDEyIDE4IDE2IiAvPgogIDxwb2x5bGluZSBwb2ludHM9IjYgOCAyIDEyIDYgMTYiIC8+CiAgPGxpbmUgeDE9IjIiIHgyPSIyMiIgeTE9IjEyIiB5Mj0iMTIiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/move-horizontal\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst MoveHorizontal = createLucideIcon('MoveHorizontal', [\n  ['polyline', { points: '18 8 22 12 18 16', key: '1hqrds' }],\n  ['polyline', { points: '6 8 2 12 6 16', key: 'f0ernq' }],\n  ['line', { x1: '2', x2: '22', y1: '12', y2: '12', key: '1dnqot' }],\n]);\n\nexport default MoveHorizontal;\n"], "mappings": ";;;;;AAaM,MAAAA,cAAA,GAAiBC,gBAAA,CAAiB,gBAAkB,GACxD,CAAC,UAAY;EAAEC,MAAA,EAAQ,kBAAoB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1D,CAAC,UAAY;EAAED,MAAA,EAAQ,eAAiB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACvD,CAAC,QAAQ;EAAEC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAJ,GAAA,EAAK;AAAA,CAAU,EAClE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}