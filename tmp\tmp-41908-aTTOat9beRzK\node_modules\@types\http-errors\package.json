{"name": "@types/http-errors", "version": "2.0.4", "description": "TypeScript definitions for http-errors", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/http-errors", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON>", "githubUsername": "t<PERSON><PERSON><PERSON>", "url": "https://github.com/tkrotoff"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/BendingBender"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/http-errors"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "06e33723b60f818facd3b7dd2025f043142fb7c56ab4832babafeb9470f2086f", "typeScriptVersion": "4.5"}