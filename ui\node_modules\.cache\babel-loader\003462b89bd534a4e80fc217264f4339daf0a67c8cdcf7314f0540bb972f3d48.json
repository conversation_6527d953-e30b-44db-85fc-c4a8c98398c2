{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst Shrink = createLucideIcon(\"Shrink\", [[\"path\", {\n  d: \"m15 15 6 6m-6-6v4.8m0-4.8h4.8\",\n  key: \"17vawe\"\n}], [\"path\", {\n  d: \"M9 19.8V15m0 0H4.2M9 15l-6 6\",\n  key: \"chjx8e\"\n}], [\"path\", {\n  d: \"M15 4.2V9m0 0h4.8M15 9l6-6\",\n  key: \"lav6yq\"\n}], [\"path\", {\n  d: \"M9 4.2V9m0 0H4.2M9 9 3 3\",\n  key: \"1pxi2q\"\n}]]);\nexport { Shrink as default };", "map": {"version": 3, "names": ["Shrink", "createLucideIcon", "d", "key"], "sources": ["D:\\repos-personal\\repos\\openapi-to-mcp\\ui\\node_modules\\lucide-react\\src\\icons\\shrink.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Shrink\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMTUgMTUgNiA2bS02LTZ2NC44bTAtNC44aDQuOCIgLz4KICA8cGF0aCBkPSJNOSAxOS44VjE1bTAgMEg0LjJNOSAxNWwtNiA2IiAvPgogIDxwYXRoIGQ9Ik0xNSA0LjJWOW0wIDBoNC44TTE1IDlsNi02IiAvPgogIDxwYXRoIGQ9Ik05IDQuMlY5bTAgMEg0LjJNOSA5IDMgMyIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/shrink\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Shrink = createLucideIcon('Shrink', [\n  ['path', { d: 'm15 15 6 6m-6-6v4.8m0-4.8h4.8', key: '17vawe' }],\n  ['path', { d: 'M9 19.8V15m0 0H4.2M9 15l-6 6', key: 'chjx8e' }],\n  ['path', { d: 'M15 4.2V9m0 0h4.8M15 9l6-6', key: 'lav6yq' }],\n  ['path', { d: 'M9 4.2V9m0 0H4.2M9 9 3 3', key: '1pxi2q' }],\n]);\n\nexport default Shrink;\n"], "mappings": ";;;;;AAaM,MAAAA,MAAA,GAASC,gBAAA,CAAiB,QAAU,GACxC,CAAC,MAAQ;EAAEC,CAAA,EAAG,+BAAiC;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC9D,CAAC,MAAQ;EAAED,CAAA,EAAG,8BAAgC;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC7D,CAAC,MAAQ;EAAED,CAAA,EAAG,4BAA8B;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3D,CAAC,MAAQ;EAAED,CAAA,EAAG,0BAA4B;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC1D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}