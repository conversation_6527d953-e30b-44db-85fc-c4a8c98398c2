{"generatedAt": "2025-06-09T17:19:28.409Z", "generator": "openapi-to-mcp", "version": "1.0.0", "config": {"name": "swagger-petstore-openapi-3-0", "version": "1.0.26", "description": "This is a sample Pet Store Server based on the OpenAPI 3.0 specification.  You can find out more about\nSwagger at [https://swagger.io](https://swagger.io). In the third iteration of the pet store, we've switched to the design first approach!\nYou can now help us improve the API whether it's by making changes to the definition itself or to the code.\nThat way, with time, we can improve the API in general, and expose some of the new features in OAS3.\n\nSome useful links:\n- [The Pet Store repository](https://github.com/swagger-api/swagger-petstore)\n- [The source API definition for the Pet Store](https://github.com/swagger-api/swagger-petstore/blob/master/src/main/resources/openapi.yaml)", "port": 8000, "baseUrl": "http://localhost:3000", "outputDir": "/tmp/mcp-bundles/b99865a4-cd12-41da-94b7-82880d28b9b9-1749489567230", "license": "MIT"}, "files": ["src/server.ts", "src/types.ts", "src/routes.ts", "package.json", "tsconfig.json", "README.md", ".env.example", "test-stdio.js"], "toolCount": 19}