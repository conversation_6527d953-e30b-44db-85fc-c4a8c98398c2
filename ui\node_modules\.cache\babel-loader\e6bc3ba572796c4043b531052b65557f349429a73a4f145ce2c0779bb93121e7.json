{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst FileStack = createLucideIcon(\"FileStack\", [[\"path\", {\n  d: \"M16 2v5h5\",\n  key: \"kt2in0\"\n}], [\"path\", {\n  d: \"M21 6v6.5c0 .8-.7 1.5-1.5 1.5h-7c-.8 0-1.5-.7-1.5-1.5v-9c0-.8.7-1.5 1.5-1.5H17l4 4z\",\n  key: \"1km23n\"\n}], [\"path\", {\n  d: \"M7 8v8.8c0 .*******.*******.4.8.4H15\",\n  key: \"16874u\"\n}], [\"path\", {\n  d: \"M3 12v8.8c0 .*******.*******.4.8.4H11\",\n  key: \"k2ox98\"\n}]]);\nexport { FileStack as default };", "map": {"version": 3, "names": ["FileStack", "createLucideIcon", "d", "key"], "sources": ["D:\\repos-personal\\repos\\openapi-to-mcp\\ui\\node_modules\\lucide-react\\src\\icons\\file-stack.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name FileStack\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************) - https://lucide.dev/icons/file-stack\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst FileStack = createLucideIcon('FileStack', [\n  ['path', { d: 'M16 2v5h5', key: 'kt2in0' }],\n  [\n    'path',\n    {\n      d: 'M21 6v6.5c0 .8-.7 1.5-1.5 1.5h-7c-.8 0-1.5-.7-1.5-1.5v-9c0-.8.7-1.5 1.5-1.5H17l4 4z',\n      key: '1km23n',\n    },\n  ],\n  ['path', { d: 'M7 8v8.8c0 .*******.*******.4.8.4H15', key: '16874u' }],\n  ['path', { d: 'M3 12v8.8c0 .*******.*******.4.8.4H11', key: 'k2ox98' }],\n]);\n\nexport default FileStack;\n"], "mappings": ";;;;;AAaM,MAAAA,SAAA,GAAYC,gBAAA,CAAiB,WAAa,GAC9C,CAAC,MAAQ;EAAEC,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1C,CACE,QACA;EACED,CAAG;EACHC,GAAK;AACP,EACF,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,sCAAwC;EAAAC,GAAA,EAAK;AAAA,CAAU,GACrE,CAAC,MAAQ;EAAED,CAAA,EAAG,uCAAyC;EAAAC,GAAA,EAAK;AAAA,CAAU,EACvE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}