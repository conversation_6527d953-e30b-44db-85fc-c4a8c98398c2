{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst GalleryHorizontal = createLucideIcon(\"GalleryHorizontal\", [[\"path\", {\n  d: \"M2 3v18\",\n  key: \"pzttux\"\n}], [\"rect\", {\n  width: \"12\",\n  height: \"18\",\n  x: \"6\",\n  y: \"3\",\n  rx: \"2\",\n  key: \"btr8bg\"\n}], [\"path\", {\n  d: \"M22 3v18\",\n  key: \"6jf3v\"\n}]]);\nexport { GalleryHorizontal as default };", "map": {"version": 3, "names": ["GalleryHorizontal", "createLucideIcon", "d", "key", "width", "height", "x", "y", "rx"], "sources": ["D:\\repos-personal\\repos\\openapi-to-mcp\\ui\\node_modules\\lucide-react\\src\\icons\\gallery-horizontal.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name GalleryHorizontal\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMiAzdjE4IiAvPgogIDxyZWN0IHdpZHRoPSIxMiIgaGVpZ2h0PSIxOCIgeD0iNiIgeT0iMyIgcng9IjIiIC8+CiAgPHBhdGggZD0iTTIyIDN2MTgiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/gallery-horizontal\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst GalleryHorizontal = createLucideIcon('GalleryHorizontal', [\n  ['path', { d: 'M2 3v18', key: 'pzttux' }],\n  [\n    'rect',\n    { width: '12', height: '18', x: '6', y: '3', rx: '2', key: 'btr8bg' },\n  ],\n  ['path', { d: 'M22 3v18', key: '6jf3v' }],\n]);\n\nexport default GalleryHorizontal;\n"], "mappings": ";;;;;AAaM,MAAAA,iBAAA,GAAoBC,gBAAA,CAAiB,mBAAqB,GAC9D,CAAC,MAAQ;EAAEC,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CACE,QACA;EAAEC,KAAA,EAAO,IAAM;EAAAC,MAAA,EAAQ,IAAM;EAAAC,CAAA,EAAG,GAAK;EAAAC,CAAA,EAAG,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAL,GAAA,EAAK;AAAS,EACtE,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAS,EACzC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}