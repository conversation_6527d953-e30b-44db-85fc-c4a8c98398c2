{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst MoveDiagonal2 = createLucideIcon(\"MoveDiagonal2\", [[\"polyline\", {\n  points: \"5 11 5 5 11 5\",\n  key: \"ncfzxk\"\n}], [\"polyline\", {\n  points: \"19 13 19 19 13 19\",\n  key: \"1mk7hk\"\n}], [\"line\", {\n  x1: \"5\",\n  x2: \"19\",\n  y1: \"5\",\n  y2: \"19\",\n  key: \"mcyte3\"\n}]]);\nexport { MoveDiagonal2 as default };", "map": {"version": 3, "names": ["MoveDiagonal2", "createLucideIcon", "points", "key", "x1", "x2", "y1", "y2"], "sources": ["D:\\repos-personal\\repos\\openapi-to-mcp\\ui\\node_modules\\lucide-react\\src\\icons\\move-diagonal-2.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name MoveDiagonal2\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cG9seWxpbmUgcG9pbnRzPSI1IDExIDUgNSAxMSA1IiAvPgogIDxwb2x5bGluZSBwb2ludHM9IjE5IDEzIDE5IDE5IDEzIDE5IiAvPgogIDxsaW5lIHgxPSI1IiB4Mj0iMTkiIHkxPSI1IiB5Mj0iMTkiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/move-diagonal-2\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst MoveDiagonal2 = createLucideIcon('MoveDiagonal2', [\n  ['polyline', { points: '5 11 5 5 11 5', key: 'ncfzxk' }],\n  ['polyline', { points: '19 13 19 19 13 19', key: '1mk7hk' }],\n  ['line', { x1: '5', x2: '19', y1: '5', y2: '19', key: 'mcyte3' }],\n]);\n\nexport default MoveDiagonal2;\n"], "mappings": ";;;;;AAaM,MAAAA,aAAA,GAAgBC,gBAAA,CAAiB,eAAiB,GACtD,CAAC,UAAY;EAAEC,MAAA,EAAQ,eAAiB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACvD,CAAC,UAAY;EAAED,MAAA,EAAQ,mBAAqB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3D,CAAC,QAAQ;EAAEC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,IAAM;EAAAJ,GAAA,EAAK;AAAA,CAAU,EACjE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}