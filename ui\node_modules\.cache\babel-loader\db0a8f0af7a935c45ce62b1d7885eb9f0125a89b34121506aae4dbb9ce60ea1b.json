{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst CopyMinus = createLucideIcon(\"CopyMinus\", [[\"line\", {\n  x1: \"12\",\n  x2: \"18\",\n  y1: \"15\",\n  y2: \"15\",\n  key: \"1nscbv\"\n}], [\"rect\", {\n  width: \"14\",\n  height: \"14\",\n  x: \"8\",\n  y: \"8\",\n  rx: \"2\",\n  ry: \"2\",\n  key: \"17jyea\"\n}], [\"path\", {\n  d: \"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2\",\n  key: \"zix9uf\"\n}]]);\nexport { Copy<PERSON>inus as default };", "map": {"version": 3, "names": ["CopyMinus", "createLucideIcon", "x1", "x2", "y1", "y2", "key", "width", "height", "x", "y", "rx", "ry", "d"], "sources": ["D:\\repos-personal\\repos\\openapi-to-mcp\\ui\\node_modules\\lucide-react\\src\\icons\\copy-minus.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name CopyMinus\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8bGluZSB4MT0iMTIiIHgyPSIxOCIgeTE9IjE1IiB5Mj0iMTUiIC8+CiAgPHJlY3Qgd2lkdGg9IjE0IiBoZWlnaHQ9IjE0IiB4PSI4IiB5PSI4IiByeD0iMiIgcnk9IjIiIC8+CiAgPHBhdGggZD0iTTQgMTZjLTEuMSAwLTItLjktMi0yVjRjMC0xLjEuOS0yIDItMmgxMGMxLjEgMCAyIC45IDIgMiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/copy-minus\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst CopyMinus = createLucideIcon('CopyMinus', [\n  ['line', { x1: '12', x2: '18', y1: '15', y2: '15', key: '1nscbv' }],\n  [\n    'rect',\n    {\n      width: '14',\n      height: '14',\n      x: '8',\n      y: '8',\n      rx: '2',\n      ry: '2',\n      key: '17jyea',\n    },\n  ],\n  [\n    'path',\n    {\n      d: 'M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2',\n      key: 'zix9uf',\n    },\n  ],\n]);\n\nexport default CopyMinus;\n"], "mappings": ";;;;;AAaM,MAAAA,SAAA,GAAYC,gBAAA,CAAiB,WAAa,GAC9C,CAAC,QAAQ;EAAEC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,GAAA,EAAK;AAAA,CAAU,GAClE,CACE,QACA;EACEC,KAAO;EACPC,MAAQ;EACRC,CAAG;EACHC,CAAG;EACHC,EAAI;EACJC,EAAI;EACJN,GAAK;AACP,EACF,EACA,CACE,QACA;EACEO,CAAG;EACHP,GAAK;AACP,EACF,CACD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}