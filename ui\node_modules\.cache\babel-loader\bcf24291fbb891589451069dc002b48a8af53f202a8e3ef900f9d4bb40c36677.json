{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst Pilcrow = createLucideIcon(\"Pilcrow\", [[\"path\", {\n  d: \"M13 4v16\",\n  key: \"8vvj80\"\n}], [\"path\", {\n  d: \"M17 4v16\",\n  key: \"7dpous\"\n}], [\"path\", {\n  d: \"M19 4H9.5a4.5 4.5 0 0 0 0 9H13\",\n  key: \"sh4n9v\"\n}]]);\nexport { Pilcrow as default };", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "createLucideIcon", "d", "key"], "sources": ["D:\\repos-personal\\repos\\openapi-to-mcp\\ui\\node_modules\\lucide-react\\src\\icons\\pilcrow.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Pilcrow\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTMgNHYxNiIgLz4KICA8cGF0aCBkPSJNMTcgNHYxNiIgLz4KICA8cGF0aCBkPSJNMTkgNEg5LjVhNC41IDQuNSAwIDAgMCAwIDlIMTMiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/pilcrow\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Pilcrow = createLucideIcon('Pilcrow', [\n  ['path', { d: 'M13 4v16', key: '8vvj80' }],\n  ['path', { d: 'M17 4v16', key: '7dpous' }],\n  ['path', { d: 'M19 4H9.5a4.5 4.5 0 0 0 0 9H13', key: 'sh4n9v' }],\n]);\n\nexport default Pilcrow;\n"], "mappings": ";;;;;AAaM,MAAAA,OAAA,GAAUC,gBAAA,CAAiB,SAAW,GAC1C,CAAC,MAAQ;EAAEC,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,gCAAkC;EAAAC,GAAA,EAAK;AAAA,CAAU,EAChE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}