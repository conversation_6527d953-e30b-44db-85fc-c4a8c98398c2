"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const dotenv_1 = __importDefault(require("dotenv"));
dotenv_1.default.config();
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const helmet_1 = __importDefault(require("helmet"));
const axios_1 = __importDefault(require("axios"));
const routes_1 = require("./routes");
const app = (0, express_1.default)();
const PORT = process.env.PORT || 8001;
const BASE_URL = process.env.BASE_URL || 'http://localhost:8001';
// Security middleware
app.use((0, helmet_1.default)());
app.use((0, cors_1.default)());
// Body parsing middleware
app.use(express_1.default.json({ limit: '10mb' }));
app.use(express_1.default.urlencoded({ extended: true }));
// Health check
app.get('/health', (req, res) => {
    res.json({
        status: 'healthy',
        name: 'instant-mcp',
        version: '1.0.0',
        timestamp: new Date().toISOString(),
        baseUrl: BASE_URL
    });
});
// MCP tool endpoints
app.use('/tools', routes_1.router);
// MCP protocol handler
app.post('/mcp', async (req, res) => {
    try {
        const { tool, parameters } = req.body;
        // Route to appropriate tool handler
        const response = await fetch(`http://localhost:${PORT}/tools/${tool}`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(parameters)
        });
        const result = await response.json();
        res.json({
            success: true,
            result
        });
    }
    catch (error) {
        res.status(500).json({
            success: false,
            error: error instanceof Error ? error.message : String(error)
        });
    }
});
// LLM Chat endpoint using LiteLLM Gateway and API key
app.post('/chat', async (req, res) => {
    const { message } = req.body;
    if (!message) {
        return res.status(400).json({ error: 'No message provided' });
    }
    try {
        const headers = {};
        if (process.env.LITELLM_API_KEY) {
            headers['Authorization'] = `Bearer ${process.env.LITELLM_API_KEY}`;
        }
        const llmUrl = process.env.LITELLM_URL || 'https://litellm-production-744f.up.railway.app/chat/completions';
        const llmModel = 'deepseek-chat';
        console.log('[MCP] LLM Chat request:', {
            url: llmUrl,
            model: llmModel,
            apiKeyPresent: 'sk-railway-litellm-2024',
            headers,
            message
        });
        const llmRes = await axios_1.default.post(llmUrl, {
            model: llmModel,
            messages: [
                { role: 'system', content: 'You are an API assistant. Help the user interact with the API using natural language.' },
                { role: 'user', content: message }
            ]
        }, { headers });
        const llmMessage = llmRes.data?.choices?.[0]?.message?.content || llmRes.data?.choices?.[0]?.text || JSON.stringify(llmRes.data);
        res.json({ response: llmMessage });
    }
    catch (err) {
        console.error('[MCP] LLM call failed:', err && (err.response?.data || err.message || err.toString()), err);
        res.status(500).json({
            error: 'LLM call failed',
            details: err && (err.response?.data || err.message || err.toString())
        });
    }
});
// Error handling
app.use((error, req, res, next) => {
    console.error('Server error:', error);
    res.status(500).json({
        error: 'Internal server error',
        message: error instanceof Error ? error.message : String(error)
    });
});
// 404 handler
app.use('*', (req, res) => {
    res.status(404).json({
        error: 'Not found',
        message: `Route ${req.originalUrl} not found`
    });
});
// Start server
const server = app.listen(PORT, () => {
    console.log(`🚀 instant-mcp MCP Server running on port ${PORT}`);
    console.log(`📖 Health check: http://localhost:${PORT}/health`);
    console.log(`🔧 Base URL: ${BASE_URL}`);
});
// Graceful shutdown
process.on('SIGTERM', () => {
    console.log('SIGTERM received, shutting down gracefully');
    server.close(() => {
        console.log('Server closed');
        process.exit(0);
    });
});
exports.default = app;
//# sourceMappingURL=server.js.map