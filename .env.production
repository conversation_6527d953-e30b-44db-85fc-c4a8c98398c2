# Production Environment Configuration for MCPForge

# Server Configuration
NODE_ENV=production
PORT=3000

# CORS Configuration
CORS_ORIGIN=https://mcpforge.com,https://www.mcpforge.com

# File Upload Limits
MAX_FILE_SIZE=10mb

# Rate Limiting
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX=100

# Security
HELMET_CSP_ENABLED=true

# Logging
LOG_LEVEL=info

# Future: Database Configuration (when user management is added)
# DATABASE_URL=postgresql://username:password@host:port/database

# Future: Redis Configuration (for caching and sessions)
# REDIS_URL=redis://username:password@host:port

# Future: Clerk Authentication (when auth is implemented)
# CLERK_PUBLISHABLE_KEY=pk_live_...
# CLERK_SECRET_KEY=sk_live_...
# CLERK_WEBHOOK_SECRET=whsec_...

# Future: Email Service (for notifications)
# SMTP_HOST=smtp.sendgrid.net
# SMTP_PORT=587
# SMTP_USER=apikey
# SMTP_PASS=your_sendgrid_api_key
# FROM_EMAIL=<EMAIL>

# Future: Analytics
# GOOGLE_ANALYTICS_ID=G-XXXXXXXXXX

# Future: Monitoring
# SENTRY_DSN=https://<EMAIL>/project-id

# Future: Payment Processing (for Pro plans)
# STRIPE_PUBLISHABLE_KEY=pk_live_...
# STRIPE_SECRET_KEY=sk_live_...
# STRIPE_WEBHOOK_SECRET=whsec_...

# Future: Cloud Storage (for generated servers)
# AWS_ACCESS_KEY_ID=your_access_key
# AWS_SECRET_ACCESS_KEY=your_secret_key
# AWS_REGION=us-east-1
# AWS_S3_BUCKET=mcpforge-generated-servers
