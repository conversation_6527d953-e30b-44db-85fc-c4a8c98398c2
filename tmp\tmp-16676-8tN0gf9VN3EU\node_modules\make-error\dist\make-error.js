!function(f){if("object"==typeof exports&&"undefined"!=typeof module)module.exports=f();else if("function"==typeof define&&define.amd)define([],f);else{("undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this).makeError=f()}}(function(){return function r(e,n,t){function o(i,f){if(!n[i]){if(!e[i]){var c="function"==typeof require&&require;if(!f&&c)return c(i,!0);if(u)return u(i,!0);var a=new Error("Cannot find module '"+i+"'");throw a.code="MODULE_NOT_FOUND",a}var p=n[i]={exports:{}};e[i][0].call(p.exports,function(r){return o(e[i][1][r]||r)},p,p.exports,r,e,n,t)}return n[i].exports}for(var u="function"==typeof require&&require,i=0;i<t.length;i++)o(t[i]);return o}({1:[function(require,module,exports){"use strict";var construct="undefined"!=typeof Reflect?Reflect.construct:void 0,defineProperty=Object.defineProperty,captureStackTrace=Error.captureStackTrace;function BaseError(message){void 0!==message&&defineProperty(this,"message",{configurable:!0,value:message,writable:!0});var cname=this.constructor.name;void 0!==cname&&cname!==this.name&&defineProperty(this,"name",{configurable:!0,value:cname,writable:!0}),captureStackTrace(this,this.constructor)}void 0===captureStackTrace&&(captureStackTrace=function(error){var container=new Error;defineProperty(error,"stack",{configurable:!0,get:function(){var stack=container.stack;return defineProperty(this,"stack",{configurable:!0,value:stack,writable:!0}),stack},set:function(stack){defineProperty(error,"stack",{configurable:!0,value:stack,writable:!0})}})}),BaseError.prototype=Object.create(Error.prototype,{constructor:{configurable:!0,value:BaseError,writable:!0}});var setFunctionName=function(){function setFunctionName(fn,name){return defineProperty(fn,"name",{configurable:!0,value:name})}try{var f=function(){};if(setFunctionName(f,"foo"),"foo"===f.name)return setFunctionName}catch(_){}}();(module.exports=function(constructor,super_){if(null==super_||super_===Error)super_=BaseError;else if("function"!=typeof super_)throw new TypeError("super_ should be a function");var name;if("string"==typeof constructor)name=constructor,constructor=void 0!==construct?function(){return construct(super_,arguments,this.constructor)}:function(){super_.apply(this,arguments)},void 0!==setFunctionName&&(setFunctionName(constructor,name),name=void 0);else if("function"!=typeof constructor)throw new TypeError("constructor should be either a string or a function");constructor.super_=constructor.super=super_;var properties={constructor:{configurable:!0,value:constructor,writable:!0}};return void 0!==name&&(properties.name={configurable:!0,value:name,writable:!0}),constructor.prototype=Object.create(super_.prototype,properties),constructor}).BaseError=BaseError},{}]},{},[1])(1)});
