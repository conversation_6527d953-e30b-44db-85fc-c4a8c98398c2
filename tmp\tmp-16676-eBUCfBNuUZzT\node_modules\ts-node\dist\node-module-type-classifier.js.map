{"version": 3, "file": "node-module-type-classifier.js", "sourceRoot": "", "sources": ["../src/node-module-type-classifier.ts"], "names": [], "mappings": ";;;AAAA,mGAAgF;AAEhF;;;;;;;;;;;;GAYG;AACH,SAAgB,cAAc,CAC5B,cAAsB,EACtB,gBAAyB;IAEzB,wCAAwC;IACxC,MAAM,YAAY,GAAG,cAAc,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;IACrD,MAAM,GAAG,GAAG,YAAY,IAAI,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACxE,QAAQ,GAAG,EAAE;QACX,KAAK,MAAM,CAAC;QACZ,KAAK,MAAM;YACT,OAAO,gBAAgB,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC;QAC9C,KAAK,MAAM,CAAC;QACZ,KAAK,MAAM;YACT,OAAO,gBAAgB,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC;KAC/C;IACD,IAAI,gBAAgB,EAAE;QACpB,MAAM,YAAY,GAAG,IAAA,mDAAgB,EAAC,cAAc,CAAC,CAAC;QACtD,IAAI,YAAY,IAAI,YAAY,CAAC,IAAI,CAAC,IAAI,KAAK,QAAQ;YAAE,OAAO,SAAS,CAAC;QAC1E,OAAO,SAAS,CAAC;KAClB;IACD,OAAO,SAAS,CAAC;AACnB,CAAC;AArBD,wCAqBC", "sourcesContent": ["import { readPackageScope } from '../dist-raw/node-internal-modules-cjs-loader';\n\n/**\n * Determine how to emit a module based on tsconfig \"module\" and package.json \"type\"\n *\n * Supports module=nodenext/node16 with transpileOnly, where we cannot ask the\n * TS typechecker to tell us if a file is CJS or ESM.\n *\n * Return values indicate:\n * - cjs\n * - esm\n * - nodecjs == node-flavored cjs where dynamic imports are *not* transformed into `require()`\n * - undefined == emit according to tsconfig `module` config, whatever that is\n * @internal\n */\nexport function classifyModule(\n  nativeFilename: string,\n  isNodeModuleType: boolean\n): 'nodecjs' | 'cjs' | 'esm' | 'nodeesm' | undefined {\n  // [MUST_UPDATE_FOR_NEW_FILE_EXTENSIONS]\n  const lastDotIndex = nativeFilename.lastIndexOf('.');\n  const ext = lastDotIndex >= 0 ? nativeFilename.slice(lastDotIndex) : '';\n  switch (ext) {\n    case '.cjs':\n    case '.cts':\n      return isNodeModuleType ? 'nodecjs' : 'cjs';\n    case '.mjs':\n    case '.mts':\n      return isNodeModuleType ? 'nodeesm' : 'esm';\n  }\n  if (isNodeModuleType) {\n    const packageScope = readPackageScope(nativeFilename);\n    if (packageScope && packageScope.data.type === 'module') return 'nodeesm';\n    return 'nodecjs';\n  }\n  return undefined;\n}\n"]}