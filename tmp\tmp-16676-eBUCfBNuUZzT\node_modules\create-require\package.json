{"name": "create-require", "version": "1.1.1", "description": "Polyfill for Node.js module.createRequire (<= v12.2.0)", "repository": "nuxt-contrib/create-require", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Pooya Parsa", "email": "<EMAIL>"}], "main": "./create-require.js", "types": "./create-require.d.ts", "files": ["create-require.js", "create-require.d.ts"], "scripts": {"lint": "eslint .", "release": "yarn test && standard-version && git push --follow-tags && npm publish", "test:matrix": "tap --no-esm --no-timeout ./test/matrix.js", "test": "yarn lint && yarn test:matrix"}, "devDependencies": {"@nuxtjs/eslint-config": "latest", "eslint": "latest", "tap": "latest", "standard-version": "latest"}}