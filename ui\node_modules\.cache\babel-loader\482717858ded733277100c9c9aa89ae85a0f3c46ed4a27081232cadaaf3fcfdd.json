{"ast": null, "code": "var _jsxFileName = \"D:\\\\repos-personal\\\\repos\\\\openapi-to-mcp\\\\ui\\\\src\\\\pages\\\\LandingPage.tsx\";\n/**\n * Landing Page component - Professional homepage\n */\n\nimport React from 'react';\nimport { Link } from 'react-router-dom';\nimport { Zap, ArrowRight, Globe, Shield, Rocket, Code, Bot } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const LandingPage = () => {\n  const features = [{\n    icon: Zap,\n    title: 'Instant Conversion',\n    description: 'Convert any OpenAPI specification to MCP server in seconds'\n  }, {\n    icon: Code,\n    title: 'Production Ready',\n    description: 'Generated servers are TypeScript-based and production-ready'\n  }, {\n    icon: Bot,\n    title: 'AI Compatible',\n    description: 'Works with Cline, Cursor, Windsurf, and other MCP clients'\n  }, {\n    icon: Shield,\n    title: 'Secure & Reliable',\n    description: 'Built with security best practices and error handling'\n  }, {\n    icon: Globe,\n    title: 'Universal Support',\n    description: 'Supports any REST API with OpenAPI 3.0+ specification'\n  }, {\n    icon: Rocket,\n    title: 'Deploy Anywhere',\n    description: 'Deploy to Railway, Vercel, or any Node.js hosting platform'\n  }];\n  const stats = [{\n    label: 'APIs Converted',\n    value: '1,000+'\n  }, {\n    label: 'Happy Developers',\n    value: '500+'\n  }, {\n    label: 'MCP Servers Generated',\n    value: '2,500+'\n  }, {\n    label: 'Uptime',\n    value: '99.9%'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-white\",\n    children: [/*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"relative bg-gradient-to-br from-blue-50 via-white to-purple-50 py-20\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-5xl md:text-6xl font-bold text-gray-900 mb-6\",\n            children: [\"Transform APIs into\", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\",\n              children: [' ', \"MCP Servers\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 69,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 67,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xl text-gray-600 mb-8 max-w-3xl mx-auto\",\n            children: \"Convert any OpenAPI specification into a fully functional MCP (Model Context Protocol) server. Enable AI assistants to interact with your APIs seamlessly.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 73,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col sm:flex-row gap-4 justify-center mb-12\",\n            children: [/*#__PURE__*/_jsxDEV(Link, {\n              to: \"/convert\",\n              className: \"inline-flex items-center space-x-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-4 rounded-lg text-lg font-medium hover:from-blue-700 hover:to-purple-700 transition-all shadow-lg\",\n              children: [/*#__PURE__*/_jsxDEV(Zap, {\n                className: \"w-5 h-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 83,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Start Converting\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 84,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(ArrowRight, {\n                className: \"w-5 h-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 85,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 79,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/playground\",\n              className: \"inline-flex items-center space-x-2 bg-white text-gray-700 px-8 py-4 rounded-lg text-lg font-medium hover:bg-gray-50 transition-all border border-gray-300\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"View Examples\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 91,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 87,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 78,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto\",\n            children: stats.map((stat, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-3xl font-bold text-gray-900 mb-1\",\n                children: stat.value\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 99,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-gray-600\",\n                children: stat.label\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 100,\n                columnNumber: 19\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 98,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 65,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 64,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-20 bg-gray-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mb-16\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-4xl font-bold text-gray-900 mb-4\",\n            children: \"Why Choose MCPify?\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 112,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xl text-gray-600 max-w-3xl mx-auto\",\n            children: \"The most powerful and easy-to-use API to MCP converter in the market\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 115,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid md:grid-cols-2 lg:grid-cols-3 gap-8\",\n          children: features.map((feature, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-xl p-8 shadow-sm border border-gray-200 hover:shadow-lg transition-shadow\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-12 h-12 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center mb-6\",\n              children: /*#__PURE__*/_jsxDEV(feature.icon, {\n                className: \"w-6 h-6 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 124,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 123,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-semibold text-gray-900 mb-3\",\n              children: feature.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 126,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600\",\n              children: feature.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 127,\n              columnNumber: 17\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 122,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 120,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 110,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 109,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-20 bg-white\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mb-16\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-4xl font-bold text-gray-900 mb-4\",\n            children: \"How It Works\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 138,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xl text-gray-600\",\n            children: \"Three simple steps to get your MCP server running\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 137,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid md:grid-cols-3 gap-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-2xl font-bold text-blue-600\",\n                children: \"1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 149,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 148,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-semibold text-gray-900 mb-3\",\n              children: \"Provide OpenAPI Spec\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600\",\n              children: \"Upload your OpenAPI specification file or provide a URL to your API documentation\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-6\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-2xl font-bold text-purple-600\",\n                children: \"2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 157,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-semibold text-gray-900 mb-3\",\n              children: \"Generate MCP Server\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 159,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600\",\n              children: \"Our AI-powered converter analyzes your API and generates a complete MCP server\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-2xl font-bold text-green-600\",\n                children: \"3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 165,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 164,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-semibold text-gray-900 mb-3\",\n              children: \"Deploy & Use\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 167,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600\",\n              children: \"Download your server, deploy it, and connect it to your favorite AI assistant\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 168,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 163,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 136,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 135,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-20 bg-gradient-to-r from-blue-600 to-purple-600\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-4xl font-bold text-white mb-6\",\n          children: \"Ready to Transform Your APIs?\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 177,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xl text-blue-100 mb-8\",\n          children: \"Join thousands of developers who are already using MCPify to power their AI assistants\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 180,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/convert\",\n          className: \"inline-flex items-center space-x-2 bg-white text-blue-600 px-8 py-4 rounded-lg text-lg font-medium hover:bg-gray-100 transition-all shadow-lg\",\n          children: [/*#__PURE__*/_jsxDEV(Zap, {\n            className: \"w-5 h-5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Get Started Free\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ArrowRight, {\n            className: \"w-5 h-5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 176,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 175,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 62,\n    columnNumber: 5\n  }, this);\n};\n_c = LandingPage;\nvar _c;\n$RefreshReg$(_c, \"LandingPage\");", "map": {"version": 3, "names": ["React", "Link", "Zap", "ArrowRight", "Globe", "Shield", "Rocket", "Code", "Bot", "jsxDEV", "_jsxDEV", "LandingPage", "features", "icon", "title", "description", "stats", "label", "value", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "map", "stat", "index", "feature", "_c", "$RefreshReg$"], "sources": ["D:/repos-personal/repos/openapi-to-mcp/ui/src/pages/LandingPage.tsx"], "sourcesContent": ["/**\n * Landing Page component - Professional homepage\n */\n\nimport React from 'react';\nimport { <PERSON> } from 'react-router-dom';\nimport { \n  <PERSON>ap, \n  ArrowR<PERSON>, \n  CheckCircle, \n  Star,\n  Users,\n  Globe,\n  Shield,\n  Rocket,\n  Code,\n  Bot\n} from 'lucide-react';\n\nexport const LandingPage: React.FC = () => {\n  const features = [\n    {\n      icon: Zap,\n      title: 'Instant Conversion',\n      description: 'Convert any OpenAPI specification to MCP server in seconds'\n    },\n    {\n      icon: Code,\n      title: 'Production Ready',\n      description: 'Generated servers are TypeScript-based and production-ready'\n    },\n    {\n      icon: Bot,\n      title: 'AI Compatible',\n      description: 'Works with Cline, Cursor, Windsurf, and other MCP clients'\n    },\n    {\n      icon: Shield,\n      title: 'Secure & Reliable',\n      description: 'Built with security best practices and error handling'\n    },\n    {\n      icon: Globe,\n      title: 'Universal Support',\n      description: 'Supports any REST API with OpenAPI 3.0+ specification'\n    },\n    {\n      icon: Rocket,\n      title: 'Deploy Anywhere',\n      description: 'Deploy to Railway, Vercel, or any Node.js hosting platform'\n    }\n  ];\n\n  const stats = [\n    { label: 'APIs Converted', value: '1,000+' },\n    { label: 'Happy Developers', value: '500+' },\n    { label: 'MCP Servers Generated', value: '2,500+' },\n    { label: 'Uptime', value: '99.9%' }\n  ];\n\n  return (\n    <div className=\"min-h-screen bg-white\">\n      {/* Hero Section */}\n      <section className=\"relative bg-gradient-to-br from-blue-50 via-white to-purple-50 py-20\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center\">\n            <h1 className=\"text-5xl md:text-6xl font-bold text-gray-900 mb-6\">\n              Transform APIs into\n              <span className=\"bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\">\n                {' '}MCP Servers\n              </span>\n            </h1>\n            <p className=\"text-xl text-gray-600 mb-8 max-w-3xl mx-auto\">\n              Convert any OpenAPI specification into a fully functional MCP (Model Context Protocol) server. \n              Enable AI assistants to interact with your APIs seamlessly.\n            </p>\n            \n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center mb-12\">\n              <Link\n                to=\"/convert\"\n                className=\"inline-flex items-center space-x-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-4 rounded-lg text-lg font-medium hover:from-blue-700 hover:to-purple-700 transition-all shadow-lg\"\n              >\n                <Zap className=\"w-5 h-5\" />\n                <span>Start Converting</span>\n                <ArrowRight className=\"w-5 h-5\" />\n              </Link>\n              <Link\n                to=\"/playground\"\n                className=\"inline-flex items-center space-x-2 bg-white text-gray-700 px-8 py-4 rounded-lg text-lg font-medium hover:bg-gray-50 transition-all border border-gray-300\"\n              >\n                <span>View Examples</span>\n              </Link>\n            </div>\n\n            {/* Stats */}\n            <div className=\"grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto\">\n              {stats.map((stat, index) => (\n                <div key={index} className=\"text-center\">\n                  <div className=\"text-3xl font-bold text-gray-900 mb-1\">{stat.value}</div>\n                  <div className=\"text-gray-600\">{stat.label}</div>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Features Section */}\n      <section className=\"py-20 bg-gray-50\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-4xl font-bold text-gray-900 mb-4\">\n              Why Choose MCPify?\n            </h2>\n            <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n              The most powerful and easy-to-use API to MCP converter in the market\n            </p>\n          </div>\n\n          <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-8\">\n            {features.map((feature, index) => (\n              <div key={index} className=\"bg-white rounded-xl p-8 shadow-sm border border-gray-200 hover:shadow-lg transition-shadow\">\n                <div className=\"w-12 h-12 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center mb-6\">\n                  <feature.icon className=\"w-6 h-6 text-white\" />\n                </div>\n                <h3 className=\"text-xl font-semibold text-gray-900 mb-3\">{feature.title}</h3>\n                <p className=\"text-gray-600\">{feature.description}</p>\n              </div>\n            ))}\n          </div>\n        </div>\n      </section>\n\n      {/* How It Works */}\n      <section className=\"py-20 bg-white\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-4xl font-bold text-gray-900 mb-4\">\n              How It Works\n            </h2>\n            <p className=\"text-xl text-gray-600\">\n              Three simple steps to get your MCP server running\n            </p>\n          </div>\n\n          <div className=\"grid md:grid-cols-3 gap-8\">\n            <div className=\"text-center\">\n              <div className=\"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6\">\n                <span className=\"text-2xl font-bold text-blue-600\">1</span>\n              </div>\n              <h3 className=\"text-xl font-semibold text-gray-900 mb-3\">Provide OpenAPI Spec</h3>\n              <p className=\"text-gray-600\">Upload your OpenAPI specification file or provide a URL to your API documentation</p>\n            </div>\n\n            <div className=\"text-center\">\n              <div className=\"w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-6\">\n                <span className=\"text-2xl font-bold text-purple-600\">2</span>\n              </div>\n              <h3 className=\"text-xl font-semibold text-gray-900 mb-3\">Generate MCP Server</h3>\n              <p className=\"text-gray-600\">Our AI-powered converter analyzes your API and generates a complete MCP server</p>\n            </div>\n\n            <div className=\"text-center\">\n              <div className=\"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6\">\n                <span className=\"text-2xl font-bold text-green-600\">3</span>\n              </div>\n              <h3 className=\"text-xl font-semibold text-gray-900 mb-3\">Deploy & Use</h3>\n              <p className=\"text-gray-600\">Download your server, deploy it, and connect it to your favorite AI assistant</p>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* CTA Section */}\n      <section className=\"py-20 bg-gradient-to-r from-blue-600 to-purple-600\">\n        <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n          <h2 className=\"text-4xl font-bold text-white mb-6\">\n            Ready to Transform Your APIs?\n          </h2>\n          <p className=\"text-xl text-blue-100 mb-8\">\n            Join thousands of developers who are already using MCPify to power their AI assistants\n          </p>\n          <Link\n            to=\"/convert\"\n            className=\"inline-flex items-center space-x-2 bg-white text-blue-600 px-8 py-4 rounded-lg text-lg font-medium hover:bg-gray-100 transition-all shadow-lg\"\n          >\n            <Zap className=\"w-5 h-5\" />\n            <span>Get Started Free</span>\n            <ArrowRight className=\"w-5 h-5\" />\n          </Link>\n        </div>\n      </section>\n    </div>\n  );\n};\n"], "mappings": ";AAAA;AACA;AACA;;AAEA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SACEC,GAAG,EACHC,UAAU,EAIVC,KAAK,EACLC,MAAM,EACNC,MAAM,EACNC,IAAI,EACJC,GAAG,QACE,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtB,OAAO,MAAMC,WAAqB,GAAGA,CAAA,KAAM;EACzC,MAAMC,QAAQ,GAAG,CACf;IACEC,IAAI,EAAEX,GAAG;IACTY,KAAK,EAAE,oBAAoB;IAC3BC,WAAW,EAAE;EACf,CAAC,EACD;IACEF,IAAI,EAAEN,IAAI;IACVO,KAAK,EAAE,kBAAkB;IACzBC,WAAW,EAAE;EACf,CAAC,EACD;IACEF,IAAI,EAAEL,GAAG;IACTM,KAAK,EAAE,eAAe;IACtBC,WAAW,EAAE;EACf,CAAC,EACD;IACEF,IAAI,EAAER,MAAM;IACZS,KAAK,EAAE,mBAAmB;IAC1BC,WAAW,EAAE;EACf,CAAC,EACD;IACEF,IAAI,EAAET,KAAK;IACXU,KAAK,EAAE,mBAAmB;IAC1BC,WAAW,EAAE;EACf,CAAC,EACD;IACEF,IAAI,EAAEP,MAAM;IACZQ,KAAK,EAAE,iBAAiB;IACxBC,WAAW,EAAE;EACf,CAAC,CACF;EAED,MAAMC,KAAK,GAAG,CACZ;IAAEC,KAAK,EAAE,gBAAgB;IAAEC,KAAK,EAAE;EAAS,CAAC,EAC5C;IAAED,KAAK,EAAE,kBAAkB;IAAEC,KAAK,EAAE;EAAO,CAAC,EAC5C;IAAED,KAAK,EAAE,uBAAuB;IAAEC,KAAK,EAAE;EAAS,CAAC,EACnD;IAAED,KAAK,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAQ,CAAC,CACpC;EAED,oBACER,OAAA;IAAKS,SAAS,EAAC,uBAAuB;IAAAC,QAAA,gBAEpCV,OAAA;MAASS,SAAS,EAAC,sEAAsE;MAAAC,QAAA,eACvFV,OAAA;QAAKS,SAAS,EAAC,wCAAwC;QAAAC,QAAA,eACrDV,OAAA;UAAKS,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BV,OAAA;YAAIS,SAAS,EAAC,mDAAmD;YAAAC,QAAA,GAAC,qBAEhE,eAAAV,OAAA;cAAMS,SAAS,EAAC,4EAA4E;cAAAC,QAAA,GACzF,GAAG,EAAC,aACP;YAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACLd,OAAA;YAAGS,SAAS,EAAC,8CAA8C;YAAAC,QAAA,EAAC;UAG5D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAEJd,OAAA;YAAKS,SAAS,EAAC,sDAAsD;YAAAC,QAAA,gBACnEV,OAAA,CAACT,IAAI;cACHwB,EAAE,EAAC,UAAU;cACbN,SAAS,EAAC,sMAAsM;cAAAC,QAAA,gBAEhNV,OAAA,CAACR,GAAG;gBAACiB,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC3Bd,OAAA;gBAAAU,QAAA,EAAM;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC7Bd,OAAA,CAACP,UAAU;gBAACgB,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC,eACPd,OAAA,CAACT,IAAI;cACHwB,EAAE,EAAC,aAAa;cAChBN,SAAS,EAAC,2JAA2J;cAAAC,QAAA,eAErKV,OAAA;gBAAAU,QAAA,EAAM;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAGNd,OAAA;YAAKS,SAAS,EAAC,yDAAyD;YAAAC,QAAA,EACrEJ,KAAK,CAACU,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBACrBlB,OAAA;cAAiBS,SAAS,EAAC,aAAa;cAAAC,QAAA,gBACtCV,OAAA;gBAAKS,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,EAAEO,IAAI,CAACT;cAAK;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACzEd,OAAA;gBAAKS,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAEO,IAAI,CAACV;cAAK;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA,GAFzCI,KAAK;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAGV,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVd,OAAA;MAASS,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eACnCV,OAAA;QAAKS,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrDV,OAAA;UAAKS,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCV,OAAA;YAAIS,SAAS,EAAC,uCAAuC;YAAAC,QAAA,EAAC;UAEtD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLd,OAAA;YAAGS,SAAS,EAAC,yCAAyC;YAAAC,QAAA,EAAC;UAEvD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAENd,OAAA;UAAKS,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EACtDR,QAAQ,CAACc,GAAG,CAAC,CAACG,OAAO,EAAED,KAAK,kBAC3BlB,OAAA;YAAiBS,SAAS,EAAC,4FAA4F;YAAAC,QAAA,gBACrHV,OAAA;cAAKS,SAAS,EAAC,yGAAyG;cAAAC,QAAA,eACtHV,OAAA,CAACmB,OAAO,CAAChB,IAAI;gBAACM,SAAS,EAAC;cAAoB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C,CAAC,eACNd,OAAA;cAAIS,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAES,OAAO,CAACf;YAAK;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC7Ed,OAAA;cAAGS,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAES,OAAO,CAACd;YAAW;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA,GAL9CI,KAAK;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAMV,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVd,OAAA;MAASS,SAAS,EAAC,gBAAgB;MAAAC,QAAA,eACjCV,OAAA;QAAKS,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrDV,OAAA;UAAKS,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCV,OAAA;YAAIS,SAAS,EAAC,uCAAuC;YAAAC,QAAA,EAAC;UAEtD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLd,OAAA;YAAGS,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAErC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAENd,OAAA;UAAKS,SAAS,EAAC,2BAA2B;UAAAC,QAAA,gBACxCV,OAAA;YAAKS,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BV,OAAA;cAAKS,SAAS,EAAC,kFAAkF;cAAAC,QAAA,eAC/FV,OAAA;gBAAMS,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxD,CAAC,eACNd,OAAA;cAAIS,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAC;YAAoB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClFd,OAAA;cAAGS,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAiF;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/G,CAAC,eAENd,OAAA;YAAKS,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BV,OAAA;cAAKS,SAAS,EAAC,oFAAoF;cAAAC,QAAA,eACjGV,OAAA;gBAAMS,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1D,CAAC,eACNd,OAAA;cAAIS,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAC;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjFd,OAAA;cAAGS,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAA8E;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5G,CAAC,eAENd,OAAA;YAAKS,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BV,OAAA;cAAKS,SAAS,EAAC,mFAAmF;cAAAC,QAAA,eAChGV,OAAA;gBAAMS,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzD,CAAC,eACNd,OAAA;cAAIS,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC1Ed,OAAA;cAAGS,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAA6E;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3G,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVd,OAAA;MAASS,SAAS,EAAC,oDAAoD;MAAAC,QAAA,eACrEV,OAAA;QAAKS,SAAS,EAAC,oDAAoD;QAAAC,QAAA,gBACjEV,OAAA;UAAIS,SAAS,EAAC,oCAAoC;UAAAC,QAAA,EAAC;QAEnD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLd,OAAA;UAAGS,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EAAC;QAE1C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJd,OAAA,CAACT,IAAI;UACHwB,EAAE,EAAC,UAAU;UACbN,SAAS,EAAC,+IAA+I;UAAAC,QAAA,gBAEzJV,OAAA,CAACR,GAAG;YAACiB,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC3Bd,OAAA;YAAAU,QAAA,EAAM;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC7Bd,OAAA,CAACP,UAAU;YAACgB,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAACM,EAAA,GA/KWnB,WAAqB;AAAA,IAAAmB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}