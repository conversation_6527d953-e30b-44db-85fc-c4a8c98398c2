# Installation
> `npm install --save @types/range-parser`

# Summary
This package contains type definitions for range-parser (https://github.com/jshttp/range-parser).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/range-parser.
## [index.d.ts](https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/range-parser/index.d.ts)
````ts
/**
 * When ranges are returned, the array has a "type" property which is the type of
 * range that is required (most commonly, "bytes"). Each array element is an object
 * with a "start" and "end" property for the portion of the range.
 *
 * @returns `-1` when unsatisfiable and `-2` when syntactically invalid, ranges otherwise.
 */
declare function RangeParser(
    size: number,
    str: string,
    options?: RangeParser.Options,
): RangeParser.Result | RangeParser.Ranges;

declare namespace RangeParser {
    interface Ranges extends Array<Range> {
        type: string;
    }
    interface Range {
        start: number;
        end: number;
    }
    interface Options {
        /**
         * The "combine" option can be set to `true` and overlapping & adjacent ranges
         * will be combined into a single range.
         */
        combine?: boolean | undefined;
    }
    type ResultUnsatisfiable = -1;
    type ResultInvalid = -2;
    type Result = ResultUnsatisfiable | ResultInvalid;
}

export = RangeParser;

````

### Additional Details
 * Last updated: Tue, 07 Nov 2023 09:09:39 GMT
 * Dependencies: none

# Credits
These definitions were written by [Tomek Łaziuk](https://github.com/tlaziuk).
