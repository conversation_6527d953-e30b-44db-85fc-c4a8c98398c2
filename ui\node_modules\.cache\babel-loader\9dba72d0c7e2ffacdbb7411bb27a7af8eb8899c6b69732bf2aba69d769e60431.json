{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst MousePointer = createLucideIcon(\"MousePointer\", [[\"path\", {\n  d: \"m3 3 7.07 16.97 2.51-7.39 7.39-2.51L3 3z\",\n  key: \"y2ucgo\"\n}], [\"path\", {\n  d: \"m13 13 6 6\",\n  key: \"1nhxnf\"\n}]]);\nexport { MousePointer as default };", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "createLucideIcon", "d", "key"], "sources": ["D:\\repos-personal\\repos\\openapi-to-mcp\\ui\\node_modules\\lucide-react\\src\\icons\\mouse-pointer.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name MousePointer\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMyAzIDcuMDcgMTYuOTcgMi41MS03LjM5IDcuMzktMi41MUwzIDN6IiAvPgogIDxwYXRoIGQ9Im0xMyAxMyA2IDYiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/mouse-pointer\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst MousePointer = createLucideIcon('MousePointer', [\n  ['path', { d: 'm3 3 7.07 16.97 2.51-7.39 7.39-2.51L3 3z', key: 'y2ucgo' }],\n  ['path', { d: 'm13 13 6 6', key: '1nhxnf' }],\n]);\n\nexport default MousePointer;\n"], "mappings": ";;;;;AAaM,MAAAA,YAAA,GAAeC,gBAAA,CAAiB,cAAgB,GACpD,CAAC,MAAQ;EAAEC,CAAA,EAAG,0CAA4C;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzE,CAAC,MAAQ;EAAED,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC5C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}