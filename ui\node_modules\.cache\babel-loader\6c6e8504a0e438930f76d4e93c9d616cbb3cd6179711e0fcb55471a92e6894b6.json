{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst Bold = createLucideIcon(\"Bold\", [[\"path\", {\n  d: \"M14 12a4 4 0 0 0 0-8H6v8\",\n  key: \"v2sylx\"\n}], [\"path\", {\n  d: \"M15 20a4 4 0 0 0 0-8H6v8Z\",\n  key: \"1ef5ya\"\n}]]);\nexport { Bold as default };", "map": {"version": 3, "names": ["Bold", "createLucideIcon", "d", "key"], "sources": ["D:\\repos-personal\\repos\\openapi-to-mcp\\ui\\node_modules\\lucide-react\\src\\icons\\bold.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Bold\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTQgMTJhNCA0IDAgMCAwIDAtOEg2djgiIC8+CiAgPHBhdGggZD0iTTE1IDIwYTQgNCAwIDAgMCAwLThINnY4WiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/bold\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Bold = createLucideIcon('Bold', [\n  ['path', { d: 'M14 12a4 4 0 0 0 0-8H6v8', key: 'v2sylx' }],\n  ['path', { d: 'M15 20a4 4 0 0 0 0-8H6v8Z', key: '1ef5ya' }],\n]);\n\nexport default Bold;\n"], "mappings": ";;;;;AAaM,MAAAA,IAAA,GAAOC,gBAAA,CAAiB,MAAQ,GACpC,CAAC,MAAQ;EAAEC,CAAA,EAAG,0BAA4B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzD,CAAC,MAAQ;EAAED,CAAA,EAAG,2BAA6B;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC3D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}