{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst PcCase = createLucideIcon(\"PcCase\", [[\"rect\", {\n  width: \"14\",\n  height: \"20\",\n  x: \"5\",\n  y: \"2\",\n  rx: \"2\",\n  key: \"1uq1d7\"\n}], [\"path\", {\n  d: \"M15 14h.01\",\n  key: \"1kp3bh\"\n}], [\"path\", {\n  d: \"M9 6h6\",\n  key: \"dgm16u\"\n}], [\"path\", {\n  d: \"M9 10h6\",\n  key: \"9gxzsh\"\n}]]);\nexport { PcCase as default };", "map": {"version": 3, "names": ["PcCase", "createLucideIcon", "width", "height", "x", "y", "rx", "key", "d"], "sources": ["D:\\repos-personal\\repos\\openapi-to-mcp\\ui\\node_modules\\lucide-react\\src\\icons\\pc-case.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name PcCase\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMTQiIGhlaWdodD0iMjAiIHg9IjUiIHk9IjIiIHJ4PSIyIiAvPgogIDxwYXRoIGQ9Ik0xNSAxNGguMDEiIC8+CiAgPHBhdGggZD0iTTkgNmg2IiAvPgogIDxwYXRoIGQ9Ik05IDEwaDYiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/pc-case\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst PcCase = createLucideIcon('PcCase', [\n  [\n    'rect',\n    { width: '14', height: '20', x: '5', y: '2', rx: '2', key: '1uq1d7' },\n  ],\n  ['path', { d: 'M15 14h.01', key: '1kp3bh' }],\n  ['path', { d: 'M9 6h6', key: 'dgm16u' }],\n  ['path', { d: 'M9 10h6', key: '9gxzsh' }],\n]);\n\nexport default PcCase;\n"], "mappings": ";;;;;AAaM,MAAAA,MAAA,GAASC,gBAAA,CAAiB,QAAU,GACxC,CACE,QACA;EAAEC,KAAA,EAAO,IAAM;EAAAC,MAAA,EAAQ,IAAM;EAAAC,CAAA,EAAG,GAAK;EAAAC,CAAA,EAAG,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,GAAA,EAAK;AAAS,EACtE,EACA,CAAC,MAAQ;EAAEC,CAAA,EAAG,YAAc;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,MAAQ;EAAEC,CAAA,EAAG,QAAU;EAAAD,GAAA,EAAK;AAAA,CAAU,GACvC,CAAC,MAAQ;EAAEC,CAAA,EAAG,SAAW;EAAAD,GAAA,EAAK;AAAA,CAAU,EACzC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}