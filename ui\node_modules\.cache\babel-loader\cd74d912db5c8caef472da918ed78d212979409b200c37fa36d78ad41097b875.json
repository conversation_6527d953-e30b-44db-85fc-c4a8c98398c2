{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst BarChartHorizontalBig = createLucideIcon(\"BarChartHorizontalBig\", [[\"path\", {\n  d: \"M3 3v18h18\",\n  key: \"1s2lah\"\n}], [\"rect\", {\n  width: \"12\",\n  height: \"4\",\n  x: \"7\",\n  y: \"5\",\n  rx: \"1\",\n  key: \"936jl1\"\n}], [\"rect\", {\n  width: \"7\",\n  height: \"4\",\n  x: \"7\",\n  y: \"13\",\n  rx: \"1\",\n  key: \"jqfkpy\"\n}]]);\nexport { BarChartHorizontalBig as default };", "map": {"version": 3, "names": ["BarChartHorizontalBig", "createLucideIcon", "d", "key", "width", "height", "x", "y", "rx"], "sources": ["D:\\repos-personal\\repos\\openapi-to-mcp\\ui\\node_modules\\lucide-react\\src\\icons\\bar-chart-horizontal-big.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name BarChartHorizontalBig\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMyAzdjE4aDE4IiAvPgogIDxyZWN0IHdpZHRoPSIxMiIgaGVpZ2h0PSI0IiB4PSI3IiB5PSI1IiByeD0iMSIgLz4KICA8cmVjdCB3aWR0aD0iNyIgaGVpZ2h0PSI0IiB4PSI3IiB5PSIxMyIgcng9IjEiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/bar-chart-horizontal-big\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst BarChartHorizontalBig = createLucideIcon('BarChartHorizontalBig', [\n  ['path', { d: 'M3 3v18h18', key: '1s2lah' }],\n  [\n    'rect',\n    { width: '12', height: '4', x: '7', y: '5', rx: '1', key: '936jl1' },\n  ],\n  [\n    'rect',\n    { width: '7', height: '4', x: '7', y: '13', rx: '1', key: 'jqfkpy' },\n  ],\n]);\n\nexport default BarChartHorizontalBig;\n"], "mappings": ";;;;;AAaM,MAAAA,qBAAA,GAAwBC,gBAAA,CAAiB,uBAAyB,GACtE,CAAC,MAAQ;EAAEC,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3C,CACE,QACA;EAAEC,KAAA,EAAO,IAAM;EAAAC,MAAA,EAAQ,GAAK;EAAAC,CAAA,EAAG,GAAK;EAAAC,CAAA,EAAG,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAL,GAAA,EAAK;AAAS,EACrE,EACA,CACE,QACA;EAAEC,KAAA,EAAO,GAAK;EAAAC,MAAA,EAAQ,GAAK;EAAAC,CAAA,EAAG,GAAK;EAAAC,CAAA,EAAG,IAAM;EAAAC,EAAA,EAAI,GAAK;EAAAL,GAAA,EAAK;AAAS,EACrE,CACD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}