{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst ArrowDownFromLine = createLucideIcon(\"ArrowDownFromLine\", [[\"path\", {\n  d: \"M19 3H5\",\n  key: \"1236rx\"\n}], [\"path\", {\n  d: \"M12 21V7\",\n  key: \"gj6g52\"\n}], [\"path\", {\n  d: \"m6 15 6 6 6-6\",\n  key: \"h15q88\"\n}]]);\nexport { ArrowDownFromLine as default };", "map": {"version": 3, "names": ["ArrowDownFromLine", "createLucideIcon", "d", "key"], "sources": ["D:\\repos-personal\\repos\\openapi-to-mcp\\ui\\node_modules\\lucide-react\\src\\icons\\arrow-down-from-line.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name ArrowDownFromLine\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTkgM0g1IiAvPgogIDxwYXRoIGQ9Ik0xMiAyMVY3IiAvPgogIDxwYXRoIGQ9Im02IDE1IDYgNiA2LTYiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/arrow-down-from-line\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ArrowDownFromLine = createLucideIcon('ArrowDownFromLine', [\n  ['path', { d: 'M19 3H5', key: '1236rx' }],\n  ['path', { d: 'M12 21V7', key: 'gj6g52' }],\n  ['path', { d: 'm6 15 6 6 6-6', key: 'h15q88' }],\n]);\n\nexport default ArrowDownFromLine;\n"], "mappings": ";;;;;AAaM,MAAAA,iBAAA,GAAoBC,gBAAA,CAAiB,mBAAqB,GAC9D,CAAC,MAAQ;EAAEC,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,eAAiB;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC/C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}